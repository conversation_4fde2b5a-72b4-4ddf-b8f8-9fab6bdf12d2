<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.nation.BasicDownloadMapper">

	<delete id="deleteNationProductControl">
		delete from nation_product_control where pk_tradecigarette in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK_TRADECIGARETTE}
		</foreach>
	</delete>

	<insert id="insertNationProductControl">
		insert into nation_product_control(
		pk_tradecigarette
		,pk_tradeproduct
		,year
		,org_code
		,org_name
		,mark_org_code
		,mark_org_name
		,cigarette_code
		,cigarette_name
		,brand_code
		,brand_name
		,memory_code
		,gjj_code
		,region_code
		,region_name
		,box_code
		,bar_code
		,package_code
		,cgt_type_code
		,cgt_type_name
		,cgttotal_length
		,cgt_length
		,cgtfilter_length
		,cgt_girth
		,cgt_tarcontent
		,cgt_nicotinic
		,cgt_packtype_code
		,branch_qty
		,is_import
		,is_cigar
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK_TRADECIGARETTE}
			,#{d.PK_TRADEPRODUCT,jdbcType=VARCHAR}
			,#{d.YEAR,jdbcType=CHAR}
			,#{d.ORG_CODE,jdbcType=VARCHAR}
			,#{d.ORG_NAME,jdbcType=VARCHAR}
			,#{d.MARK_ORG_CODE,jdbcType=VARCHAR}
			,#{d.MARK_ORG_NAME,jdbcType=VARCHAR}
			,#{d.CIGARETTE_CODE,jdbcType=VARCHAR}
			,#{d.CIGARETTE_NAME,jdbcType=VARCHAR}
			,#{d.BRAND_CODE,jdbcType=VARCHAR}
			,#{d.BRAND_NAME,jdbcType=VARCHAR}
			,#{d.MEMORY_CODE,jdbcType=VARCHAR}
			,#{d.GJJ_CODE,jdbcType=VARCHAR}
			,#{d.REGION_CODE,jdbcType=VARCHAR}
			,#{d.REGION_NAME,jdbcType=VARCHAR}
			,#{d.BOX_CODE,jdbcType=VARCHAR}
			,#{d.BAR_CODE,jdbcType=VARCHAR}
			,#{d.PACKAGE_CODE,jdbcType=VARCHAR}
			,#{d.CGT_TYPE_CODE,jdbcType=VARCHAR}
			,#{d.CGT_TYPE_NAME,jdbcType=VARCHAR}
			,#{d.CGTTOTAL_LENGTH,jdbcType=DECIMAL}
			,#{d.CGT_LENGTH,jdbcType=DECIMAL}
			,#{d.CGTFILTER_LENGTH,jdbcType=DECIMAL}
			,#{d.CGT_GIRTH,jdbcType=DECIMAL}
			,#{d.CGT_TARCONTENT,jdbcType=DECIMAL}
			,#{d.CGT_NICOTINIC,jdbcType=DECIMAL}
			,#{d.CGT_PACKTYPE_CODE,jdbcType=VARCHAR}
			,#{d.BRANCH_QTY,jdbcType=DECIMAL}
			,#{d.IS_IMPORT,jdbcType=CHAR}
			,#{d.IS_CIGAR,jdbcType=CHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationProductControlPrice">
		delete from nation_product_control_price where pk_tradecigarette in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK_TRADECIGARETTE}
		</foreach>
	</delete>

	<insert id="insertNationProductControlPrice">
		insert into nation_product_control_price(
		pk_tradecigarette
		,pk_tradeproduct
		,year
		,org_code
		,org_name
		,mark_org_code
		,mark_org_name
		,cigarette_code
		,cigarette_name
		,brand_code
		,brand_name
		,memory_code
		,gjj_code
		,region_code
		,region_name
		,box_code
		,bar_code
		,package_code
		,cgt_type_code
		,cgt_type_name
		,cgttotal_length
		,cgt_length
		,cgtfilter_length
		,cgt_girth
		,cgt_tarcontent
		,cgt_nicotinic
		,cgt_packtype_code
		,branch_qty
		,is_import
		,is_cigar
		,sale_price
		,trans_price
		,trans_w_price
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK_TRADECIGARETTE}
			,#{d.PK_TRADEPRODUCT,jdbcType=VARCHAR}
			,#{d.YEAR,jdbcType=CHAR}
			,#{d.ORG_CODE,jdbcType=VARCHAR}
			,#{d.ORG_NAME,jdbcType=VARCHAR}
			,#{d.MARK_ORG_CODE,jdbcType=VARCHAR}
			,#{d.MARK_ORG_NAME,jdbcType=VARCHAR}
			,#{d.CIGARETTE_CODE,jdbcType=VARCHAR}
			,#{d.CIGARETTE_NAME,jdbcType=VARCHAR}
			,#{d.BRAND_CODE,jdbcType=VARCHAR}
			,#{d.BRAND_NAME,jdbcType=VARCHAR}
			,#{d.MEMORY_CODE,jdbcType=VARCHAR}
			,#{d.GJJ_CODE,jdbcType=VARCHAR}
			,#{d.REGION_CODE,jdbcType=VARCHAR}
			,#{d.REGION_NAME,jdbcType=VARCHAR}
			,#{d.BOX_CODE,jdbcType=VARCHAR}
			,#{d.BAR_CODE,jdbcType=VARCHAR}
			,#{d.PACKAGE_CODE,jdbcType=VARCHAR}
			,#{d.CGT_TYPE_CODE,jdbcType=VARCHAR}
			,#{d.CGT_TYPE_NAME,jdbcType=VARCHAR}
			,#{d.CGTTOTAL_LENGTH,jdbcType=DECIMAL}
			,#{d.CGT_LENGTH,jdbcType=DECIMAL}
			,#{d.CGTFILTER_LENGTH,jdbcType=DECIMAL}
			,#{d.CGT_GIRTH,jdbcType=DECIMAL}
			,#{d.CGT_TARCONTENT,jdbcType=DECIMAL}
			,#{d.CGT_NICOTINIC,jdbcType=DECIMAL}
			,#{d.CGT_PACKTYPE_CODE,jdbcType=VARCHAR}
			,#{d.BRANCH_QTY,jdbcType=DECIMAL}
			,#{d.IS_IMPORT,jdbcType=CHAR}
			,#{d.IS_CIGAR,jdbcType=CHAR}
			,#{d.SALE_PRICE,jdbcType=DECIMAL}
			,#{d.TRANS_PRICE,jdbcType=DECIMAL}
			,#{d.TRANS_W_PRICE,jdbcType=DECIMAL}
			)
		</foreach>
	</insert>

	<delete id="deleteNationProduct">
		delete from nation_product where pk_tradeproduct in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK_TRADEPRODUCT}
		</foreach>
	</delete>

	<insert id="insertNationProduct">
		insert into nation_product(
			pk_tradeproduct
			,org_code
			,org_name
			,mark_org_code
			,mark_org_name
			,cigarette_code
			,cigarette_name
			,brand_code
			,brand_name
			,memory_code
			,gjj_code
			,region_code
			,region_name
			,box_code
			,bar_code
			,package_code
			,cgt_type_code
			,cgt_type_name
			,cgttotal_length
			,cgt_length
			,cgtfilter_length
			,cgt_girth
			,cgt_tarcontent
			,cgt_nicotinic
			,cgt_packtype_code
			,branch_qty
			,is_import
			,is_cigar
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK_TRADEPRODUCT,jdbcType=VARCHAR}
			,#{d.ORG_CODE,jdbcType=VARCHAR}
			,#{d.ORG_NAME,jdbcType=VARCHAR}
			,#{d.MARK_ORG_CODE,jdbcType=VARCHAR}
			,#{d.MARK_ORG_NAME,jdbcType=VARCHAR}
			,#{d.CIGARETTE_CODE,jdbcType=VARCHAR}
			,#{d.CIGARETTE_NAME,jdbcType=VARCHAR}
			,#{d.BRAND_CODE,jdbcType=VARCHAR}
			,#{d.BRAND_NAME,jdbcType=VARCHAR}
			,#{d.MEMORY_CODE,jdbcType=VARCHAR}
			,#{d.GJJ_CODE,jdbcType=VARCHAR}
			,#{d.REGION_CODE,jdbcType=VARCHAR}
			,#{d.REGION_NAME,jdbcType=VARCHAR}
			,#{d.BOX_CODE,jdbcType=VARCHAR}
			,#{d.BAR_CODE,jdbcType=VARCHAR}
			,#{d.PACKAGE_CODE,jdbcType=VARCHAR}
			,#{d.CGT_TYPE_CODE,jdbcType=VARCHAR}
			,#{d.CGT_TYPE_NAME,jdbcType=VARCHAR}
			,#{d.CGTTOTAL_LENGTH,jdbcType=DECIMAL}
			,#{d.CGT_LENGTH,jdbcType=DECIMAL}
			,#{d.CGTFILTER_LENGTH,jdbcType=DECIMAL}
			,#{d.CGT_GIRTH,jdbcType=DECIMAL}
			,#{d.CGT_TARCONTENT,jdbcType=DECIMAL}
			,#{d.CGT_NICOTINIC,jdbcType=DECIMAL}
			,#{d.CGT_PACKTYPE_CODE,jdbcType=VARCHAR}
			,#{d.BRANCH_QTY,jdbcType=DECIMAL}
			,#{d.IS_IMPORT,jdbcType=CHAR}
			,#{d.IS_CIGAR,jdbcType=CHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationMember">
		delete from nation_member where pk_member in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK_MEMBER}
		</foreach>
	</delete>

	<insert id="insertNationMember">
		insert into nation_member(
			pk_member
			,member_code
			,member_name
			,license_code
			,org_type
			,org_property
			,financial_psnname
			,financial_phone
			,address
			,telephone
			,postcode
			,deposit
			,istrade
			,parentmember_code
			,region_code
			,region_name
			,remark
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK_MEMBER,jdbcType=VARCHAR}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.LICENSE_CODE,jdbcType=VARCHAR}
			,#{d.ORG_TYPE,jdbcType=VARCHAR}
			,#{d.ORG_PROPERTY,jdbcType=VARCHAR}
			,#{d.FINANCIAL_PSNNAME,jdbcType=VARCHAR}
			,#{d.FINANCIAL_PHONE,jdbcType=VARCHAR}
			,#{d.ADDRESS,jdbcType=VARCHAR}
			,#{d.TELEPHONE,jdbcType=VARCHAR}
			,#{d.POSTCODE,jdbcType=VARCHAR}
			,#{d.DEPOSIT,jdbcType=DECIMAL}
			,#{d.ISTRADE,jdbcType=CHAR}
			,#{d.PARENTMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REGION_CODE,jdbcType=VARCHAR}
			,#{d.REGION_NAME,jdbcType=VARCHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationDeployee">
		delete from nation_employee where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDeployee">
		insert into nation_employee(
			pk
			,pk_member
			,member_code
			,member_name
			,employeecode
			,employeename
			,is_default
			,remark
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.PK_MEMBER,jdbcType=VARCHAR}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.EMPLOYEECODE,jdbcType=VARCHAR}
			,#{d.EMPLOYEENAME,jdbcType=VARCHAR}
			,#{d.IS_DEFAULT,jdbcType=CHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationWhse">
		delete from nation_whse where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationWhse">
		insert into nation_whse(
			pk
			,pk_member
			,member_code
			,member_name
			,warehousecode
			,warehousename
			,region_code
			,region_name
			,address
			,is_default
			,remark
			,is_seal
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.PK_MEMBER,jdbcType=VARCHAR}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.WAREHOUSECODE,jdbcType=VARCHAR}
			,#{d.WAREHOUSENAME,jdbcType=VARCHAR}
			,#{d.REGION_CODE,jdbcType=VARCHAR}
			,#{d.REGION_NAME,jdbcType=VARCHAR}
			,#{d.ADDRESS,jdbcType=VARCHAR}
			,#{d.IS_DEFAULT,jdbcType=CHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			,coalesce(#{d.IS_SEAL,jdbcType=VARCHAR},'N')
			)
		</foreach>
	</insert>

	<insert id="insertNationWhseFromNation">
		insert into ism_com_cont_reach_whse (
		    com_cont_reach_whse_id,
            com_cont_reach_whse_code,
		    com_code,
		    com_cont_reach_whse_nbitis_code,
            com_cont_reach_whse_name,
		    com_cont_reach_whse_addr,
		    is_default,
		    is_use,
            icom_code
        )
		select
		    left(uuid(), 32) as com_cont_reach_whse_id,
			warehousecode as com_cont_reach_whse_code,
			member_code as com_code,
			'' as com_cont_reach_whse_nbitis_code,
			warehousename as com_cont_reach_whse_name,
			address as com_cont_reach_whse_addr,
			'0' as is_default,
			'0' as is_use,
			#{icomCode} as icom_code
		from nation_whse
		where (warehousecode, member_code) not in (
		    select com_cont_reach_whse_code, com_code from ism_com_cont_reach_whse
			)
		and PK in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<delete id="deleteNationTax">
		delete from nation_tax where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationTax">
		insert into nation_tax(
			pk
			,pk_member
			,member_code
			,member_name
			,enterprise_name
			,taxpayer_no
			,is_default
			,remark
			)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.PK_MEMBER,jdbcType=VARCHAR}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.ENTERPRISE_NAME,jdbcType=VARCHAR}
			,#{d.TAXPAYER_NO,jdbcType=VARCHAR}
			,#{d.IS_DEFAULT,jdbcType=CHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationBank">
		delete from nation_bank where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationBank">
		insert into nation_bank(
			pk
			,pk_member
			,member_code
			,member_name
			,bank_account
			,bank_accname
			,is_default
			,bankdoc_name
			,remark
		)
		values
		<foreach collection="datas" item="d" separator=",">
		(
			#{d.PK}
			,#{d.PK_MEMBER,jdbcType=VARCHAR}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.BANK_ACCOUNT,jdbcType=VARCHAR}
			,#{d.BANK_ACCNAME,jdbcType=VARCHAR}
			,#{d.IS_DEFAULT,jdbcType=CHAR}
			,#{d.BANKDOC_NAME,jdbcType=VARCHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
		)
		</foreach>
	</insert>

	<delete id="deleteNationRatifyQty" >
		delete from nation_ratify_qty where cycle_code in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</delete>

	<insert id="insertNationRatifyQty">
		insert into nation_ratify_qty(
		cycle_code
		,member_code
		,member_name
		,planqty
		,protqty
		,contrqty
		,lowgrdqty
		,normalgrdqty
		,protlowgrdqty
		,protnormgrdqty
		,busi_type
		,deadline_time
		,inout_type
		)
		VALUES
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.CYCLE_CODE}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.PLANQTY,jdbcType=DECIMAL}
			,#{d.PROTQTY,jdbcType=DECIMAL}
			,#{d.CONTRQTY,jdbcType=DECIMAL}
			,#{d.LOWGRDQTY,jdbcType=DECIMAL}
			,#{d.NORMALGRDQTY,jdbcType=DECIMAL}
			,#{d.PROTLOWGRDQTY,jdbcType=DECIMAL}
			,#{d.PROTNORMGRDQTY,jdbcType=DECIMAL}
			,#{d.BUSI_TYPE,jdbcType=DECIMAL}
			,#{d.DEADLINE_TIME,jdbcType=DECIMAL}
			,#{d.INOUT_TYPE,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>


</mapper>