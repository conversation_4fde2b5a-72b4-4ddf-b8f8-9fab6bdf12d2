package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 网配订单日志表 视图层对象/值对象
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-08-18
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islmc_cgt_dist_order_log")
public class Mc04IslmcCgtDistOrderLogDO extends BaseFactor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 网配订单日志编号
     */
    @TableId(value = "mc04_cgt_dist_order_log_id", type = IdType.NONE)
    @Field(value = "网配订单日志编号", name = "网配订单日志编号")
    private String mc04CgtDistOrderLogId;

    /**
     * 网配订单代码
     */
    @Field(value = "网配订单代码", name = "网配订单代码")
    private String mc04CgtDistOrderCode;

    /**
     * 网配订单当前状态
     */
    @Field(value = "网配订单当前状态", name = "网配订单当前状态")
    private String mc04CgtDistOrderCurrentStatus;

    /**
     * 网配订单下一状态
     */
    @Field(value = "网配订单下一状态", name = "网配订单下一状态")
    private String mc04CgtDistOrderNextStatus;

    /**
     * 网配订单处理端,pc:电脑端,app:移动端
     */
    @Field(value = "网配订单处理端,pc:电脑端,app:移动端", name = "网配订单处理端,pc:电脑端,app:移动端")
    private String mc04CgtDistOrderClient;

}
