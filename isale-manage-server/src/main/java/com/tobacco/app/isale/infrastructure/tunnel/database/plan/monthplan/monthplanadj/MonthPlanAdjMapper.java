/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanadj;


import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdj;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: hzs
 * @Date: 2025/8/5
 * @Description:
 */
@Mapper
public interface MonthPlanAdjMapper {

    List<MonthPlanAdj> getComItemList(String planMonth, List<String> comIds);

    List<MonthPlanAdj> getCgtPlResolTotalTplanQtyList(String icomCode, String planMonth, List<String> baComOrgCodeList);

    /**
     * 查询地市状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @return
     */
    List<Map<String, Object>> queryOrgStatus(String ma02PlanMonth, String icomCode, String mc04MonthSalePlanStatus);

    /**
     * 查询地市该月份历史调整数量
     *
     * @param ma02PlanMonth
     * @param baComOrgCodeList
     * @return
     */
    List<Map<String, Object>> getAdjustedQtyByMonth(String ma02PlanMonth, List<String> baComOrgCodeList);

}
