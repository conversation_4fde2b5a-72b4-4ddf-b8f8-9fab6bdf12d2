/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.agreement.adjust;


import com.tobacco.app.isale.domain.model.adjust.AdjustApplyParam;
import com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> jinfuli
 * @Email : @inspur.com
 * @Create : 2025/05/13 15:25
 * @Description :
 */
@Mapper
public interface AgreementAdjustMapper {


    /**
     * 查询最近一次协议变更申请单
     *
     * @param param
     * @return
     */
    List<IslmcXyAdjustApply> queryLastApplyList(AdjustApplyParam param);

    /**
     * 已审核通过变更申请
     *
     * @param param
     * @return
     */
    List<IslmcXyAdjustApply> queryFnishedApplyList(AdjustApplyParam param);


    /**
     * 所有变更申请单
     *
     * @param param
     * @return
     */
    List<IslmcXyAdjustApply> queryAllApplyList(AdjustApplyParam param);

    /**
     * 查询除最近一次协议调整申请单的数据
     *
     * @param param
     * @return
     */
    List<IslmcXyAdjustApply> queryExcepLastApplyList(AdjustApplyParam param);

//    /**
//     * 查询导回的协议调整
//     *
//     * @param param
//     * @return
//     */
//    List<IslmcXyAdjust> queryAdjustList(Map<String, String> param);

    /**
     * 查询初始化协议调整申请单
     *
     * @return
     */
    //List<IslmcXyAdjustApply> queryInitApply(Map map);

    /**
     * 查询初始化协议调整申请单
     *
     * @return
     */
    List<IslmcXyAdjustApply> queryInitXyAdjustApply(AdjustApplyParam map);

    /**
     * 查询协议调整申请单
     *
     * @param param
     * @return
     */
    List<IslmcXyAdjustApply> queryApplyList(AdjustApplyParam param);
}
