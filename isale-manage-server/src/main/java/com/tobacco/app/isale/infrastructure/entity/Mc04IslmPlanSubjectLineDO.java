/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 年度销售调拨计划主题明细
 *
 * @Author: qintian
 * @Since: 2025-07-18
 * @Email: <EMAIL>
 * @Create: 2025-07-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islm_plan_subject_line")
public class Mc04IslmPlanSubjectLineDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 年度销售调拨计划主题明细编号
             */

            @TableId(value = "mc04_plan_subject_line_id", type = IdType.NONE)
    @Field(value = "年度销售调拨计划主题明细编号", name = "年度销售调拨计划主题明细编号")
    private String mc04PlanSubjectLineId;
                                /**
             * 年度销售调拨计划主题编号
             */

    @Field(value = "年度销售调拨计划主题编号", name = "年度销售调拨计划主题编号")
    private String mc04PlanSubjectId;
                                /**
             * 年度销售调拨计划子主题编号
             */

    @Field(value = "年度销售调拨计划子主题编号", name = "年度销售调拨计划子主题编号")
    private String mc04PlanChildSubjectCode;
                                /**
             * 年度销售调拨计划子主题名称
             */

    @Field(value = "年度销售调拨计划子主题名称", name = "年度销售调拨计划子主题名称")
    private String mc04PlanChildSubjectName;
                                /**
             * 年度销售调拨计划子主题填报开始日期
             */

    @Field(value = "年度销售调拨计划子主题填报开始日期", name = "年度销售调拨计划子主题填报开始日期")
    private String mc04PlanChildSubjectBeginDate;
                                /**
             * 年度销售调拨计划子主题填报结束日期
             */

    @Field(value = "年度销售调拨计划子主题填报结束日期", name = "年度销售调拨计划子主题填报结束日期")
    private String mc04PlanChildSubjectEndDate;
                                                                                                    

}
