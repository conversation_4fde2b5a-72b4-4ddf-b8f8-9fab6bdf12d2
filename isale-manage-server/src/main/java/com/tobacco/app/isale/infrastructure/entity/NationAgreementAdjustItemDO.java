/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 协议调整明细中间表
 *
 * @Author: renyonghui
 * @Since: 2025-05-07
 * @Email: <EMAIL>
 * @Create: 2025-05-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("nation_agreement_adjust_item")
@DataObject(name = "协议调整明细中间表", desc = "协议调整明细中间表")
public class NationAgreementAdjustItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 协议调整明细标识
             */

            @TableId(value = "row_pk", type = IdType.NONE)
    @Field(value = "协议调整明细标识", name = "协议调整明细标识")
    private String rowPk;
                                /**
             * 协议调整唯一标识
             */

    @Field(value = "协议调整唯一标识", name = "协议调整唯一标识")
    private String pk;
                                /**
             * 序号
             */

    @Field(value = "序号", name = "序号")
    private String crowno;
                                /**
             * 交易目录唯一标识
             */

    @Field(value = "交易目录唯一标识", name = "交易目录唯一标识")
    private String pkTradecigarette;
                                /**
             * 省际省内标志：0=省内,1=省际
             */

    @Field(value = "省际省内标志：0=省内,1=省际", name = "省际省内标志：0=省内,1=省际")
    private String provType;
                                /**
             * 产品编码
             */

    @Field(value = "产品编码", name = "产品编码")
    private String productCode;
                                /**
             * 产品名称
             */

    @Field(value = "产品名称", name = "产品名称")
    private String productName;
                                /**
             * 数量
             */

    @Field(value = "数量", name = "数量")
    private BigDecimal qty;
                                /**
             * 单价
             */

    @Field(value = "单价", name = "单价")
    private BigDecimal price;
                                /**
             * 供方编码
             */

    @Field(value = "供方编码", name = "供方编码")
    private String supmemberCode;
                                /**
             * 供方名称
             */

    @Field(value = "供方名称", name = "供方名称")
    private String supmemberName;
                                /**
             * 供方省份
             */

    @Field(value = "供方省份", name = "供方省份")
    private String supregion;
                                /**
             * 规格价位 一类二类
             */

    @Field(value = "规格价位 一类二类", name = "规格价位 一类二类")
    private String cgtprtypename;
                                /**
             * 烟支长度mm
             */

    @Field(value = "烟支长度mm", name = "烟支长度mm")
    private String cgtlength;
                                /**
             * 规格类型名称 一类二类
             */

    @Field(value = "规格类型名称 一类二类", name = "规格类型名称 一类二类")
    private String cgttypename;
                                /**
             * 包装类型名称
             */

    @Field(value = "包装类型名称", name = "包装类型名称")
    private String cgtpacktypename;
                                /**
             * 规格产地
             */

    @Field(value = "规格产地", name = "规格产地")
    private String regionname;
                                /**
             * 牌号
             */

    @Field(value = "牌号", name = "牌号")
    private String brandName;
                                                                                                                    /**
             * 排序号
             */

    @Field(value = "排序号", name = "排序号")
    private Integer seq;
                

}
