/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 卷烟半年协议变更申请明细
 *
 * @Author: jinfuli
 * @Since: 2025-05-12
 * @Email: <EMAIL>
 * @Create: 2025-05-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_xy_adjust_apply_item")
@DataObject(name = "卷烟半年协议变更申请明细", desc = "卷烟半年协议变更申请明细")
public class Mc04IslmcXyAdjustApplyItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 卷烟半年协议调整申请单明细编号
             */

            @TableId(value = "mc04_xy_adjust_item_id", type = IdType.NONE)
    @Field(value = "卷烟半年协议调整申请单明细编号", name = "卷烟半年协议调整申请单明细编号")
    private String mc04XyAdjustItemId;
                                /**
             * 卷烟半年协议调整单编号
             */

    @Field(value = "卷烟半年协议调整单编号", name = "卷烟半年协议调整单编号")
    private String mc04XyAdjustId;
                                /**
             * 卷烟代码（条）
             */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
                                /**
             * 卷烟名称
             */

    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;
                                /**
             * 卷烟二级牌号编码
             */

    @Field(value = "卷烟二级牌号编码", name = "卷烟二级牌号编码")
    private String acTwoLevelCigCode;
                                /**
             * 卷烟二级牌号名称
             */

    @Field(value = "卷烟二级牌号名称", name = "卷烟二级牌号名称")
    private String acTwoLevelCigName;
                                /**
             * 国产烟万支不含税调拨价
             */

    @Field(value = "国产烟万支不含税调拨价", name = "国产烟万支不含税调拨价")
    private BigDecimal acCgtTenThousandNoTaxAllotPrice;
                                /**
             * 卷烟半年协议变更销区提报量
             */

    @Field(value = "卷烟半年协议变更销区提报量", name = "卷烟半年协议变更销区提报量")
    private BigDecimal mc04XyAdjustReportQty;
                                /**
             * 卷烟半年协议变更区域确认量
             */

    @Field(value = "卷烟半年协议变更区域确认量", name = "卷烟半年协议变更区域确认量")
    private BigDecimal mc04XyAdjustRegionConfirmQty;
                                /**
             * 卷烟半年协议变更总监审核量
             */

    @Field(value = "卷烟半年协议变更总监审核量", name = "卷烟半年协议变更总监审核量")
    private BigDecimal mc04XyAdjustDirectorAuditQty;
                                /**
             * 卷烟半年协议变更品牌科审核量
             */

    @Field(value = "卷烟半年协议变更品牌科审核量", name = "卷烟半年协议变更品牌科审核量")
    private BigDecimal mc04XyAdjustBrandAuditQty;
                                /**
             * 卷烟半年协议变更业务科审核量
             */

    @Field(value = "卷烟半年协议变更业务科审核量", name = "卷烟半年协议变更业务科审核量")
    private BigDecimal mc04XyAdjustBusiAuditQty;
                                /**
             * 卷烟半年协议变更计划科确认量
             */

    @Field(value = "卷烟半年协议变更计划科确认量", name = "卷烟半年协议变更计划科确认量")
    private BigDecimal mc04XyAdjustPlanConfirmQty;
                                /**
             * 卷烟半年协议变更中心领导审核量
             */

    @Field(value = "卷烟半年协议变更中心领导审核量", name = "卷烟半年协议变更中心领导审核量")
    private BigDecimal mc04XyAdjustLeaderAuditQty;
                                /**
             * 卷烟协议本次调整量
             */

    @Field(value = "卷烟协议本次调整量", name = "卷烟协议本次调整量")
    private BigDecimal md02CgtXyAdjustQty;
                                /**
             * 备注
             */

    @Field(value = "备注", name = "备注")
    private String zaRemark;
                

}
