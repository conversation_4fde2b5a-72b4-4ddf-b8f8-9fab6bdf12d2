/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.weekplan.weekplanadjust;

import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.IslmcWeekPlanItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/8/28
 * @description 描述
 */
public interface IslmWeekPlanAdjustMapper {
    /**
     * 查询周计划调整列表
     *
     * @param planMonth        计划月
     * @param authorizedComIds 授权公司列表
     * @return 周计划调整列表
     */
    List<IslmcWeekPlanItem> findAdjustPlansByCondition(@Param("planMonth") String planMonth, @Param("authorizedComIds") List<String> authorizedComIds, @Param("week") List<String> week);

/**
     * 查询周列表
     *
     * @param planMonth 计划月
     * @return 周列表
     */
    List<String> findWeekPlanAdjustByCondition(@Param("planMonth") String planMonth);

    /**
     * 获取解锁状态
     *
     * @param planMonth       计划月
     * @param authorizedComIds 授权公司列表
     * @return 全国计划调整列表
     */
    Map<String, Boolean> getUnlockStatus(@Param("PlanMonth") String planMonth, @Param("authorizedComIds") List<String> authorizedComIds);


    /**
     * 获取每个二级卷烟规格的全国计划总量（所有月份）
     *
     * @param cigCodes 二级卷烟规格代码
     * @return 全国计划调整列表
     */
    List<Map<String, Object>> getNationalPlanTotals(@Param("cigCodes") List<String> cigCodes);
}