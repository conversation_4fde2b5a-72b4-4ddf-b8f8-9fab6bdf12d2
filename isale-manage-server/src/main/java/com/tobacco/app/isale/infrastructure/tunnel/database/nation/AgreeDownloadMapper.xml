<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.nation.AgreeDownloadMapper">

	<delete id="deleteNationAgreement" >
		delete from nation_agreement where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationAgreement" >
		insert into nation_agreement(
		pk
		,protocolno
		,supmember_code
		,supmember_name
		,supregion
		,reqmember_code
		,reqmember_name
		,reqregion
		,supdeputy_name
		,deputy_name
		,cycle_code
		,deli_start_date
		,deli_end_date
		,busi_type
		,audit_status
		,audit_time
		,audit_operat
		,cancel_time
		,order_date
		,finalqtotal
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.PROTOCOLNO,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQREGION,jdbcType=VARCHAR}
			,#{d.SUPDEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.DEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.CYCLE_CODE,jdbcType=VARCHAR}
			,#{d.DELI_START_DATE,jdbcType=CHAR}
			,#{d.DELI_END_DATE,jdbcType=CHAR}
			,#{d.BUSI_TYPE,jdbcType=VARCHAR}
			,#{d.AUDIT_STATUS,jdbcType=VARCHAR}
			,#{d.AUDIT_TIME,jdbcType=CHAR}
			,#{d.AUDIT_OPERAT,jdbcType=VARCHAR}
			,#{d.CANCEL_TIME,jdbcType=CHAR}
			,#{d.ORDER_DATE,jdbcType=CHAR}
			,#{d.FINALQTOTAL,jdbcType=DECIMAL}
			)
		</foreach>
	</insert>

	<delete id="deleteNationAgreementItem" >
		delete from nation_agreement_item where row_pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.ROW_PK}
		</foreach>
	</delete>

	<insert id="insertNationAgreementItem" >
		insert into nation_agreement_item(
		row_pk
		,pk
		,crowno
		,pk_tradecigarette
		,prov_type
		,product_code
		,product_name
		,qty
		,price
		,supmember_code
		,supmember_name
		,supregion
		,cgtprtypename
		,cgtlength
		,cgttypename
		,cgtpacktypename
		,regionname
		,brand_name
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.ROW_PK}
			,#{d.PK,jdbcType=VARCHAR}
			,#{d.CROWNO,jdbcType=VARCHAR}
			,#{d.PK_TRADECIGARETTE,jdbcType=VARCHAR}
			,#{d.PROV_TYPE,jdbcType=VARCHAR}
			,#{d.PRODUCT_CODE,jdbcType=VARCHAR}
			,#{d.PRODUCT_NAME,jdbcType=VARCHAR}
			,#{d.QTY,jdbcType=DECIMAL}
			,#{d.PRICE,jdbcType=DECIMAL}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.CGTPRTYPENAME,jdbcType=VARCHAR}
			,#{d.CGTLENGTH,jdbcType=VARCHAR}
			,#{d.CGTTYPENAME,jdbcType=VARCHAR}
			,#{d.CGTPACKTYPENAME,jdbcType=VARCHAR}
			,#{d.REGIONNAME,jdbcType=VARCHAR}
			,#{d.BRAND_NAME,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>


	<delete id="deleteIsmAgreement" >
		delete from mc04_islmc_xy where md02_cgt_xy_no in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
	</delete>

	<insert id="insertIsmAgreementFromNation" >
		insert into mc04_islmc_xy (mc04_cgt_xy_id,
		md02_cgt_xy_no,
		icom_code,
		ba_com_org_code,
		mc04_cgt_xy_period_code,
		ma02_toba_prod_trade_type_code,
		mc04_cgt_xy_status,
		mc04_cgt_xy_download_date)
		select left(uuid(), 32)                      as md02_cgt_xy_no,
		protocolno                               md02_cgt_xy_no,
		supmember_code                        as icom_code,
		reqmember_code                           ba_com_org_code,
		cycle_code                               mc04_cgt_xy_period_code,
		busi_type                             as ma02_toba_prod_trade_type_code,
		IF(cancel_time is not null, '9', '1') as mc04_cgt_xy_status,
		date_format(date(order_date), '%Y%m%d')  mc04_cgt_xy_download_date
		from nation_agreement
		where protocolno in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
	</insert>

	<delete id="deleteIsmAgreementItem" >
		delete from mc04_islmc_xy_item where mc04_cgt_xy_id in
		(
		select mc04_cgt_xy_id from mc04_islmc_xy where md02_cgt_xy_no in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
		)
	</delete>

	<insert id="insertIsmAgreementItemFromNation" >
		insert into mc04_islmc_xy_item(mc04_cgt_xy_item_id,
		mc04_cgt_xy_id,
		icom_code,
		ac_cgt_carton_code,
		ac_cgt_name,
		ac_cgt_ten_thousand_no_tax_allot_price,
		md02_cgt_xy_original_qty,
		md02_cgt_xy_adjusted_qty)
		select left(uuid(), 32)                mc04_cgt_xy_item_id,
		a.mc04_cgt_xy_id             as mc04_cgt_xy_id,
		a.supmember_code                icom_code,
		a.product_code               as ac_cgt_carton_code,
		a.product_name               as ac_cgt_name,
		a.price                      as ac_cgt_ten_thousand_no_tax_allot_price,
		sum(agreement_item_init_qty) as md02_cgt_xy_original_qty,
		sum(agreement_qty)           as md02_cgt_xy_adjusted_qty
		from (select c.mc04_cgt_xy_id,
		b.product_code,
		b.product_name,
		b.price,
		a.supmember_code,
		qty agreement_item_init_qty,
		qty agreement_qty
		from nation_agreement a,
		nation_agreement_item b,
		mc04_islmc_xy c
		where a.pk = b.pk
		and a.protocolno = c.md02_cgt_xy_no
		and a.protocolno in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>

		and not exists (select 1
		from nation_agreement_cancel c
		where a.protocolno = c.protocolno)
		union all
		select c.mc04_cgt_xy_id,
		b.product_code,
		b.product_name,
		b.price,
		a.supmember_code,
		0       agreement_item_init_qty,
		adj_qty agreement_qty
		from nation_agreement_adjust a,
		nation_agreement_adjust_item b,
		mc04_islmc_xy c
		where a.pk = b.pk
		and a.protocolno in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
		) a
		group by a.mc04_cgt_xy_id,
		a.supmember_code,
		a.product_code
	</insert>

	<update id="updateIsmAgreementItemPriceFromNation" >
		update mc04_islmc_xy_item a,
		(select c.mc04_cgt_xy_id,
		a.agreement_no,
		b.product_code,
		b.price ac_cgt_ten_thousand_no_tax_allot_price
		from nation_agreement a,
		nation_agreement_item b,
		mc04_islmc_xy c
		where a.pk = b.pk
		and a.protocolno = c.md02_cgt_xy_no
		and a.protocolno in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
		) b
		set a.ac_cgt_ten_thousand_no_tax_allot_price = b.ac_cgt_ten_thousand_no_tax_allot_price
		where b.mc04_cgt_xy_id = a.mc04_cgt_xy_id
		and b.product_code = a.ac_cgt_carton_code
		and b.agreement_no in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
	</update>

	<delete id="deleteNationFinalAgree" >
		delete from nation_final_agree where protocolno in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</delete>

	<insert id="insertNationFinalAgree" >
		insert into nation_final_agree(
		protocolno,
		supmember_code,
		supmember_name,
		reqmember_code,
		reqmember_name,
		cycle_code,
		busi_type,
		product_code,
		product_name,
		qty,
		protqty,
		cgtprtypename,
		brand_name,
		is_low_price,
		deadline_time
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PROTOCOLNO},
			#{d.SUPMEMBER_CODE},
			#{d.SUPMEMBER_NAME},
			#{d.REQMEMBER_CODE},
			#{d.REQMEMBER_NAME},
			#{d.CYCLE_CODE},
			#{d.BUSI_TYPE},
			#{d.PRODUCT_CODE},
			#{d.PRODUCT_NAME},
			#{d.QTY},
			#{d.PROTQTY},
			#{d.CGTPRTYPENAME},
			#{d.BRAND_NAME},
			#{d.IS_LOW_PRICE},
			#{d.DEADLINE_TIME}
			)
		</foreach>
	</insert>






	<delete id="deleteNationAgreementAdjust" >
		delete from nation_agreement_adjust where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationAgreementAdjust" >
		insert into nation_agreement_adjust(
		pk
		,protocolno
		,supmember_code
		,supmember_name
		,supregion
		,reqmember_code
		,reqmember_name
		,reqregion
		,supdeputy_name
		,deputy_name
		,cycle_code
		,deli_start_date
		,deli_end_date
		,busi_type
		,prov_type
		,audit_status
		,audit_time
		,audit_operat
		,cancel_time
		,order_date
		,finalqtotal
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.PROTOCOLNO,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQREGION,jdbcType=VARCHAR}
			,#{d.SUPDEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.DEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.CYCLE_CODE,jdbcType=VARCHAR}
			,#{d.DELI_START_DATE,jdbcType=CHAR}
			,#{d.DELI_END_DATE,jdbcType=CHAR}
			,#{d.BUSI_TYPE,jdbcType=VARCHAR}
			,#{d.PROV_TYPE,jdbcType=VARCHAR}
			,#{d.AUDIT_STATUS,jdbcType=VARCHAR}
			,#{d.AUDIT_TIME,jdbcType=CHAR}
			,#{d.AUDIT_OPERAT,jdbcType=VARCHAR}
			,#{d.CANCEL_TIME,jdbcType=CHAR}
			,#{d.ORDER_DATE,jdbcType=CHAR}
			,#{d.FINALQTOTAL,jdbcType=DECIMAL}
			)
		</foreach>
	</insert>

	<delete id="deleteNationAgreementAdjustItem" >
		delete from nation_agreement_adjust_item where row_pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.ROW_PK}
		</foreach>
	</delete>

	<insert id="insertNationAgreementAdjustItem" >
		insert into nation_agreement_adjust_item(
		row_pk
		,pk
		,crowno
		,pk_tradecigarette
		,prov_type
		,product_code
		,product_name
		,adj_qty
		,price
		,supmember_code
		,supmember_name
		,supregion
		,cgtprtypename
		,cgtlength
		,cgttypename
		,cgtpacktypename
		,regionname
		,brand_name
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.ROW_PK}
			,#{d.PK,jdbcType=VARCHAR}
			,#{d.CROWNO,jdbcType=VARCHAR}
			,#{d.PK_TRADECIGARETTE,jdbcType=VARCHAR}
			,#{d.PROV_TYPE,jdbcType=VARCHAR}
			,#{d.PRODUCT_CODE,jdbcType=VARCHAR}
			,#{d.PRODUCT_NAME,jdbcType=VARCHAR}
			,#{d.ADJ_QTY,jdbcType=DECIMAL}
			,#{d.PRICE,jdbcType=DECIMAL}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.CGTPRTYPENAME,jdbcType=VARCHAR}
			,#{d.CGTLENGTH,jdbcType=VARCHAR}
			,#{d.CGTTYPENAME,jdbcType=VARCHAR}
			,#{d.CGTPACKTYPENAME,jdbcType=VARCHAR}
			,#{d.REGIONNAME,jdbcType=VARCHAR}
			,#{d.BRAND_NAME,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteIsmAgreementAdjust" >
		delete from mc04_islmc_xy_adjust where mc04_nation_xy_adjust_id in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertIsmAgreementAdjustFromNation" >
		insert into mc04_islmc_xy_adjust(mc04_xy_adjust_id,
		mc04_nation_xy_adjust_id,
		md02_cgt_xy_no,
		ba_com_org_code,
		mc04_cgt_xy_period_code,
		ma02_toba_prod_trade_type_code,
		mc04_cgt_xy_download_date)
		select left(uuid(), 32)                        mc04_xy_adjust_id,
		pk                                      mc04_nation_xy_adjust_id,
		protocolno                              md02_cgt_xy_no,
		reqmember_code                          ba_com_org_code,
		cycle_code                              mc04_cgt_xy_period_code,
		busi_type as                            ma02_toba_prod_trade_type_code,
		date_format(date(order_date), '%Y%m%d') mc04_cgt_xy_download_date
		from nation_agreement_adjust
		where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<delete id="deleteIsmAgreementAdjustItem" >
		delete from mc04_islmc_xy_adjust_item where mc04_xy_adjust_id in
		(
		select mc04_xy_adjust_id from mc04_islmc_xy_adjust where mc04_nation_xy_adjust_id in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
		)
	</delete>

	<insert id="insertIsmAgreementAdjustItemFromNation" >
		insert into mc04_islmc_xy_adjust_item(
		mc04_xy_adjust_item_id,
		mc04_xy_adjust_id,
		ac_cgt_carton_code,
		ac_cgt_name,
		ac_cgt_ten_thousand_no_tax_allot_price,
		md02_cgt_xy_adjust_qty
		)
		select
		left(uuid(), 32) mc04_xy_adjust_item_id,
		c.mc04_xy_adjust_id,
		b.product_code,
		b.product_name,
		b.price ac_cgt_ten_thousand_no_tax_allot_price,
		adj_qty md02_cgt_xy_adjust_qty
		from
		nation_agreement_adjust a,
		nation_agreement_adjust_item b,
		mc04_islmc_xy_adjust c
		where
		a.pk = b.pk
		and a.pk = c.mc04_nation_xy_adjust_id
		and a.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<delete id="deleteNationAgreementCancel" >
		delete from nation_agreement_cancel where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationAgreementCancel" >
		insert into nation_agreement_cancel(
		pk
		,protocolno
		,supmember_code
		,supmember_name
		,supregion
		,reqmember_code
		,reqmember_name
		,reqregion
		,supdeputy_name
		,deputy_name
		,cycle_code
		,deli_start_date
		,deli_end_date
		,busi_type
		,audit_status
		,audit_time
		,audit_operat
		,cancel_time
		,order_date
		,finalqtotal
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.PROTOCOLNO,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQREGION,jdbcType=VARCHAR}
			,#{d.SUPDEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.DEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.CYCLE_CODE,jdbcType=VARCHAR}
			,#{d.DELI_START_DATE,jdbcType=CHAR}
			,#{d.DELI_END_DATE,jdbcType=CHAR}
			,#{d.BUSI_TYPE,jdbcType=VARCHAR}
			,#{d.AUDIT_STATUS,jdbcType=VARCHAR}
			,#{d.AUDIT_TIME,jdbcType=CHAR}
			,#{d.AUDIT_OPERAT,jdbcType=VARCHAR}
			,#{d.CANCEL_TIME,jdbcType=CHAR}
			,#{d.ORDER_DATE,jdbcType=CHAR}
			,#{d.FINALQTOTAL,jdbcType=DECIMAL}
			)
		</foreach>
	</insert>

	<delete id="updateIsmAgreementCancel" >
		update mc04_islmc_xy set
		mc04_cgt_xy_status = '9'
		where md02_cgt_xy_no in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PROTOCOLNO}
		</foreach>
	</delete>

</mapper>