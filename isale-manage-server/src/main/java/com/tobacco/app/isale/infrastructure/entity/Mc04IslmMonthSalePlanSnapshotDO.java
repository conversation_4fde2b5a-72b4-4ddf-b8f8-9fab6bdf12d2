/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * 月计划上报快照表 视图层对象/值对象
 * </p>
 *
 * @Author: hzs
 * @Since: 2025-08-07
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islm_month_sale_plan_snapshot")
public class Mc04IslmMonthSalePlanSnapshotDO extends BaseFactor {

    private static final long serialVersionUID = 2020792876122133774L;

    /**
     * 月计划上报快照编号
     */
    @TableId(value = "mc04_month_sale_plan_snapshot_id", type = IdType.NONE)
    @Field(value = "月计划上报快照编号", name = "月计划上报快照编号")
    private String mc04MonthSalePlanSnapshotId;

    /**
     * 月计划上报数据编号
     */
    @Field(value = "月计划上报数据编号", name = "月计划上报数据编号")
    private String mc04MonthSalePlanId;

    /**
     * 烟草制品交易业务类型代码
     */
    @Field(value = "烟草制品交易业务类型代码", name = "烟草制品交易业务类型代码")
    private String ma02TobaProdTradeTypeCode;

    /**
     * 卷烟业务月份
     */
    @Field(value = "卷烟业务月份", name = "卷烟业务月份")
    private String zaOccurrenceMonth;

    /**
     * 计划月份
     */
    @Field(value = "计划月份", name = "计划月份")
    private String ma02PlanMonth;

    /**
     * 销售调拨计划版本
     */
    @Field(value = "销售调拨计划版本", name = "销售调拨计划版本")
    private String mc04CgtSalePlanVersion;

    /**
     * 调拨计划类型
     */
    @Field(value = "调拨计划类型", name = "调拨计划类型")
    private String mc04MonthSalePlanType;

    /**
     * 省级公司代码
     */
    @Field(value = "省级公司代码", name = "省级公司代码")
    private String baProvOrgCode;

    /**
     * 商业公司编码
     */
    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;

    /**
     * 卷烟协议周期
     */
    @Field(value = "卷烟协议周期", name = "卷烟协议周期")
    private String mc04CgtXyPeriodCode;

    /**
     * 上报数据的状态
     */
    @Field(value = "上报数据的状态", name = "上报数据的状态")
    private String mc04MonthSalePlanStatus;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;

    /**
     * 卷烟二级牌号编码
     */
    @Field(value = "卷烟二级牌号编码", name = "卷烟二级牌号编码")
    private String acTwoLevelCigCode;

    /**
     * 卷烟二级牌号名称
     */
    @Field(value = "卷烟二级牌号名称", name = "卷烟二级牌号名称")
    private String acTwoLevelCigName;

    /**
     * 卷烟价位段代码
     */
    @Field(value = "卷烟价位段代码", name = "卷烟价位段代码")
    private String acCgtPriceSegmentCode;

    /**
     * 卷烟价位段名称
     */
    @Field(value = "卷烟价位段名称", name = "卷烟价位段名称")
    private String acCgtPriceSegmentName;

    /**
     * 卷烟含税调拨价格
     */
    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟统一批发价
     */
    @Field(value = "卷烟统一批发价", name = "卷烟统一批发价")
    private BigDecimal acCgtTradePrice;

    /**
     * 卷烟商业期初库存量（万支）
     */
    @Field(value = "卷烟商业期初库存量（万支）", name = "卷烟商业期初库存量（万支）")
    @TableField("md03_cgt_10th_com_init_stk_qty")
    private BigDecimal md03Cgt10thComInitStkQty;

    /**
     * 卷烟销售计划销区上报量
     */
    @Field(value = "卷烟销售计划销区上报量", name = "卷烟销售计划销区上报量")
    private BigDecimal mc04CgtSalePlanReportQty;

    /**
     * 卷烟销售计划区域确认量
     */
    @Field(value = "卷烟销售计划区域确认量", name = "卷烟销售计划区域确认量")
    private BigDecimal mc04CgtSalePlanRegionConfirmQty;

    /**
     * 卷烟销售计划业务科确认量
     */
    @Field(value = "卷烟销售计划业务科确认量", name = "卷烟销售计划业务科确认量")
    private BigDecimal mc04CgtSalePlanBdConfirmQty;

    /**
     * 卷烟销售计划计划科确认量
     */
    @Field(value = "卷烟销售计划计划科确认量", name = "卷烟销售计划计划科确认量")
    private BigDecimal mc04CgtSalePlanPdConfirmQty;

    /**
     * 卷烟销售计划计划科发布量
     */
    @Field(value = "卷烟销售计划计划科发布量", name = "卷烟销售计划计划科发布量")
    private BigDecimal mc04CgtSalePlanPdPublishQty;

    /**
     * 卷烟销售计划调整后计划量
     */
    @Field(value = "卷烟销售计划调整后计划量", name = "卷烟销售计划调整后计划量")
    private BigDecimal mc04CgtSalePlanAdjustedQty;

    /**
     * 卷烟调拨计划系统推荐量
     */
    @Field(value = "卷烟调拨计划系统推荐量", name = "卷烟调拨计划系统推荐量")
    private BigDecimal mc04CgtAllotPlanRecomQty;

    /**
     * 卷烟调拨计划销区上报量
     */
    @Field(value = "卷烟调拨计划销区上报量", name = "卷烟调拨计划销区上报量")
    private BigDecimal mc04CgtAllotPlanReportQty;

    /**
     * 卷烟调拨计划区域确认量
     */
    @Field(value = "卷烟调拨计划区域确认量", name = "卷烟调拨计划区域确认量")
    private BigDecimal mc04CgtAllotPlanRegionConfirmQty;

    /**
     * 卷烟调拨计划业务科确认量
     */
    @Field(value = "卷烟调拨计划业务科确认量", name = "卷烟调拨计划业务科确认量")
    private BigDecimal mc04CgtAllotPlanBdConfirmQty;

    /**
     * 卷烟调拨计划计划科确认量
     */
    @Field(value = "卷烟调拨计划计划科确认量", name = "卷烟调拨计划计划科确认量")
    private BigDecimal mc04CgtAllotPlanPdConfirmQty;

    /**
     * 卷烟调拨计划计划科发布量
     */
    @Field(value = "卷烟调拨计划计划科发布量", name = "卷烟调拨计划计划科发布量")
    private BigDecimal mc04CgtAllotPlanPdPublishQty;

    /**
     * 卷烟调拨计划调整后计划量
     */
    @Field(value = "卷烟调拨计划调整后计划量", name = "卷烟调拨计划调整后计划量")
    private BigDecimal ma02CgtPlAdjustedQty;

    /**
     * 卷烟商业期末库存量（万支）
     */
    @Field(value = "卷烟商业期末库存量（万支）", name = "卷烟商业期末库存量（万支）")
    @TableField("md03_cgt_10th_com_end_stk_qty")
    private BigDecimal md03Cgt10thComEndStkQty;

    /**
     * 全年销售计划-目标量
     */
    @Field(value = "全年销售计划-目标量", name = "全年销售计划-目标量")
    private BigDecimal mc04CgtPlanYSaleQty;

    /**
     * 全年销售计划-实际销量
     */
    @Field(value = "全年销售计划-实际销量", name = "全年销售计划-实际销量")
    private BigDecimal mc04CgtPlanYSaleActualQty;

    /**
     * 全年销售计划-剩余销量
     */
    @Field(value = "全年销售计划-剩余销量", name = "全年销售计划-剩余销量")
    private BigDecimal mc04CgtPlanYSaleRemainQty;

    /**
     * 全年销售计划-收入目标
     */
    @Field(value = "全年销售计划-收入目标", name = "全年销售计划-收入目标")
    private BigDecimal mc04CgtPlanYSaleIncome;

    /**
     * 全年销售计划-实际收入
     */
    @Field(value = "全年销售计划-实际收入", name = "全年销售计划-实际收入")
    private BigDecimal mc04CgtPlanYSaleActualIncome;

    /**
     * 全年销售计划-剩余收入
     */
    @Field(value = "全年销售计划-剩余收入", name = "全年销售计划-剩余收入")
    private BigDecimal mc04CgtPlanYSaleRemainIncome;

    /**
     * 季度销售计划-当前季度目标量
     */
    @Field(value = "季度销售计划-当前季度目标量", name = "季度销售计划-当前季度目标量")
    private BigDecimal mc04CgtPlanQSaleQty;

    /**
     * 季度销售计划-当前季度实际销量
     */
    @Field(value = "季度销售计划-当前季度实际销量", name = "季度销售计划-当前季度实际销量")
    private BigDecimal mc04CgtPlanQSaleActualQty;

    /**
     * 季度销售计划-当前季度剩余销量
     */
    @Field(value = "季度销售计划-当前季度剩余销量", name = "季度销售计划-当前季度剩余销量")
    private BigDecimal mc04CgtPlanQSaleRemainQty;

    /**
     * N-1月末的实际库存含在途
     */
    @Field(value = "N-1月末的实际库存含在途", name = "N-1月末的实际库存含在途")
    @TableField("md03_cgt_10th_com_end_stk_qty_p")
    private BigDecimal md03Cgt10thComEndStkQtyP;

    /**
     * N月销售计划-计划量
     */
    @Field(value = "N月销售计划-计划量", name = "N月销售计划-计划量")
    private BigDecimal mc04CgtPlanMSaleQty;

    /**
     * N月销售计划-执行量
     */
    @Field(value = "N月销售计划-执行量", name = "N月销售计划-执行量")
    private BigDecimal mc04CgtPlanMSaleActualQty;

    /**
     * N月销售计划-剩余量
     */
    @Field(value = "N月销售计划-剩余量", name = "N月销售计划-剩余量")
    private BigDecimal mc04CgtPlanMSaleRemainQty;

    /**
     * N月调拨计划-发布量
     */
    @Field(value = "N月调拨计划-发布量", name = "N月调拨计划-发布量")
    private BigDecimal mc04CgtPlanMAllotQty;

    /**
     * N月调拨计划-剩余量
     */
    @Field(value = "N月调拨计划-剩余量", name = "N月调拨计划-剩余量")
    private BigDecimal mc04CgtPlanMAllotRemainQty;

    /**
     * 当前商业库存
     */
    @Field(value = "当前商业库存", name = "当前商业库存")
    private BigDecimal mc04CgtComInvtQty;

    /**
     * 备注
     */
    @Field(value = "备注", name = "备注")
    private String zaRemark;
}
