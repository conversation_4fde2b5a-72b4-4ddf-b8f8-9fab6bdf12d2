/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.plan.PlanQty;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 计划公共Mapper
 * <AUTHOR>
 */
@Mapper
public interface PlanCommonMapper {

    /**
     * 获取某年末所有卷烟规格的商业库存
     * @param year 年 如果是2024年末就传入 2024
     * @param busiComCodeList 商业公司代码列表
     */

    @DS("dws")
    List<PlanQty> getYearEndStkQty(String year, List<String> busiComCodeList);

}
