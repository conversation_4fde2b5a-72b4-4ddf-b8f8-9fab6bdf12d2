package com.tobacco.app.isale.infrastructure.tunnel.database.cont.whse;

import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhseItemTray;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface IslmcContDelivWhseItemTrayMapper {

    List<IslmcContDelivWhseItemTray> getList(
            @Param("md02CgtOutStorehouseCode") String md02CgtOutStorehouseCode
    );
}
