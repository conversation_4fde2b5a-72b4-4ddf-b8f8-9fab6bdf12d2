<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.order.IslmDistOrderMapper">
    <select id="queryDistOrderWithPage"
            resultType="com.tobacco.app.isale.domain.model.order.dist.order.DistOrder">
        SELECT
        a.ba_com_org_code AS baComOrgCode,
        a.md02_cgt_in_storehouse_code AS md02CgtInStorehouseCode,
        b.md02_cgt_in_storehouse_name as md02CgtInStorehouseName,
        a.ma02_plan_month AS ma02PlanMonth,
        a.mc04_month_sale_plan_type AS mc04MonthSalePlanType,
        a.mc04_date_period_code AS mc04DatePeriodCode,
        a.mc04_cont_expec_out_date AS mc04ContExpecOutDate,
        a.mc04_cont_expec_arrival_date as mc04ContExpecArrivalDate,
        a.mc04_cgt_dist_order_status AS mc04CgtDistOrderStatus,

        sum(a.md02_cgt_dist_confirm_qty)/(
        case when a.ma02_toba_prod_trade_type_code = '0'
            then 5
            else 1
            end
        ) as sumDistConfirmQty,
        a.mc04_cgt_dist_order_code as mc04CgtDistOrderCode,
        a.md02_cgt_dist_region_code as md02CgtDistRegionCode,
        a.md02_dist_receiveregion_code as md02DistReceiveregionCode,
        DATE_FORMAT(max(a.create_time), '%Y-%m-%d %H:%i:%s') AS createTime,
        a.create_name AS createName,
        c.md02_dist_receiveregion_name AS md02DistReceiveregionName,
        d.region_name AS md02CgtDistRegionName
        from mc04_islm_cgt_dist_order a
        left join mc04_islmc_cont_reach_whse b on a.md02_cgt_in_storehouse_code = b.md02_cgt_in_storehouse_code
        left join nation_dist_receive_region c on a.md02_dist_receiveregion_code = c.md02_dist_receiveregion_code
        left join nation_dist_region d on a.md02_cgt_dist_region_code = d.region_code
        where a.icom_code = #{param.icomCode}
        <if test="param.baComOrgCodes != null and param.baComOrgCodes.size() > 0">
            and a.ba_com_org_code in
            <foreach item="item" collection="param.baComOrgCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.mc04CgtDistOrderStatuses != null and param.mc04CgtDistOrderStatuses.size() > 0">
            and a.mc04_cgt_dist_order_status in
            <foreach item="item" collection="param.mc04CgtDistOrderStatuses" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.mc04ContExpecOutDateBegin != null and !''.equals(param.mc04ContExpecOutDateBegin)">
            and a.mc04_cont_expec_out_date >= #{param.mc04ContExpecOutDateBegin}
        </if>
        <if test="param.mc04ContExpecOutDateEnd != null and !''.equals(param.mc04ContExpecOutDateEnd)">
            and a.mc04_cont_expec_out_date &lt;= #{param.mc04ContExpecOutDateEnd}
        </if>
          <if test="param.mc04ContExpecArrivalDateBegin != null and !''.equals(param.mc04ContExpecArrivalDateBegin)">
            and a.mc04_cont_expec_arrival_date >= #{param.mc04ContExpecArrivalDateBegin}
        </if>
        <if test="param.mc04ContExpecArrivalDateEnd != null and !''.equals(param.mc04ContExpecArrivalDateEnd)">
            and a.mc04_cont_expec_arrival_date &lt;= #{param.mc04ContExpecArrivalDateEnd}
        </if>
        <if test="param.ma02TobaProdTradeTypeCode != null and !''.equals(param.ma02TobaProdTradeTypeCode)">
            and a.ma02_toba_prod_trade_type_code = #{param.ma02TobaProdTradeTypeCode}
        </if>
        <if test="param.mc04CgtDistOrderType != null and !''.equals(param.mc04CgtDistOrderType)">
            and a.mc04_cgt_dist_order_type = #{param.mc04CgtDistOrderType}
        </if>
        <if test="param.ma02PlanMonth != null and !''.equals(param.ma02PlanMonth)">
            and a.ma02_plan_month = #{param.ma02PlanMonth}
        </if>

        group by a.ba_com_org_code,a.mc04_cgt_dist_order_dist_date,a.mc04_cont_expec_out_date,a.mc04_cgt_dist_order_code,a.ma02_toba_prod_trade_type_code
        order by a.mc04_cont_expec_out_date,a.mc04_cont_expec_arrival_date
    </select>

    <select id="getDayPlanData" resultType="com.tobacco.app.isale.domain.model.order.dist.order.DistOrderCols">
        select
        coalesce( sum(ma02_cgt_pl_adjusted_qty),0) as executeQty,
        ac_two_level_cig_code as acTwoLevelCigCode,
        ac_cgt_carton_code as acCgtCartonCode
        from mc04_islmc_cgt_day_plan
        where ba_com_org_code = #{baComOrgCode}
        and ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
        and icom_code = #{icomCode}
        <if test="mc04CgtXyPeriodCode != null and !''.equals(mc04CgtXyPeriodCode)">
            and mc04_cgt_xy_period_code = #{mc04CgtXyPeriodCode}
        </if>
        <if test="ma02PlanMonth != null and !''.equals(ma02PlanMonth)">
            and ma02_plan_month = #{ma02PlanMonth}
        </if>
        <if test="mc04DatePeriodCode != null  and !''.equals(mc04DatePeriodCode)">
            and mc04_date_period_code = #{mc04DatePeriodCode}
        </if>
        group by ba_com_org_code, ac_two_level_cig_code, ac_cgt_carton_code
    </select>
    <select id="queryDistOrderList" resultType="com.tobacco.app.isale.domain.model.order.dist.order.DistOrder">
        SELECT
        a.mc04_cgt_dist_order_id AS mc04CgtDistOrderId,
        a.mc04_cgt_dist_order_code AS mc04CgtDistOrderCode,
        a.ba_com_org_code AS baComOrgCode,
        a.md02_cgt_dist_region_code AS md02CgtDistRegionCode,
        a.md02_dist_receiveregion_code AS md02DistReceiveregionCode,
        a.ma02_toba_prod_trade_type_code AS ma02TobaProdTradeTypeCode,
        a.mc04_cgt_dist_order_type AS mc04CgtDistOrderType,
        a.ac_cgt_carton_code AS acCgtCartonCode,
        a.ac_cgt_name AS acCgtName,
        a.ac_two_level_cig_code AS acTwoLevelCigCode,
        a.ac_two_level_cig_name AS acTwoLevelCigName,
        a.md02_cgt_out_storehouse_code AS md02CgtOutStorehouseCode,
        a.md02_cgt_in_storehouse_code AS md02CgtInStorehouseCode,
        a.mc04_cgt_pra_in_storehouse_code AS mc04CgtPraInStorehouseCode,
        a.ma02_plan_month AS ma02PlanMonth,
        a.mc04_month_sale_plan_type AS mc04MonthSalePlanType,
        a.mc04_date_period_code AS mc04DatePeriodCode,
        a.md02_cgt_xy_no AS md02CgtXyNo,
        a.mc04_cgt_xy_period_code AS mc04CgtXyPeriodCode,
        a.mc04_cgt_dist_order_dist_date AS mc04CgtDistOrderDistDate,
        a.mc04_cont_expec_out_date AS mc04ContExpecOutDate,
        a.mc04_cont_expec_arrival_date AS mc04ContExpecArrivalDate,
        a.md03_logt_ic_appt_begin AS md03LogtIcApptBegin,
        a.md03_logt_ic_appt_end AS md03LogtIcApptEnd,
        a.md02_cgt_dist_calc_dist_qty AS md02CgtDistCalcDistQty,
        a.mc04_cgt_dist_order_sale_area_req_qty AS mc04CgtDistOrderSaleAreaReqQty,
        a.mc04_cgt_dist_order_sale_area_audit_qty AS mc04CgtDistOrderSaleAreaAuditQty,
        a.mc04_cgt_dist_order_sale_center_audit_qty AS mc04CgtDistOrderSaleCenterAuditQty,
        a.md02_cgt_dist_reqconfirm_qty AS md02CgtDistReqconfirmQty,
        a.md02_cgt_dist_confirm_qty AS md02CgtDistConfirmQty,
        a.mc04_cgt_dist_order_status AS mc04CgtDistOrderStatus,
        a.za_remark AS zaRemark,
        a.md02_cgt_dist_indu_abnormal_remark AS md02CgtDistInduAbnormalRemark,
        a.mc04_cont_zero_clock_type AS mc04ContZeroClockType,
        a.mf04_approval_info AS mf04ApprovalInfo,
        a.md03_logt_tray_comb_tsp_tray_type AS md03LogtTrayCombTspTrayType,
        b.md02_cgt_in_storehouse_name as md02CgtInStorehouseName
        FROM mc04_islmc_cgt_dist_order a
        left join mc04_islmc_cont_reach_whse b on a.md02_cgt_in_storehouse_code = b.md02_cgt_in_storehouse_code
        where a.icom_code = #{icomCode}
        <if test="baComOrgCodes != null and baComOrgCodes.size() > 0">
            and a.ba_com_org_code in
            <foreach item="item" collection="baComOrgCodes" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="mc04CgtDistOrderCodes != null and mc04CgtDistOrderCodes.size() > 0">
            and a.mc04_cgt_dist_order_code in
            <foreach item="item" collection="mc04CgtDistOrderCodes" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="mc04CgtDistOrderStatuses != null and mc04CgtDistOrderStatuses.size() > 0">
            and a.mc04_cgt_dist_order_status in
            <foreach item="item" collection="mc04CgtDistOrderStatuses" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="mc04ContExpecOutDateBegin != null and !''.equals(mc04ContExpecOutDateBegin)">
            and a.mc04_cont_expec_out_date >= #{mc04ContExpecOutDateBegin}
        </if>
        <if test="mc04ContExpecOutDateEnd != null and !''.equals(mc04ContExpecOutDateEnd)">
            and a.mc04_cont_expec_out_date &lt;= #{mc04ContExpecOutDateEnd}
        </if>
        <if test="ma02TobaProdTradeTypeCode != null and !''.equals(ma02TobaProdTradeTypeCode)">
            and a.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
        </if>
    </select>

    <select id="getTwoLevelDayPlans" resultType="com.tobacco.app.isale.domain.model.order.dist.order.DistOrderDayPlan">
        select
            coalesce( sum(ma02_cgt_pl_adjusted_qty),0) as ma02_cgt_pl_adjusted_qty,
            ac_two_level_cig_code ,
            ac_cgt_carton_code
        from mc04_islmc_cgt_day_plan
        group by ac_two_level_cig_code, ac_cgt_carton_code
    </select>

</mapper>