<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.cont.whse.IslmcContDelivWhseMapper">

    <select id="page" resultType="com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhse">
        select
        a.*,
        b.ba_fact_shortname
        from
        mc04_islmc_cont_deliv_whse a left join mc04_islmc_factory b on a.ba_fact_org_code = b.ba_fact_org_code
        <where>
            <if test="icomCode != null and icomCode != ''">
                and a.icom_code = #{icomCode}
            </if>
            <if test="islmcContDelivWhse.md02CgtOutStorehouseCode != null and islmcContDelivWhse.md02CgtOutStorehouseCode != ''">
                and a.md02_cgt_out_storehouse_code like concat('%',#{islmcContDelivWhse.md02CgtOutStorehouseCode},'%')
            </if>
            <if test="islmcContDelivWhse.md02CgtOutStorehouseName != null and islmcContDelivWhse.md02CgtOutStorehouseName != ''">
                and a.md02_cgt_out_storehouse_name like concat('%',#{islmcContDelivWhse.md02CgtOutStorehouseName},'%')
            </if>
            <if test="islmcContDelivWhse.isUse != null and islmcContDelivWhse.isUse != ''">
                and a.is_use = #{islmcContDelivWhse.isUse}
            </if>
        </where>
    </select>
</mapper>