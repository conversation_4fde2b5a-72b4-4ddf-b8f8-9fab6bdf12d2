package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 调拨计划变更主表 视图层对象/值对象
 * </p>
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islm_cgt_tran_demand_adj")
public class Mc04IslmCgtTranDemandAdjDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 月调拨计划调整单编号 使用流水号，Y+年份后2位+计划月份后两位+3位序号
     */
    @TableId(value = "mc04_month_sale_plan_adj_id", type = IdType.NONE)
    @Field(value = "月调拨计划调整单编号 使用流水号，Y+年份后2位+计划月份后两位+3位序号", name = "月调拨计划调整单编号 使用流水号，Y+年份后2位+计划月份后两位+3位序号")
    private String mc04MonthSalePlanAdjId;

    /**
     * 烟草制品交易业务类型代码 0:国产卷烟,1:国产雪茄烟
     */
    @Field(value = "烟草制品交易业务类型代码 0:国产卷烟,1:国产雪茄烟", name = "烟草制品交易业务类型代码 0:国产卷烟,1:国产雪茄烟")
    private String ma02TobaProdTradeTypeCode;

    /**
     * 计划月份
     */
    @Field(value = "计划月份", name = "计划月份")
    private String ma02PlanMonth;

    /**
     * 卷烟调拨需求计划变更日期
     */
    @Field(value = "卷烟调拨需求计划变更日期", name = "卷烟调拨需求计划变更日期")
    private String mc04CgtTranDemandAdjDate;

    /**
     * 卷烟调拨需求计划订单类型 10:N+3调拨计划
     */
    @Field(value = "卷烟调拨需求计划订单类型 10:N+3调拨计划", name = "卷烟调拨需求计划订单类型 10:N+3调拨计划")
    private String mc04CgtTranDemandOrderType;

    /**
     * 卷烟生产计划类型 01:生产计划,02:预生产计划,03:备料生产计划 N+1月为生产计划,N+2月为预计划,N+3月为备料计划
     */
    @Field(value = "卷烟生产计划类型 01:生产计划,02:预生产计划,03:备料生产计划 N+1月为生产计划,N+2月为预计划,N+3月为备料计划", name = "卷烟生产计划类型 01:生产计划,02:预生产计划,03:备料生产计划 N+1月为生产计划,N+2月为预计划,N+3月为备料计划")
    private String mc03CgtProdPlType;

    /**
     * 卷烟调拨需求变更状态 10:草稿,20:提交,30:审核,40:递交
     */
    @Field(value = "卷烟调拨需求变更状态 10:草稿,20:提交,30:审核,40:递交", name = "卷烟调拨需求变更状态 10:草稿,20:提交,30:审核,40:递交")
    private String mc04CgtTranDemandAdjStatus;

    /**
     * 单据日期  单据提交日期
     */
    @Field(value = "单据日期  单据提交日期", name = "单据日期  单据提交日期")
    private String zaBillDate;

    /**
     * 删除标识 0:否,1:是 删除操作更新此字段,进行逻辑删除
     */
    @Field(value = "删除标识 0:否,1:是 删除操作更新此字段,进行逻辑删除", name = "删除标识 0:否,1:是 删除操作更新此字段,进行逻辑删除")
    private String isDelete;

    /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

    /**
     * 创建人ID
     */
    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

    /**
     * 创建人名称
     */
    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @Field(value = "创建时间", name = "创建时间")
    private String createTime;

    /**
     * 修改人ID
     */
    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;

    /**
     * 修改人名称
     */
    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

    /**
     * 修改时间
     */
    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;


}
