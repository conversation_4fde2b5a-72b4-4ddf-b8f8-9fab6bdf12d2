<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.nation.ContractDownloadMapper">


	<delete id="deleteContractNo">
		delete from nation_contract_no where contractno in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
	</delete>

	<insert id="insertContractNo">
		insert into nation_contract_no(
		contractno
		,effect_status
		,itfpk
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.CONTRACTNO,jdbcType=VARCHAR}
			,#{d.EFFECT_STATUS,jdbcType=VARCHAR}
			,#{d.ITFPK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<update id="processContractNo">
		update mc04_islm_cont_order a,nation_contract_no b
		set
		a.md02_cgt_trade_cont_no = b.contractno,
		a.mc04_cgt_trade_cont_status =  if(b.effect_status = '2', '80', '02')
		where
		b.itfpk = a.mc04_cont_order_code
		and a.mc04_cgt_trade_cont_status in ('01', '02')
		and a.mc04_cont_order_code in
		<foreach collection="codeList" item="code" open="(" close=")" separator=",">
			#{code}
		</foreach>
	</update>

	<update id="updateItemDayPlanStatus">
		update mc04_islmc_cgt_day_plan set mc04_cgt_day_plan_status = '1'
		where mc04_cgt_day_plan_status = '4' and mc04_cgt_day_plan_code in (
		select distinct mc04_cgt_day_plan_code
		from mc04_islm_cont_order
		where mc04_cgt_trade_cont_status in ('80', '90')
		and mc04_cont_order_code in
		<foreach collection="codeList" item="code" open="(" close=")" separator=",">
			#{code}
		</foreach>
		)
	</update>


	<delete id="deleteContract">
		delete from nation_contract where pk in
		<foreach collection="resultList" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertContract">
		insert into nation_contract(
		pk
		,itfpk
		,bill_date
		,contractno
		,protocolno
		,supmember_code
		,supmember_name
		,supregion
		,reqmember_code
		,reqmember_name
		,reqregion
		,supdeputy_name
		,deputy_name
		,cycle_code
		,deli_start_date
		,deli_end_date
		,busi_type
		,audit_status
		,audit_time
		,audit_operat
		,exec_status
		,cancel_time
		,transcertno
		,printno
		,deliwarehouse_code
		,recwarehouse_code
		,deliaddress
		,recaddress
		,reqaddress
		,reqpostcode
		,reqbankacct
		,reqtelephone
		,reqtaxno
		,reqbankdocname
		,supaddress
		,suppostcode
		,supbankacct
		,suptelephone
		,suptaxno
		,supbankdocname
		,portname
		,trans_type
		,settlt_type
		,remark
		)
		values
		<foreach collection="resultList" item="d" separator=",">
			(
			#{d.PK}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.BILL_DATE,jdbcType=CHAR}
			,#{d.CONTRACTNO,jdbcType=VARCHAR}
			,#{d.PROTOCOLNO,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQREGION,jdbcType=VARCHAR}
			,#{d.SUPDEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.DEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.CYCLE_CODE,jdbcType=VARCHAR}
			,#{d.DELI_START_DATE,jdbcType=CHAR}
			,#{d.DELI_END_DATE,jdbcType=CHAR}
			,#{d.BUSI_TYPE,jdbcType=VARCHAR}
			,#{d.AUDIT_STATUS,jdbcType=VARCHAR}
			,#{d.AUDIT_TIME,jdbcType=CHAR}
			,#{d.AUDIT_OPERAT,jdbcType=VARCHAR}
			,#{d.EXEC_STATUS,jdbcType=VARCHAR}
			,#{d.CANCEL_TIME,jdbcType=CHAR}
			,#{d.TRANSCERTNO,jdbcType=VARCHAR}
			,#{d.PRINTNO,jdbcType=VARCHAR}
			,#{d.DELIWAREHOUSE_CODE,jdbcType=VARCHAR}
			,#{d.RECWAREHOUSE_CODE,jdbcType=VARCHAR}
			,#{d.DELIADDRESS,jdbcType=VARCHAR}
			,#{d.RECADDRESS,jdbcType=VARCHAR}
			,#{d.REQADDRESS,jdbcType=VARCHAR}
			,#{d.REQPOSTCODE,jdbcType=VARCHAR}
			,#{d.REQBANKACCT,jdbcType=VARCHAR}
			,#{d.REQTELEPHONE,jdbcType=VARCHAR}
			,#{d.REQTAXNO,jdbcType=VARCHAR}
			,#{d.REQBANKDOCNAME,jdbcType=VARCHAR}
			,#{d.SUPADDRESS,jdbcType=VARCHAR}
			,#{d.SUPPOSTCODE,jdbcType=VARCHAR}
			,#{d.SUPBANKACCT,jdbcType=VARCHAR}
			,#{d.SUPTELEPHONE,jdbcType=VARCHAR}
			,#{d.SUPTAXNO,jdbcType=VARCHAR}
			,#{d.SUPBANKDOCNAME,jdbcType=VARCHAR}
			,#{d.PORTNAME,jdbcType=VARCHAR}
			,#{d.TRANS_TYPE,jdbcType=VARCHAR}
			,#{d.SETTLT_TYPE,jdbcType=VARCHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>


	<delete id="deleteContractItem">
		delete from nation_contract_item where row_pk in
		<foreach collection="items" item="d" open="(" close=")" separator=",">
			#{d.ROW_PK}
		</foreach>
	</delete>


	<insert id="insertContractItem">
		insert into nation_contract_item(
		row_pk
		,pk
		,pk_tradecigarette
		,prov_type
		,itfrow_pk
		,itfpk
		,crowno
		,product_code
		,product_name
		,qty
		,price
		,supmember_code
		,supmember_name
		,supregion
		,cgtprtypename
		,cgtlength
		,cgttypename
		,cgtpacktypename
		,regionname
		,brand_name
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.ROW_PK}
			,#{d.PK,jdbcType=VARCHAR}
			,#{d.PK_TRADECIGARETTE,jdbcType=VARCHAR}
			,#{d.PROV_TYPE,jdbcType=VARCHAR}
			,#{d.ITFROW_PK,jdbcType=VARCHAR}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.CROWNO,jdbcType=VARCHAR}
			,#{d.PRODUCT_CODE,jdbcType=VARCHAR}
			,#{d.PRODUCT_NAME,jdbcType=VARCHAR}
			,#{d.QTY,jdbcType=DECIMAL}
			,#{d.PRICE,jdbcType=DECIMAL}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.CGTPRTYPENAME,jdbcType=VARCHAR}
			,#{d.CGTLENGTH,jdbcType=VARCHAR}
			,#{d.CGTTYPENAME,jdbcType=VARCHAR}
			,#{d.CGTPACKTYPENAME,jdbcType=VARCHAR}
			,#{d.REGIONNAME,jdbcType=VARCHAR}
			,#{d.BRAND_NAME,jdbcType=VARCHAR}
			)
		</foreach>

	</insert>


	<update id="updateSignContract">
		update mc04_islm_cont_order a,nation_contract b
		set
		a.md02_cgt_trade_cont_no = b.contractno,
		a.ma02_cgt_trade_cont_auditoperat_code = b.audit_operat,
		a.ma02_cgt_trade_cont_audit_time = date_format(date(b.audit_time),'%Y%m%d%H%i%S'),
		a.mc04_cgt_trade_cont_status = '10'
		where
		b.contractno = a.md02_cgt_trade_cont_no
		and a.mc04_cgt_trade_cont_status = '02'
		and b.audit_status = '2'
		and b.contractno in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
	</update>

	<update id="updateContractItemFromNation">
		update mc04_islm_cont_order_item a,
		(select c.mc04_cont_order_id,
		b.product_code,
		b.qty,
		b.price                                   as product_puh_no_tax_price,
		qty * price                               as product_puh_no_tax_price_amt
		from nation_contract a,
		nation_contract_item b,
		mc04_islm_cont_order_item c
		where a.pk = b.pk
		and a.itfpk = b.itfpk
		and a.itfpk = c.mc04_cont_order_id
		and b.product_code = c.ac_cgt_carton_code
		and a.contractno in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
		) b
		set a.md02_cgt_trade_cont_qty                = b.qty,
		a.ac_cgt_no_tax_allot_price     = b.product_puh_no_tax_price,
		a.mc04_cgt_trade_cont_no_tax_amt = b.product_puh_no_tax_price_amt
		where b.mc04_cont_order_id = a.mc04_cont_order_id
		and a.ac_cgt_carton_code = b.product_code
		and a.mc04_cont_order_id in (select distinct mc04_cont_order_id
		from mc04_islm_cont_order
		where mc04_cgt_trade_cont_status <![CDATA[<=]]> '10')
	</update>

	<insert id="insertContractFromNation">
		insert into mc04_islm_cont_order(mc04_cont_order_id,
		mc04_cont_order_code,
		ba_com_org_code,
		md02_cgt_trade_cont_no,
		mc04_cgt_xy_period_code,
		ma02_plan_month,
		md02_cgt_xy_no,
		md02_trade_trans_type_code,
		zb_settlement_mode_code,
		md02_cgt_out_storehouse_code,
		md02_cgt_in_storehouse_code,
		mc04_cont_expec_out_date,
		mc04_cgt_pra_in_storehouse_code,
		mc04_cgt_trade_cont_status,
		ma02_toba_prod_trade_type_code,
		mc04_cgt_trade_cont_inv_status,
		md02_cgt_trade_cont_delistartdate,
		md02_cgt_trade_cont_delienddate,
		ma02_cgt_trade_cont_audit_time,
		md02_cgt_trade_cont_cancel_time,
		ma02_cgt_trade_cont_auditoperat_code,
		mz04_navicert_no,
		md02_cgt_trade_cont_remark,
		icom_code)
		select coalesce(itfpk, contractno)                       as mc04_cont_order_id,
		coalesce(itfpk, contractno)                       as mc04_cont_order_code,
		reqmember_code                                    as ba_com_org_code,
		contractno                                        as md02_cgt_trade_cont_no,
		cycle_code                                        as mc04_cgt_xy_period_code,
		date_format(date(audit_time), '%Y%m')             as ma02_plan_month,
		protocolno                                        as md02_cgt_xy_no,
		trans_type                                        as md02_trade_trans_type_code,
		settlt_type                                       as zb_settlement_mode_code,
		b.md02_cgt_out_storehouse_code,
		c.md02_cgt_in_storehouse_code,
		if(busi_type = '2' and reqmember_code = #{icomCode},
		date_format(date(audit_time), '%Y%m%d'), null) as mc04_cont_expec_out_date,
		if(busi_type = '2' and reqmember_code = #{icomCode},
		f.mc04_cgt_pra_in_storehouse_code, null)       as mc04_cgt_pra_in_storehouse_code,
		'10'                                              as mc04_cgt_trade_cont_status,
		busi_type                                         as ma02_toba_prod_trade_type_code,
		'0'                                               as mc04_cgt_trade_cont_inv_status,
		date_format(date(deli_start_date), '%Y%m%d')      as md02_cgt_trade_cont_delistartdate,
		date_format(date(deli_end_date), '%Y%m%d')        as md02_cgt_trade_cont_delienddate,
		date_format(date(audit_time), '%Y%m%d%H%i%s')     as ma02_cgt_trade_cont_audit_time,
		date_format(date(cancel_time), '%Y%m%d%H%i%s')    as md02_cgt_trade_cont_cancel_time,
		audit_operat                                      as ma02_cgt_trade_cont_auditoperat_code,
		transcertno                                       as mz04_navicert_no,
		remark                                            as md02_cgt_trade_cont_remark,
		#{icomCode}                                       as icom_code
		from nation_contract a
		left join mc04_islmc_cont_deliv_whse b
		on b.mc05_logt_transport_del_ind_warehouse_code = a.deliwarehouse_code
		left join mc04_islmc_cont_reach_whse c
		on c.ba_com_org_code = a.reqmember_code and
		c.md03_logt_ic_order_delivery_whse_code = a.recwarehouse_code
		left join mc04_islmc_cont_real_reach_whse f
		on a.reqmember_code = f.ba_com_org_code and f.is_use = '1' and f.ba_default_site_flag = '1'
		where (
		(
		coalesce(`itfpk`, '') = '' and contractno not in (select distinct md02_cgt_trade_cont_no
		from mc04_islm_cont_order
		where md02_cgt_trade_cont_no is not null)
		) or
		(
		(
		coalesce(`itfpk`, '') != '' and `itfpk` not in (select distinct mc04_cont_order_code
		from mc04_islm_cont_order)
		and contractno not in (select distinct md02_cgt_trade_cont_no
		from mc04_islm_cont_order
		where md02_cgt_trade_cont_no is not null)
		)
		)
		)
		and not exists (select 1
		from nation_contract_cancel d
		where a.contractno = d.contractno)
		and PK in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<insert id="insertContractItemFromNation">
		insert into mc04_islm_cont_order_item (mc04_cont_order_item_id,
		mc04_cont_order_id,
		ac_cgt_carton_code,
		ac_two_level_cig_code,
		mc04_cgt_trade_cont_original_qty,
		md02_cgt_trade_cont_qty,
		ac_cgt_no_tax_allot_price,
		mc04_cgt_trade_cont_no_tax_amt,
		icom_code)
		select a.mc04_cont_order_item_id,
		a.mc04_cont_order_id,
		a.ac_cgt_carton_code         as ac_cgt_carton_code,
		c.ac_two_level_cig_code      as ac_two_level_cig_code,
		origin_cont_item_qty         as mc04_cgt_trade_cont_original_qty,
		cont_item_qty                as md02_cgt_trade_cont_qty,
		product_puh_no_tax_price     as ac_cgt_no_tax_allot_price,
		product_puh_no_tax_price_amt as mc04_cgt_trade_cont_no_tax_amt,
		#{icomCode}                  as icom_code
		from (select left(uuid(), 32)              mc04_cont_order_item_id,
		coalesce(a.itfpk, contractno) mc04_cont_order_id,
		b.product_code   as           ac_cgt_carton_code,
		a.reqmember_code as           ba_com_org_code,
		qty                           origin_cont_item_qty,
		qty                           cont_item_qty,
		price                         product_puh_no_tax_price,
		qty * price                   product_puh_no_tax_price_amt
		from nation_contract a,
		nation_contract_item b
		where a.pk = b.pk
		and a.itfpk = b.itfpk
		and not exists (select 1
		from nation_contract_cancel d
		where a.contractno = d.contractno)
		and a.PK in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
		) a
		left join mc04_islmc_com_item c on
		c.ba_com_org_code = a.ba_com_org_code
		and c.ac_cgt_carton_code = a.ac_cgt_carton_code
		and c.is_use = '1'
		left join mc04_islm_cont_order d on a.mc04_cont_order_id = d.mc04_cont_order_id
		where
		(a.mc04_cont_order_id, a.ac_cgt_carton_code) not in (select distinct mc04_cont_order_id, ac_cgt_carton_code
		from mc04_islm_cont_order_item)
	</insert>

	<update id="summaryContract">
		update mc04_islm_cont_order a,
		(select a.mc04_cont_order_id,
		sum(b.md02_cgt_trade_cont_qty)           as md02_cgt_trade_cont_qty,
		sum(b.md02_cgt_trade_cont_qty * coalesce(ac_cgt_tax_allot_price, 0)) as md04_cgt_order_sum_withtax_amt
		from mc04_islm_cont_order a,
		mc04_islm_cont_order_item b
		where a.mc04_cont_order_id = b.mc04_cont_order_id
		and a.md02_cgt_trade_cont_no in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
		group by a.mc04_cont_order_id) b
		set a.md02_cgt_trade_cont_qty = b.md02_cgt_trade_cont_qty,
		a.md04_cgt_order_sum_withtax_amt = b.md04_cgt_order_sum_withtax_amt
		where b.mc04_cont_order_id = a.mc04_cont_order_id
	</update>

	<select id="getNeedSignContract" resultType="java.lang.String">
		select mc04_cont_order_id
		from mc04_islm_cont_order
		where mc04_cgt_trade_cont_status = '02'
		and md02_cgt_trade_cont_no in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
	</select>

	<select id="getChangeSignContract" resultType="java.lang.String">
		select mc04_cont_order_id
		from mc04_islm_cont_order
		where mc04_cgt_trade_cont_status = '10'
		and mc04_islm_cont_order.mc04_cont_order_id in
		<foreach collection="contIdList" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</select>

	<delete id="deleteNationContractCancel">
		delete from nation_contract_cancel where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationContractCancel">
		insert into nation_contract_cancel(
		pk,
		itfpk,
		bill_date,
		contractno,
		supmember_code,
		supmember_name,
		supregion,
		reqmember_code,
		reqmember_name,
		reqregion,
		supdeputy_name,
		deputy_name,
		cycle_code,
		deli_start_date,
		deli_end_date,
		busi_type,
		audit_status,
		audit_time,
		audit_operat,
		cancel_time,
		cancel_reason
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.BILL_DATE,jdbcType=VARCHAR}
			,#{d.CONTRACTNO,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.SUPREGION,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQREGION,jdbcType=VARCHAR}
			,#{d.SUPDEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.DEPUTY_NAME,jdbcType=VARCHAR}
			,#{d.CYCLE_CODE,jdbcType=VARCHAR}
			,#{d.DELI_START_DATE,jdbcType=CHAR}
			,#{d.DELI_END_DATE,jdbcType=CHAR}
			,#{d.BUSI_TYPE,jdbcType=VARCHAR}
			,#{d.AUDIT_STATUS,jdbcType=VARCHAR}
			,#{d.AUDIT_TIME,jdbcType=CHAR}
			,#{d.AUDIT_OPERAT,jdbcType=VARCHAR}
			,#{d.CANCEL_TIME,jdbcType=CHAR}
			,#{d.CANCEL_REASON,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<select id="getNeedCancelContract" resultType="java.lang.String">
		select mc04_cont_order_id
		from mc04_islm_cont_order
		where mc04_cgt_trade_cont_status != '90'
		and md02_cgt_trade_cont_no in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
	</select>

	<update id="updateCancelContract">
		update mc04_islm_cont_order a,nation_contract b
		set
		a.mc04_cgt_trade_cont_status = '90'
		where
		b.contractno = a.md02_cgt_trade_cont_no
		and b.contractno in
		<foreach collection="dataList" item="d" open="(" close=")" separator=",">
			#{d.CONTRACTNO}
		</foreach>
	</update>
</mapper>