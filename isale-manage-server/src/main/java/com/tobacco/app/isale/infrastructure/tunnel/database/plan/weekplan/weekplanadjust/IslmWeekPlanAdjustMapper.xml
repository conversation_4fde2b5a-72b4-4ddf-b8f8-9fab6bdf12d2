<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.weekplan.weekplanadjust.IslmWeekPlanAdjustMapper">

    <!-- 基础结果集映射 -->
    <resultMap id="BaseResultMap" type="com.tobacco.app.isale.infrastructure.entity.IslmcWeekPlanDO">
        <!-- 数据库表字段映射（持久化字段） -->
        <id column="mc04_week_plan_id" property="mc04WeekPlanId"/>
        <result column="mc04_week_plan_code" property="mc04WeekPlanCode"/>
        <result column="ma02_plan_month" property="ma02PlanMonth"/>
        <result column="ba_com_org_code" property="baComOrgCode"/>
        <result column="mc04_date_period_code" property="mc04DatePeriodCode"/>
        <result column="mc04_date_period_name" property="mc04DatePeriodName"/>
        <result column="mc04_date_period_begin_date" property="mc04DatePeriodBeginDate"/>
        <result column="mc04_date_period_end_date" property="mc04DatePeriodEndDate"/>
        <result column="mc04_sale_plan_status" property="mc04SalePlanStatus"/>
        <result column="ac_cgt_carton_code" property="acCgtCartonCode"/>
        <result column="ac_two_level_cig_code" property="acTwoLevelCigCode"/>
        <result column="ac_two_level_cig_name" property="acTwoLevelCigName"/>
        <result column="ac_cgt_tax_allot_price" property="acCgtTaxAllotPrice"/>
        <result column="ac_cgt_trade_price" property="acCgtTradePrice"/>
        <result column="ma02_cgt_pl_qty" property="ma02CgtPlQty"/>
        <result column="mc04_cgt_allot_plan_report_qty" property="mc04CgtAllotPlanReportQty"/>
        <result column="mc04_cgt_allot_plan_region_confirm_qty" property="mc04CgtAllotPlanRegionConfirmQty"/>
        <result column="mc04_cgt_allot_plan_pd_confirm_qty" property="mc04CgtAllotPlanPdConfirmQty"/>
        <result column="za_remark" property="zaRemark"/>
        <result column="ma02_toba_prod_trade_type_code" property="ma02TobaProdTradeTypeCode"/>
        <result column="ac_cgt_price_segment_name" property="acCgtPriceSegmentName"/>
        <result column="ac_cgt_price_segment_code" property="acCgtPriceSegmentCode"/>
        <result column="qty_month_plan" property="qtyMonthPlan"/>
        <result column="ma02_cgt_pl_qty1" property="ma02CgtPlQty1"/>
        <result column="ma02_cgt_pl_qty2" property="ma02CgtPlQty2"/>
        <result column="ma02_cgt_pl_qty3" property="ma02CgtPlQty3"/>
        <result column="ma02_cgt_pl_qty4" property="ma02CgtPlQty4"/>
        <result column="ma02_cgt_pl_qty5" property="ma02CgtPlQty5"/>
        <result column="qty_req1" property="qtyReq1"/>
        <result column="qty_req2" property="qtyReq2"/>
        <result column="qty_req3" property="qtyReq3"/>
        <result column="qty_req4" property="qtyReq4"/>
        <result column="qty_req5" property="qtyReq5"/>
        <result column="qty_act1" property="qtyAct1"/>
        <result column="qty_act2" property="qtyAct2"/>
        <result column="qty_act3" property="qtyAct3"/>
        <result column="qty_act4" property="qtyAct4"/>
        <result column="qty_act5" property="qtyAct5"/>
    </resultMap>

    <!-- 查询月份对应的周编码列表（用于动态获取weekCodes） -->
    <select id="findWeekPlanAdjustByCondition" resultType="string">
        SELECT
            mc04_date_period_code,
            mc04_date_period_name,
            mc04_date_period_begin_date,
            mc04_date_period_end_date
        FROM mc04_ind_data_period
        WHERE mc04_date_period_type = 'T11'
          AND md04_mg_occurrence_month = #{planMonth}
          AND icom_code = '20420001'
        ORDER BY mc04_date_period_code
    </select>

    <!-- 核心查询：按周编码拆分计划量、要货量、实际量 -->
    <select id="findAdjustPlansByCondition" resultMap="BaseResultMap">
        SELECT
            t1.ba_com_org_code,
            t1.ac_cgt_carton_code,
            t1.ac_cgt_name,
            t1.ac_two_level_cig_code,
            t1.ac_two_level_cig_name,
            t1.ac_cgt_price_segment_code,
            t1.ac_cgt_price_segment_name,
            t1.ac_cgt_tax_allot_price,
            t1.ac_cgt_trade_price,
            t1.ma02_cgt_pl_adjusted_qty qty_month_plan,
            -- 周计划量：按周编码拆分ma02_cgt_pl_qty
            t2.ma02_cgt_pl_qty1,
            t2.ma02_cgt_pl_qty2,
            t2.ma02_cgt_pl_qty3,
            t2.ma02_cgt_pl_qty4,
            t2.ma02_cgt_pl_qty5,
            -- 周要货量：按周编码拆分
            t3.qty_req1,
            t3.qty_req2,
            t3.qty_req3,
            t3.qty_req4,
            t3.qty_req5,
            -- 周实际调拨量：按周编码拆分
            t4.qty_act1,
            t4.qty_act2,
            t4.qty_act3,
            t4.qty_act4,
            t4.qty_act5
        FROM
            -- 月计划量子查询
            (
                SELECT
                    ba_com_org_code,
                    ac_cgt_carton_code,
                    ac_cgt_name,
                    ac_two_level_cig_code,
                    ac_two_level_cig_name,
                    ac_cgt_price_segment_code,
                    ac_cgt_price_segment_name,
                    ac_cgt_tax_allot_price,
                    ac_cgt_trade_price,
                    SUM(ma02_cgt_pl_adjusted_qty) ma02_cgt_pl_adjusted_qty
                FROM mc04_islm_month_sale_plan
                WHERE ma02_toba_prod_trade_type_code = '0'
                  AND ma02_plan_month = #{planMonth}
                  AND mc04_cgt_sale_plan_version = '1'
                  AND mc04_month_sale_plan_status = '90'
                  AND ba_com_org_code IN
                      <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                          #{comId}
                      </foreach>
                GROUP BY
                    ba_com_org_code,
                    ac_cgt_carton_code,
                    ac_cgt_name,
                    ac_two_level_cig_code,
                    ac_two_level_cig_name,
                    ac_cgt_price_segment_code,
                    ac_cgt_price_segment_name,
                    ac_cgt_tax_allot_price,
                    ac_cgt_trade_price
            ) t1
            LEFT JOIN
            -- 周计划量查询
            (
                SELECT
                    ba_com_org_code,
                    ac_two_level_cig_code,
                    SUM(CASE WHEN mc04_date_period_code = #{week[0]} THEN ma02_cgt_pl_qty ELSE 0 END) ma02_cgt_pl_qty1,
                    SUM(CASE WHEN mc04_date_period_code = #{week[1]} THEN ma02_cgt_pl_qty ELSE 0 END) ma02_cgt_pl_qty2,
                    SUM(CASE WHEN mc04_date_period_code = #{week[2]} THEN ma02_cgt_pl_qty ELSE 0 END) ma02_cgt_pl_qty3,
                    SUM(CASE WHEN mc04_date_period_code = #{week[3]} THEN ma02_cgt_pl_qty ELSE 0 END) ma02_cgt_pl_qty4,
                    SUM(CASE WHEN mc04_date_period_code = #{week[4]} THEN ma02_cgt_pl_qty ELSE 0 END) ma02_cgt_pl_qty5
                FROM mc04_islm_week_sale_plan
                WHERE ma02_toba_prod_trade_type_code = '0'
                  AND ma02_plan_month = #{planMonth}
                  AND ba_com_org_code IN
                      <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                          #{comId}
                      </foreach>
                GROUP BY ba_com_org_code, ac_two_level_cig_code
            ) t2 ON t1.ba_com_org_code = t2.ba_com_org_code AND t1.ac_two_level_cig_code = t2.ac_two_level_cig_code
            LEFT JOIN
            -- 周要货量子查询
            (
                SELECT
                    ba_com_org_code,
                    ac_two_level_cig_code,
                    SUM(CASE WHEN mc04_date_period_code = #{week[0]} THEN qty_req ELSE 0 END) qty_req1,
                    SUM(CASE WHEN mc04_date_period_code = #{week[1]} THEN qty_req ELSE 0 END) qty_req2,
                    SUM(CASE WHEN mc04_date_period_code = #{week[2]} THEN qty_req ELSE 0 END) qty_req3,
                    SUM(CASE WHEN mc04_date_period_code = #{week[3]} THEN qty_req ELSE 0 END) qty_req4,
                    SUM(CASE WHEN mc04_date_period_code = #{week[4]} THEN qty_req ELSE 0 END) qty_req5
                FROM
                    (
                        -- 前置库移库单量
                        SELECT
                            a.ba_com_org_code,
                            b.ac_two_level_cig_code,
                            a.mc04_date_period_code,
                            SUM(b.md03_logt_tray_comb_tsp_ic_rlc_qty) qty_req
                        FROM mc04_islmc_warehouse_stock_tran_bill a,
                             mc04_islmc_warehouse_stock_tran_bill_item b
                        WHERE a.mc04_warehouse_stock_tran_bill_id = b.mc04_warehouse_stock_tran_bill_id
                          AND a.ma02_toba_prod_trade_type_code = '0'
                          AND a.ma02_plan_month = #{planMonth}
                          AND a.mc04_warehouse_stock_tran_bill_status IN ('30', '40')
                          AND a.ba_com_org_code IN
                              <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                                  #{comId}
                              </foreach>
                        GROUP BY a.ba_com_org_code, b.ac_two_level_cig_code, a.mc04_date_period_code

                        UNION ALL

                        -- 要货量数据
                        SELECT
                            req.ba_com_org_code,
                            req.ac_two_level_cig_code,
                            req.mc04_date_period_code,
                            req.qty_req
                        FROM
                            (
                                SELECT
                                    plan.ba_com_org_code,
                                    plan.ac_two_level_cig_code,
                                    plan.mc04_date_period_code,
                                    COALESCE(plan.ma02_cgt_pl_adjusted_qty, 0) - COALESCE(cont.md02_cgt_trade_cont_qty, 0) qty_req
                                FROM
                                    (
                                        SELECT
                                            ba_com_org_code,
                                            ac_two_level_cig_code,
                                            mc04_date_period_code,
                                            SUM(ma02_cgt_pl_adjusted_qty) ma02_cgt_pl_adjusted_qty
                                        FROM mc04_islmc_cgt_day_plan
                                        WHERE ma02_toba_prod_trade_type_code = '0'
                                          AND ma02_plan_month = #{planMonth}
                                          AND ba_com_org_code IN
                                              <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                                                  #{comId}
                                              </foreach>
                                        GROUP BY ba_com_org_code, ac_two_level_cig_code, mc04_date_period_code
                                    ) plan
                                    LEFT JOIN
                                    (
                                        SELECT
                                            a.ba_com_org_code,
                                            b.ac_two_level_cig_code,
                                            a.mc04_date_period_code,
                                            SUM(b.md02_cgt_trade_cont_qty) md02_cgt_trade_cont_qty
                                        FROM mc04_islm_cont_order a,
                                             mc04_islm_cont_order_item b
                                        WHERE a.mc04_cont_order_id = b.mc04_cont_order_id
                                          AND a.ma02_toba_prod_trade_type_code = '0'
                                          AND a.ma02_plan_month = #{planMonth}
                                          AND a.mc04_cgt_trade_cont_status NOT IN ('80', '90')
                                          AND a.ba_com_org_code IN
                                              <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                                                  #{comId}
                                              </foreach>
                                        GROUP BY a.ba_com_org_code, b.ac_two_level_cig_code, a.mc04_date_period_code
                                    ) cont ON plan.ba_com_org_code = cont.ba_com_org_code
                                        AND plan.ac_two_level_cig_code = cont.ac_two_level_cig_code
                                        AND plan.mc04_date_period_code = cont.mc04_date_period_code
                            ) req
                        WHERE NOT EXISTS (
                            SELECT 1
                            FROM
                                (
                                    SELECT
                                        a.ba_com_org_code,
                                        b.ac_two_level_cig_code,
                                        a.mc04_date_period_code,
                                        SUM(b.md03_logt_tray_comb_tsp_ic_rlc_qty) md03_logt_tray_comb_tsp_ic_rlc_qty
                                    FROM mc04_islmc_warehouse_stock_tran_bill a,
                                         mc04_islmc_warehouse_stock_tran_bill_item b
                                    WHERE a.mc04_warehouse_stock_tran_bill_id = b.mc04_warehouse_stock_tran_bill_id
                                      AND a.ma02_toba_prod_trade_type_code = '0'
                                      AND a.ma02_plan_month = #{planMonth}
                                      AND a.mc04_warehouse_stock_tran_bill_status IN ('30', '40')
                                      AND a.ba_com_org_code IN
                                          <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                                              #{comId}
                                          </foreach>
                                    GROUP BY a.ba_com_org_code, b.ac_two_level_cig_code, a.mc04_date_period_code
                                ) pre
                            WHERE req.ba_com_org_code = pre.ba_com_org_code
                              AND req.ac_two_level_cig_code = pre.ac_two_level_cig_code
                        )
                    ) tab
                GROUP BY ba_com_org_code, ac_two_level_cig_code
            ) t3 ON t1.ba_com_org_code = t3.ba_com_org_code AND t1.ac_two_level_cig_code = t3.ac_two_level_cig_code
            LEFT JOIN
            -- 周实际调拨量子查询
            (
                SELECT
                    a.ba_com_org_code,
                    b.ac_two_level_cig_code,
                    SUM(CASE WHEN a.mc04_date_period_code = #{week[0]} THEN b.md02_cgt_trade_cont_qty ELSE 0 END) qty_act1,
                    SUM(CASE WHEN a.mc04_date_period_code = #{week[1]} THEN b.md02_cgt_trade_cont_qty ELSE 0 END) qty_act2,
                    SUM(CASE WHEN a.mc04_date_period_code = #{week[2]} THEN b.md02_cgt_trade_cont_qty ELSE 0 END) qty_act3,
                    SUM(CASE WHEN a.mc04_date_period_code = #{week[3]} THEN b.md02_cgt_trade_cont_qty ELSE 0 END) qty_act4,
                    SUM(CASE WHEN a.mc04_date_period_code = #{week[4]} THEN b.md02_cgt_trade_cont_qty ELSE 0 END) qty_act5
                FROM mc04_islm_cont_order a,
                     mc04_islm_cont_order_item b
                WHERE a.mc04_cont_order_id = b.mc04_cont_order_id
                  AND a.ma02_toba_prod_trade_type_code = '0'
                  AND a.ma02_plan_month = #{planMonth}
                  AND a.mc04_cgt_trade_cont_status BETWEEN '10' AND '60'
                  AND a.ba_com_org_code IN
                      <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                          #{comId}
                      </foreach>
                GROUP BY a.ba_com_org_code, b.ac_two_level_cig_code
            ) t4 ON t1.ba_com_org_code = t4.ba_com_org_code AND t1.ac_two_level_cig_code = t4.ac_two_level_cig_code
    </select>

    <!-- 获取解锁状态 -->
    <select id="getUnlockStatus" resultType="Map">
        SELECT
            l.ba_com_org_code AS com_code,
            CASE
                WHEN l.ma02_as_lock_state = 0 THEN true
                ELSE false
            END AS unlocked
        FROM mc04_islm_month_sale_plan_lock l
        WHERE l.mz04_business_type_code = '50'
          AND l.za_occurrence_month = #{planMonth}
          AND l.ba_com_org_code IN
              <foreach item="comCode" collection="comCodes" open="(" separator="," close=")">
                  #{comCode}
              </foreach>
    </select>

    <!-- 获取全国计划总量 -->
    <select id="getNationalPlanTotals" resultType="Map">
        SELECT
            ac_two_level_cig_code,
            SUM(ma02_cgt_pl_adjusted_qty) AS national_total
        FROM mc04_islm_month_sale_plan
        WHERE ac_two_level_cig_code IN
              <foreach item="cigCode" collection="cigCodes" open="(" separator="," close=")">
                  #{cigCode}
              </foreach>
          AND mc04_month_sale_plan_status = '90'
        GROUP BY ac_two_level_cig_code
    </select>

</mapper>