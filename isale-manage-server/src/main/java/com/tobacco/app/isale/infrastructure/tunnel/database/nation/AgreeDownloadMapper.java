package com.tobacco.app.isale.infrastructure.tunnel.database.nation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 行业营销子系统协议数据查询 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface AgreeDownloadMapper extends BaseMapper<Map> {
    /**
     * 删除协议中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationAgreement(List<Map<String, Object>> datas);

    /**
     * 批量插入协议中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationAgreement(List<Map<String, Object>> datas);

    /**
     * 删除协议规格中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationAgreementItem(List<Map<String, Object>> datas);

    /**
     * 批量插入协议规格中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationAgreementItem(List<Map<String, Object>> datas);

    /**
     * 删除协议业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmAgreement(List<Map<String, Object>> datas);

    /**
     * 从中间表处理协议业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmAgreementFromNation(List<Map<String, Object>> datas);

    /**
     * 删除协议规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmAgreementItem(List<Map<String, Object>> datas);

    /**
     * 从中间表处理协议规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmAgreementItemFromNation(List<Map<String, Object>> datas);

    /**
     * 从中间表处理协议规格业务从表价格
     *
     * @param datas 数据
     * @return 返回数据
     */
    int updateIsmAgreementItemPriceFromNation(List<Map<String, Object>> datas);

    /**
     * 删除协议最终量中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationFinalAgree(List<String> datas);

    /**
     * 批量插入协议最终量中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationFinalAgree(List<Map<String, Object>> datas);

    /**
     * 删除协议调整中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationAgreementAdjust(List<Map<String, Object>> datas);

    /**
     * 批量插入协议调整中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationAgreementAdjust(List<Map<String, Object>> datas);

    /**
     * 删除协议调整规格中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationAgreementAdjustItem(List<Map<String, Object>> datas);

    /**
     * 批量插入协议调整规格中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationAgreementAdjustItem(List<Map<String, Object>> datas);

    /**
     * 删除协议调整业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmAgreementAdjust(List<Map<String, Object>> datas);

    /**
     * 批量插入协议调整业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmAgreementAdjustFromNation(List<Map<String, Object>> datas);

    /**
     * 删除协议调整规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmAgreementAdjustItem(List<Map<String, Object>> datas);

    /**
     * 批量插入协议调整规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmAgreementAdjustItemFromNation(List<Map<String, Object>> datas);


    /**
     * 删除协议解除中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationAgreementCancel(List<Map<String, Object>> datas);

    /**
     * 批量插入协议解除中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationAgreementCancel(List<Map<String, Object>> datas);

    /**
     * 更新协议业务表状态解除
     *
     * @param datas 数据
     * @return 返回数据
     */
    int updateIsmAgreementCancel(List<Map<String, Object>> datas);

}
