package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * 合同明细表 视图层对象/值对象
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islmc_cont_order_item")
public class Mc04IslmcContOrderItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 合同订单规格主键编码
     */
    @TableId(value = "mc04_cont_order_item_id", type = IdType.NONE)
    @Field(value = "合同订单规格主键编码", name = "合同订单规格主键编码")
    private String mc04ContOrderItemId;

    /**
     * 合同订单编码
     */
    @Field(value = "合同订单编码", name = "合同订单编码")
    private String mc04ContOrderId;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;

    /**
     * 二级牌号编码
     */
    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;

    /**
     * 二级牌号名称
     */
    @Field(value = "二级牌号名称", name = "二级牌号名称")
    private String acTwoLevelCigName;

    /**
     * 三级牌号编码
     */
    @Field(value = "三级牌号编码", name = "三级牌号编码")
    private String acThrLevelCigCode;

    /**
     * 三级牌号名称
     */
    @Field(value = "三级牌号名称", name = "三级牌号名称")
    private String acThrLevelCigName;

    /**
     * 卷烟交易合同原始交易量（万支）
     */
    @Field(value = "卷烟交易合同原始交易量（万支）", name = "卷烟交易合同原始交易量（万支）")
    private BigDecimal mc04CgtTradeContOriginalQty;

    /**
     * 卷烟交易合同交易量（万支）
     */
    @Field(value = "卷烟交易合同交易量（万支）", name = "卷烟交易合同交易量（万支）")
    private BigDecimal md02CgtTradeContQty;

    /**
     * 卷烟含税调拨价格（元/万支）
     */
    @Field(value = "卷烟含税调拨价格（元/万支）", name = "卷烟含税调拨价格（元/万支）")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟交易合同交易金额（元）
     */
    @Field(value = "卷烟交易合同交易金额（元）", name = "卷烟交易合同交易金额（元）")
    private BigDecimal md02CgtTradeContAmt;

    /**
     * 卷烟不含税调拨价（元/万支）
     */
    @Field(value = "卷烟不含税调拨价（元/万支）", name = "卷烟不含税调拨价（元/万支）")
    private BigDecimal acCgtNoTaxAllotPrice;

    /**
     * 卷烟交易合同交易不含税金额（元）
     */
    @Field(value = "卷烟交易合同交易不含税金额（元）", name = "卷烟交易合同交易不含税金额（元）")
    private BigDecimal mc04CgtTradeContNoTaxAmt;

    /**
     * 卷烟交易合同规格托盘类型（枚举：0:非托盘,1:纸滑拖）
     */
    @Field(value = "卷烟交易合同规格托盘类型（枚举：0:非托盘,1:纸滑拖）", name = "卷烟交易合同规格托盘类型（枚举：0:非托盘,1:纸滑拖）")
    private String mc04CgtTradeContCgtTrayType;

    /**
     * 卷烟订单金额（元）
     */
    @Field(value = "卷烟订单金额（元）", name = "卷烟订单金额（元）")
    private BigDecimal md04CgtOrderAmt;

    /**
     * 卷烟采购发票税额（元）
     */
    @Field(value = "卷烟采购发票税额（元）", name = "卷烟采购发票税额（元）")
    private BigDecimal mz04CgtBuyInvTaxAmt;

    /**
     * 合同托盘数量
     */
    @Field(value = "合同托盘数量", name = "合同托盘数量")
    private BigDecimal mc04CgtContPalletQty;

    /**
     * 合同非托盘数量
     */
    @Field(value = "合同非托盘数量", name = "合同非托盘数量")
    private BigDecimal mc04CgtContUnPalletQty;


}
