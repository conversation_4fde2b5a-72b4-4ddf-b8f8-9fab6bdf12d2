<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.mapper.Mc04IndXyOrgCgtPurchSaleStkDayMapper">

    <select id="getEndDate" resultType="java.lang.String">
        select data_date
        from ism_gather_log
        where data_table = #{tableName}
    </select>

    <select id="getFirstHalfYearSalesGroupByProduct"
            resultType="com.tobacco.app.isale.domain.model.agreement.sign.ManageIndXyOrgCgtPurchSaleStkDay">
        select ac_cgt_carton_code, sum(md03_cgt_10th_com_coe_sale_qty_y_a) as md03_cgt_10th_com_coe_sale_qty_y_a
        from mc04_ind_xy_org_cgt_purch_sale_stk_day
        where mc04_xy_org_code = #{baComOrgCode}
            and mc04_purchase_sale_stk_date = #{endDate}
        group by ac_cgt_code
    </select>

</mapper>
