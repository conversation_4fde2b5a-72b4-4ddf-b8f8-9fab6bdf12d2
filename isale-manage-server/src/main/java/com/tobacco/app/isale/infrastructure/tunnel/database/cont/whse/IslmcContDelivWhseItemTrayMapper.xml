<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.cont.whse.IslmcContDelivWhseItemTrayMapper">

    <select id="getList" resultType="com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhseItemTray">
        WITH tray_products AS (
            SELECT DISTINCT ac_cgt_carton_code
            FROM mc04_islmc_com_item
            WHERE md03_logt_tray_comb_tsp_tray_type = '1'
        )
        SELECT
            ii.item_code,
            ii.product_code,
            ii.item_name,
            ii.item_tray_type,
            icdwit.item_tray_capacity
        FROM
            mc04_islmc_com_item ii
        LEFT JOIN mc04_islmc_cont_deliv_whse_item_tray icdwit ON icdwit.item_code = ii.item_code

            AND icdwit.cont_deliv_whse_id = #{md02CgtOutStorehouseCode}

        JOIN tray_products tp ON tp.product_code = ii.product_code
        WHERE
            ii.is_use = '1' AND ii.item_level = '3'
    </select>
</mapper>