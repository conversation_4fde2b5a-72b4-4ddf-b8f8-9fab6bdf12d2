/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.ycplan;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.plan.PlanQty;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: loongxi
 * @Date: 2025/8/12
 * @Description:
 */
@Mapper
public interface IslmYcSalePlanMapper {
    /**
     * 获取销售数据
     * @param baComOrgCode 公司编码
     * @param acCgtCartonCode 产品code
     * @param monthCode 月份
     * @return
     */
    @MapKey("acCgtCartonCode")
    @DS("dws")
    Map<String, Object> getMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth);

    @MapKey("acCgtCartonCode")
    @DS("dws")
    Map<String, Object> getSpecialProvinceMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth);

    @MapKey("acCgtCartonCode")
    @DS("dws")
    Map<String, Object> getNormalProvinceMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth);




    /**
     * 获取省内省外某年末所有卷烟规格的商业库存
     * @param year 年 如果是2024年末就传入 2024
     */
    @DS("dws")
    List<PlanQty> getYearEndStkQtySnSw(String year);

    /**
     * 获取近三年省外或省外某月近三年同期最大库存
     * @param year
     */
    @DS("dws")
    List<PlanQty> getSnSwJanAndFebMaxEndStkQty(String year);

}
