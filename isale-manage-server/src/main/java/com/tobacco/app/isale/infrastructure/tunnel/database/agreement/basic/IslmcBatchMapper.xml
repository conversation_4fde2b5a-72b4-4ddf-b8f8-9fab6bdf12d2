<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic.IslmcBatchMapper">
    <select id="queryBatchPage" resultType="com.tobacco.app.isale.domain.model.agreement.basic.IslmcBatchModel" >
        select b.*,

        GROUP_CONCAT(DISTINCT bd.mz10_index_name ORDER BY bd.seq SEPARATOR ', ') AS mz10IndexName
        from mc04_islmc_batch b
        LEFT JOIN (
        SELECT
        bi.mc04_batch_no,
        dfi.mz10_index_name,
        bi.seq
        FROM
        mc04_islmc_batch_index bi
        LEFT JOIN
        mc04_islmc_demand_fo_index dfi ON bi.mz10_index_code = dfi.mz10_index_code
        ) AS bd ON b.mc04_batch_no = bd.mc04_batch_no
        where b.ma02_toba_prod_trade_type_code = #{map.cgtType}
        <if test="map.dateTypes != null and map.dateTypes.size() > 0">
            and b.mc04_date_period_type in
            <foreach collection="map.dateTypes" item="dateType" open="(" separator="," close=")">
                #{dateType}
            </foreach>
        </if>
        <if test="map.dateCode != null and map.dateCode != ''">
            <choose>
                <when test="map.dateCode.length() > 4">
                    and b.mc04_date_period_code = #{map.dateCode}
                </when>
                <otherwise>
                    and b.mc04_date_period_code like concat('%', #{map.dateCode}, '%')
                </otherwise>
            </choose>
        </if>
        <if test="map.batchStatusList != null and map.batchStatusList.size() > 0">
            and b.za_enable_status in
            <foreach collection="map.batchStatusList" item="batchStatus" open="(" separator="," close=")">
                #{batchStatus}
            </foreach>
        </if>
        <if test="map.batchTypes != null and map.batchTypes.size() > 0">
            and b.mc04_batch_type in
            <foreach collection="map.batchTypes" item="batchType" open="(" separator="," close=")">
                #{batchType}
            </foreach>
        </if>
        group by b.mc04_batch_no
        order by b.mc04_date_period_code desc
    </select>
    <select id="queryComBatchPage" resultType="com.tobacco.app.isale.domain.model.agreement.basic.IslmcBatchLineModel">
        select bl.*,b.* from mc04_islmc_batch_line bl,
        mc04_islmc_batch b
        where bl.mc04_batch_no = b.mc04_batch_no
        and b.ma02_toba_prod_trade_type_code = #{map.cgtType}
        and b.za_enable_status = '1'
        <if test="map.lockStates != null and map.lockStates.size() > 0">
            and bl.ma02_as_lock_state in
            <foreach collection="map.lockStates" item="lockState" open="(" separator="," close=")">
                #{lockState}
            </foreach>
        </if>
        <if test="map.batchTypes != null and map.batchTypes.size() > 0">
            and b.mc04_batch_type in
            <foreach collection="map.batchTypes" item="batchType" open="(" separator="," close=")">
                #{batchType}
            </foreach>
        </if>
        <if test="map.dateTypes != null and map.dateTypes.size() > 0">
            and b.mc04_date_period_type in
            <foreach collection="map.dateTypes" item="dateType" open="(" separator="," close=")">
                #{dateType}
            </foreach>
        </if>
        <if test="map.dateCode != null and map.dateCode != ''">
            <choose>
                <when test="map.dateCode.length() > 4">
                    and b.mc04_date_period_code = #{map.dateCode}
                </when>
                <otherwise>
                    and b.mc04_date_period_code like concat('%', #{map.dateCode}, '%')
                </otherwise>
            </choose>
        </if>
        <if test="map.comIds != null and map.comIds.size() > 0">
            and bl.ba_com_org_code in
            <foreach collection="map.comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="map.batchNos != null and map.batchNos.size() > 0">
            and bl.mc04_batch_no in
            <foreach collection="map.batchNos" item="batchNo" open="(" separator="," close=")">
                #{batchNo}
            </foreach>
        </if>
        order by b.mc04_date_period_code desc ,b.mc04_batch_type ,bl.ba_com_org_code
    </select>
    <select id="queryComBatchList" resultType="com.tobacco.app.isale.domain.model.agreement.basic.IslmcBatchModel">
        select
        b.mc04_batch_no,
        b.mc04_batch_name,
        b.mc04_date_period_code,
        b.mc04_batch_type
        from mc04_islmc_batch_line bl,
        mc04_islmc_batch b
        where bl.mc04_batch_no = b.mc04_batch_no
        and b.ma02_toba_prod_trade_type_code = #{cgtType}
        and b.za_enable_status = '1'
        <if test="lockStates != null and lockStates.size() > 0">
            and bl.ma02_as_lock_state in
            <foreach collection="lockStates" item="lockState" open="(" separator="," close=")">
                #{lockState}
            </foreach>
        </if>
        <if test="batchTypes != null and batchTypes.size() > 0">
            and b.mc04_batch_type in
            <foreach collection="batchTypes" item="batchType" open="(" separator="," close=")">
                #{batchType}
            </foreach>
        </if>
        <if test="dateTypes != null and dateTypes.size() > 0">
            and b.mc04_date_period_type in
            <foreach collection="dateTypes" item="dateType" open="(" separator="," close=")">
                #{dateType}
            </foreach>
        </if>
        <if test="dateCode != null and dateCode != ''">
            <choose>
                <when test="dateCode.length() > 4">
                    and b.mc04_date_period_code = #{dateCode}
                </when>
                <otherwise>
                    and b.mc04_date_period_code like concat('%', #{dateCode}, '%')
                </otherwise>
            </choose>
        </if>
        <if test="comIds != null and comIds.size() > 0">
            and bl.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        group by b.mc04_batch_no,b.mc04_batch_name,b.mc04_date_period_code,b.mc04_batch_type
        order by b.mc04_date_period_code desc,b.mc04_batch_type
    </select>
</mapper>