/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @description : 发货仓库信息表
 *
 * <AUTHOR> wangluhao01
 * @since : 2025-05-23
 * @email : <EMAIL>
 * @create_time : 2025-05-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_cont_deliv_whse")
@DataObject(name = "发货仓库信息表", desc = "发货仓库信息表")
public class Mc04IslmcContDelivWhseDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 卷烟发货仓库代码
             */

            @TableId(value = "md02_cgt_out_storehouse_code", type = IdType.NONE)
    @Field(value = "卷烟发货仓库代码", name = "卷烟发货仓库代码")
    private String md02CgtOutStorehouseCode;
                                /**
             * 卷烟发货仓库名称
             */

    @Field(value = "卷烟发货仓库名称", name = "卷烟发货仓库名称")
    private String md02CgtOutStorehouseName;
                                /**
             * 物流仓库基本信息名称简称
             */

    @Field(value = "物流仓库基本信息名称简称", name = "物流仓库基本信息名称简称")
    private String cbCbLogtWhseNameAbbrev;
                                /**
             * 卷烟发货仓库地址
             */

    @Field(value = "卷烟发货仓库地址", name = "卷烟发货仓库地址")
    private String md02CgtOutStorehouseAdress;
                                /**
             * 发货仓库经度(国标),计算距离使用
             */

    @Field(value = "发货仓库经度(国标),计算距离使用", name = "发货仓库经度(国标),计算距离使用")
    private BigDecimal caStorehouseOutLongitude;
                                /**
             * 发货仓库维度(国标),计算距离使用
             */

    @Field(value = "发货仓库维度(国标),计算距离使用", name = "发货仓库维度(国标),计算距离使用")
    private BigDecimal caStorehouseOutLalitude;
                                /**
             * 发货仓库国家局交易子系统编码
             */

    @Field(value = "发货仓库国家局交易子系统编码", name = "发货仓库国家局交易子系统编码")
    private String mc05LogtTransportDelIndWarehouseCode;
                                /**
             * 国家局在途物流信息系统
             */

    @Field(value = "国家局在途物流信息系统", name = "国家局在途物流信息系统")
    private String md03LogtIcOrderDeliveryWhseCode;
                                /**
             * 卷烟厂代码
             */

    @Field(value = "卷烟厂代码", name = "卷烟厂代码")
    private String baFactOrgCode;
                                /**
             * 物流仓库业务类型0:自有库,1:浙江工商共库,2:前置库,3:合作生产,4:集并库
             */

    @Field(value = "物流仓库业务类型0:自有库,1:浙江工商共库,2:前置库,3:合作生产,4:集并库", name = "物流仓库业务类型0:自有库,1:浙江工商共库,2:前置库,3:合作生产,4:集并库")
    private String cbLogtWhseBusiType1;
                                /**
             * 是否使用0:否, 1:是
             */

    @Field(value = "是否使用0:否, 1:是", name = "是否使用0:否, 1:是")
    private String isUse;
                                /**
             * 排序码
             */

    @Field(value = "排序码", name = "排序码")
    private Integer seq;
                                                                                                    

}
