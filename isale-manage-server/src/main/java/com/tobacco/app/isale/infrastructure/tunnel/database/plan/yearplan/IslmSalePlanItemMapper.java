package com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface IslmSalePlanItemMapper {

    /**
     * 查询年度计划分解（中心）明细
     *
     * @param mc04SalePlanId   销售调拨计划编号
     * @param currentYear 业务年份
     * @param previousYear 业务年份上一年
     * @return 查询年度计划分解（中心）明细
     */
    @DS("dws")
    @MapKey("mc04SalePlanItemId")
    List<Map<String, Object>> itemListBySalePlanId(
            @Param("mc04SalePlanId") String mc04SalePlanId,
            @Param("currentYear") String currentYear,
            @Param("previousYear") String previousYear
    );
}
