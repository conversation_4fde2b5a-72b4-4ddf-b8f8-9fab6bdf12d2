/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.tunnel.database.cont.transfer;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.domain.model.cont.transfer.*;
import com.tobacco.app.isale.dto.cont.transfer.TransferOrderPalletDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR> wuhaoran01
 * @email : <EMAIL>
 * @create_time : 2025/05/23
 * @description : 前置库持久层
 */
@Mapper
public interface TransferOrderMapper {

    Page<WarehouseStockTranBill> queryTransferData(Page<WarehouseStockTranBill> page,@Param("queryParam") TransferOrderBillPage queryParam);

    Page<TransferMonthPlan> queryMonthPlanData(Page<TransferMonthPlan> page, @Param("queryParam") TransferOrderMonthPlanPage queryParam);

    Page<Stock> queryStockData(Page<Stock> page, @Param("queryParam") TransferOrderStockPage queryParam);

    List<TransferOrderDetail> getTransferOrderDetail(@Param("queryParam") TransferOrderDetailQuery query);

    int submit(@Param("billIds")String billIds,@Param("status") String status);

    List<TransferOrderPallet> queryPallet(@Param("baComOrgCode")String baComOrgCode,@Param("icomCode") String icomCode);

    List<TransferOrderDetail> getTransferOrderDetailInAdd(@Param("queryParam")TransferOrderDetailQuery query);

    int setPalletEnabled(@Param("list") List<TransferOrderPalletDTO> palletList,@Param("baComOrgCode")String baComOrgCode,@Param("icomCode") String icomCode);

    List<TransferOrderWeekSelect> queryWeekSelect(@Param("queryParam")TransferOrderWeekSelect transferOrderWeekSelect);
}
