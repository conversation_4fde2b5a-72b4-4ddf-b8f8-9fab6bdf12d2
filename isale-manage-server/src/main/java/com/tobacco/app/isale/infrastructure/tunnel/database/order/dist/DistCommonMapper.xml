<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.DistCommonMapper">

    <select id="getStatusNav" resultType="com.tobacco.app.isale.domain.model.order.dist.DistStatusNav">
        select
            mc04_cgt_dist_order_confirm_status confirm_status,
            mc04_cgt_dist_order_reject_status reject_status
        from mc04_islmc_dist_status_nav
        where
            mc04_cgt_dist_busi_type = #{busiType}
            and mc04_cgt_dist_order_type = #{distType}
            and mc04_cgt_dist_order_current_status = #{curStatus}
            and icom_code = #{icomCode}
    </select>

    <select id="getDistOrderCodes" resultType="String">
        select
            distinct mc04_cgt_dist_order_code
        from mc04_islmc_cgt_dist_order
        where
            mc04_cgt_dist_order_status = '50'
    </select>

    <select id="getDistParamPks" resultType="String">
        select
           b.pk
        from
            mc04_islmc_dist_parm_apply a,
            (
                select
                    b.supmember_code,b.reqmember_code,b.distregion_code,b.md02_dist_receiveregion_code,min(pk) pk
                from nation_parm b
                group by
                    b.supmember_code,b.reqmember_code,b.distregion_code,b.md02_dist_receiveregion_code
            ) b
        where a.ma02_cgt_trade_supp_memb_code = b.supmember_code
            and a.ma02_cgt_trade_req_memb_code = b.reqmember_code
            and coalesce(a.md02_cgt_dist_region_code ,'') = coalesce(b.distregion_code ,'')
            and coalesce(a.md02_dist_receiveregion_code ,'') = coalesce(b.md02_dist_receiveregion_code ,'')
            and mc04_cgt_dist_parm_apply_status = '50'
    </select>


</mapper>