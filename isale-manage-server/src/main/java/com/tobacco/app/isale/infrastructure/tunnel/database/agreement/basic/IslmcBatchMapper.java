/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomPage;
import com.tobacco.app.isale.domain.model.agreement.basic.IslmcBatchLineModel;
import com.tobacco.app.isale.domain.model.agreement.basic.IslmcBatchModel;
import com.tobacco.app.isale.req.agreement.basic.BatchQueryREQ;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> jinfuli
 * @Email : <EMAIL>
 * @Create : 2025/04/22 09:15
 * @Description : 每个协议周期，意向和反馈上报可能开展多个轮次，
 * 为了对每个轮次的上报过程和数据进行有效管理，设计批次维护的功能，由协议管理员对批次进行维护，批次维护按照行业通知进行。
 */
@Mapper
public interface IslmcBatchMapper {


    CustomPage<IslmcBatchModel> queryBatchPage(Page<IslmcBatchModel> pager, BatchQueryREQ  map);


    CustomPage<IslmcBatchLineModel> queryComBatchPage(Page<IslmcBatchLineModel> pager, Map  map);


    List<IslmcBatchModel> queryComBatchList(Map map);
}
