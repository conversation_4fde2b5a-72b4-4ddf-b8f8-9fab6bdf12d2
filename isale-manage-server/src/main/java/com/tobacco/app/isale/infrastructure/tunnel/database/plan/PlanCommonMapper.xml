<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.PlanCommonMapper">


    <select id="getYearEndStkQty" resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">
        SELECT
            ac_cgt_carton_code productCode,
            mc04_xy_org_code busiComCode,
            sum( mc04_mg_cgt_10th_eom_stk_qty ) yearEndStock
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month
        WHERE
            mc04_cgt_forfeiture_flag = '1'
          AND mc04_purchase_sale_stk_month = CONCAT( #{year}, 12 )
          AND mc04_xy_org_code IN
          <foreach item="item" index="index" collection="busiComCodeList" open="(" separator="," close=")">
            #{item}
          </foreach>
        GROUP BY
            ac_cgt_carton_code,
            mc04_xy_org_code
    </select>

</mapper>