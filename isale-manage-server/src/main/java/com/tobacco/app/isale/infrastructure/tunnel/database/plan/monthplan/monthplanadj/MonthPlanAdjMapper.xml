<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanadj.MonthPlanAdjMapper">

    <select id="getComItemList" resultType="com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdj">
        SELECT
            ici.ba_com_org_code,
            ici.ac_cgt_carton_code,
            ici.ac_two_level_cig_code,
            imsp.za_occurrence_month,
            imsp.ma02_plan_month,
            imsp.mc04_cgt_sale_plan_version,
            imsp.mc04_month_sale_plan_type,
            imsp.ba_prov_org_code,
            imsp.ba_com_org_code,
            imsp.mc04_cgt_xy_period_code,
            imsp.mc04_cgt_sale_plan_adjusted_qty,
            imsp.ma02_cgt_pl_adjusted_qty
        FROM
            mc04_islmc_com_item ici
        LEFT JOIN mc04_islm_month_sale_plan imsp ON ici.ac_cgt_carton_code = imsp.ac_cgt_carton_code
                AND ici.ac_cgt_carton_code = imsp.ac_cgt_carton_code
                AND imsp.ma02_plan_month = #{planMonth}
                AND imsp.mc04_cgt_sale_plan_version = '1'
        WHERE
            ici.ba_com_org_code IN (
                <foreach item="comId" collection="comIds" separator=",">
                    #{comId}
                </foreach>
            )
          AND ici.is_use = '1'
          AND ici.ac_one_level_class_type_code = '01'
    </select>
    <select id="getCgtPlResolTotalTplanQtyList" resultType="com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdj">
        SELECT
        a.ba_com_org_code,
        b.ac_two_level_cig_code,
        a.mc04_month_sale_plan_type,
        sum(b.md02_cgt_trade_cont_qty) ma02_cgt_pl_resol_total_tplan_qty
        FROM
        mc04_islm_cont_order a,
        mc04_islm_cont_order_item b
        WHERE
        a.mc04_cont_order_id = b.mc04_cont_order_id
        AND a.ma02_toba_prod_trade_type_code = '0'
        AND a.icom_code = #{icomCode}
        AND a.ma02_plan_month = #{planMonth}
        AND a.mc04_cgt_trade_cont_status NOT IN ( '80', '90' ) -- 80:撤销合同订单,90:解除
        AND a.ba_com_org_code IN
        <foreach collection="baComOrgCodeList" item="comId" open="(" separator="," close=")">
            #{comId}
        </foreach>
        GROUP BY
        a.ba_com_org_code,
        b.ac_two_level_cig_code,
        a.mc04_month_sale_plan_type
    </select>
	<select id="queryOrgStatus" resultType="java.util.Map">
		select
            BA_COM_ORG_CODE,
			MIN(mc04_month_sale_plan_status) STATUS
		from
			mc04_islm_month_sale_plan_adj
		where
			ma02_toba_prod_trade_type_code = '0'
			and mc04_is_latest_adjust = '1'
			and ma02_plan_month = #{ma02PlanMonth}
			and icom_code =  #{icomCode}
		group by
			ba_com_org_code
		having
            MIN(mc04_month_sale_plan_status) >= #{mc04MonthSalePlanStatus};
	</select>
    <select id="getAdjustedQtyByMonth" resultType="java.util.Map">
        select
            ac_cgt_carton_code,
            ba_com_org_code,
            mc04_month_sale_plan_type,
            COALESCE(SUM(ABS(mc04_cgt_sale_plan_pd_confirm_adj_qty)), 0) mc04_cgt_sale_plan_pd_confirm_adj_qty,
            COALESCE(SUM(ABS(mc04_cgt_allot_plan_pd_confirm_adj_qty)), 0) mc04_cgt_allot_plan_pd_confirm_adj_qty
        from
            mc04_islm_month_sale_plan_adj
        where
            ma02_toba_prod_trade_type_code = '0'
          and mc04_is_latest_adjust = '1'
          and ma02_plan_month = #{ma02PlanMonth}
            and ba_com_org_code IN
            <foreach collection="baComOrgCodeList" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        group by
            ba_com_org_code,
            ac_cgt_carton_code,
            mc04_month_sale_plan_type
    </select>
</mapper>
