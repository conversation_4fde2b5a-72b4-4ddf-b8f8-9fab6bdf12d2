/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 公司商品默认合同发货仓库表
 * @Author: zhangshch01
 * @Since: 2025-08-01
 * @Email: <EMAIL>
 * @Create: 2025-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_com_item_cont_deliv_whse")
@DataObject(name = "公司商品默认合同发货仓库表", desc = "公司商品默认合同发货仓库表")
public class Mc04IslmcComItemContDelivWhseDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
    /**
     * 商业公司编码
     */

    @TableId(value = "ba_com_org_code", type = IdType.NONE)
    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;
    /**
     * 二级牌号编码
     */

    @TableField(value = "ac_two_level_cig_code")
    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;
    /**
     * 卷烟代码（条）
     */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
    /**
     * 卷烟发货仓库代码
     */

    @Field(value = "卷烟发货仓库代码", name = "卷烟发货仓库代码")
    private String md02CgtOutStorehouseCode;
}