<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmStockInitMapper">
    <select id="selectStockHistoryByYear" resultType="com.tobacco.app.isale.domain.model.plan.yearplan.IslmStockItemHistoryModel">
        SELECT
            ac_cgt_carton_code,
            mc04_purchase_sale_stk_year,
            SUM(mc04_mg_cgt_10th_eom_stk_qty) AS year_stk_qty,
            SUM(CASE
                    WHEN ba_prov_org_code = '11420001'
                        THEN mc04_mg_cgt_10th_eom_stk_qty
                    ELSE 0
                END) AS year_in_stk_qty,
            SUM(CASE
                    WHEN ba_prov_org_code != '11420001'
                THEN mc04_mg_cgt_10th_eom_stk_qty
                    ELSE 0
                END) AS year_out_stk_qty
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month
        WHERE
            mc04_purchase_sale_stk_year = #{year}
          AND mc04_purchase_sale_stk_month = CONCAT(#{year}, '12')
        GROUP BY
            ac_cgt_carton_code,
            ba_prov_org_code;
    </select>
    <select id="selectPlHistoryByYear"
            resultType="com.tobacco.app.isale.domain.model.plan.yearplan.IslmPlItemHistoryModel">
        SELECT
            ac_cgt_carton_code,
            mc04_purchase_sale_stk_year,
            SUM(ma02_cgt_com_from_ind_buy_qty_y_a) AS year_buy_qty,
            SUM(CASE
                    WHEN ba_prov_org_code = '11420001'
                        THEN ma02_cgt_com_from_ind_buy_qty_y_a
                    ELSE 0
                END) AS year_in_buy_qty,
            SUM(CASE
                    WHEN ba_prov_org_code != '11420001'
        THEN ma02_cgt_com_from_ind_buy_qty_y_a
                    ELSE 0
                END) AS year_out_buy_qty
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month
        WHERE
            mc04_purchase_sale_stk_year = #{year}
          AND mc04_purchase_sale_stk_month = CONCAT(#{year}, '12')
        GROUP BY
            ac_cgt_carton_code,
            ba_prov_org_code;
    </select>
    <select id="selectSaleHistoryByYear"
            resultType="com.tobacco.app.isale.domain.model.plan.yearplan.IslmSaleItemHistoryModel">
        SELECT
            ac_cgt_carton_code,
            mc04_purchase_sale_stk_year,
            SUM(md03_cgt_10th_com_coe_sale_qty_y_a) AS year_sale_qty,
            SUM(CASE
                    WHEN ba_prov_org_code = '11420001'
                        THEN md03_cgt_10th_com_coe_sale_qty_y_a
                    ELSE 0
                END) AS year_in_sale_qty,
            SUM(CASE
                    WHEN ba_prov_org_code != '11420001'
        THEN md03_cgt_10th_com_coe_sale_qty_y_a
                    ELSE 0
                END) AS year_out_sale_qty
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month
        WHERE
            mc04_purchase_sale_stk_year = #{yearStr}
          AND mc04_purchase_sale_stk_month = CONCAT(#{year}, '12')
        GROUP BY
            ac_cgt_carton_code,
            ba_prov_org_code;
    </select>
    <select id="selectHistoryMonthSaleItem"
            resultType="com.tobacco.app.isale.domain.model.plan.yearplan.IslmSaleItemHistoryMonthModel">
        SELECT
            ac_cgt_carton_code,
            mc04_purchase_sale_stk_year,
            mc04_purchase_sale_stk_month,
            md03_cgt_10th_com_coe_sale_qty_m_a AS sale_qty
        FROM mc04_ind_com_cgt_purch_sale_stk_month
        WHERE mc04_purchase_sale_stk_year = #{year}
          AND mc04_purchase_sale_stk_month = CONCAT(#{year}, #{month})
          AND ba_city_org_code = #{comCode}
    </select>
    <select id="selectHistoryMonthPlItem"
            resultType="com.tobacco.app.isale.domain.model.plan.yearplan.IslmPlItemHistoryMonthModel">
        SELECT
            ac_cgt_carton_code,
            mc04_purchase_sale_stk_year,
            mc04_purchase_sale_stk_month,
            ma02_cgt_com_from_ind_buy_qty_m_a AS buy_qty
        FROM mc04_ind_com_cgt_purch_sale_stk_month
        WHERE mc04_purchase_sale_stk_year = #{year}
          AND mc04_purchase_sale_stk_month = CONCAT(#{year}, #{month})
          AND ba_city_org_code = #{comCode}
    </select>
</mapper>