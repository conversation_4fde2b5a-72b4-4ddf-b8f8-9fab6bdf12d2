/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanadjreview;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04MonthPlanAdjReviewQuery;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04MonthPlanAdjReviewQueryResult;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: jxy
 * @Date: 2025/8/18
 * @Description:
 */
@Mapper
public interface IslmMonthPlanAdjReviewMapper extends BaseMapper<Mc04IslmMonthSalePlanAdjDO> {

    /**
     * 查询卷烟状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @return
     */
    List<Map<String, Object>> queryCgtStatus(String ma02PlanMonth, String icomCode);

    /**
     * 查询年度计划最新主题类型
     *
     * @param zaOccurrenceYear
     * @param ma02TobaProdTradeTypeCode
     * @return
     */
    String getPlanSubjectType(String zaOccurrenceYear, String ma02TobaProdTradeTypeCode);

    /**
     * 在联合查询库, 获取月计划上报品牌部审核列表
     *
     * @param acCgtCartonCode
     * @param mc04PurchaseSaleStkDate
     * @return
     */
    @DS("dws")
    List<Map<String, Object>> getSaleStkDayList(String acCgtCartonCode, String mc04PurchaseSaleStkDate);

    /**
     * 在联合查询库, induniondb查最近汇总日期
     *
     * @return
     */
    @DS("dws")
    String getDataDate();

    /**
     * 查询月计划调整列表
     *
     * @param query
     * @return
     */
    List<Mc04MonthPlanAdjReviewQueryResult> getMonthPlanAdjBrandList(Mc04MonthPlanAdjReviewQuery query);

    /**
     * 查询行情价格
     *
     * @param acCgtCartonCode 卷烟编码
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @return
     */
    List<Map<String, Object>> getMarketPrice(String acCgtCartonCode, String startTime, String endTime);
}
