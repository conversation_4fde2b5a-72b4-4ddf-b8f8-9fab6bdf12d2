/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSalePlanDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Author: jinfuli
 * @Date: 2025/6/14
 * @Description:
 */
@Mapper
public interface IslmYearPlanMapper {
    /**
     * 获取年度计划列表
     **/
    Page<Mc04IslmSalePlanDTO> getyearplanlist(
            Page page,
            String startYear,
            String endYear,
            String type,
            String status,
            String ma02TobaProdTradeTypeCode,
            String foCount
    );


    /**
     * 月计划量
     **/
    boolean	createMc04IslmSalePlan(
            String planId,
            String version,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );


    /**
     * 月计划量
     **/
    boolean	createMc04IslmBrandPlan(
            String planId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );

    /**
     * 月计划量
     **/
    boolean	createMc04IslmSaleFo(
            String mc04CgtSaleFoId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );

    /**
     * 月计划量
     **/
    //todo 协议单位编码
    boolean	createMc04IslmDemandFo(
            String mc04DemandFoId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );

    /**
     * 月计划量
     **/
    boolean	createMc04IslmYcSalePlan(
            String mc04SalePlanId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );
}
