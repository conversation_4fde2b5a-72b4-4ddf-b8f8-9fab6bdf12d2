/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.cont.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomPage;
import com.tobacco.app.isale.domain.model.cont.order.ManageContOrderFromDayPlan;
import com.tobacco.app.isale.domain.model.cont.order.ManageOrderFromDayPlanPage;
import com.tobacco.app.isalecenter.client.dto.cont.order.ContOrderFromDayPlanDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/07/21 09:58
 * @description : 订单制定Mapper
 */
@Mapper
public interface ManageOrderStartMapper {
    /**
     * 查询订单(从日计划)列表(不包含从表)
     * @param page        分页查询条件
     * @param dayPlanPage 日计划查询条件
     * @param icomCode    工业编码
     * @return Page<ManageContOrderFromDayPlan>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-07-21 10:02:59
     * @description : 查询订单(从日计划)列表(不包含从表)
     */

    Page<ManageContOrderFromDayPlan> queryOrderPageFromDayPlan(
            CustomPage<ContOrderFromDayPlanDTO> page,
            @Param("dayPlanPage") ManageOrderFromDayPlanPage dayPlanPage,
            @Param("icomCode") String icomCode);

    /**
     * 根据日计划编码列表查询订单(从日计划)列表(包含从表)
     * @param dayPlanCodeList 日计划编码列表
     * @return List<ManageContOrderFromDayPlan>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-07-21 10:02:59
     * @description : 根据日计划编码列表查询订单(从日计划)列表(包含从表)
     */
    List<ManageContOrderFromDayPlan> queryOrderListFromDayPlan(List<String> dayPlanCodeList);

}
