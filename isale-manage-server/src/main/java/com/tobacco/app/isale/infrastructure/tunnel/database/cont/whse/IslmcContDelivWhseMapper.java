package com.tobacco.app.isale.infrastructure.tunnel.database.cont.whse;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomPage;
import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IslmcContDelivWhseMapper {

    Page<IslmcContDelivWhse> page(
            CustomPage<IslmcContDelivWhse> page,
            @Param("islmcContDelivWhse") IslmcContDelivWhse islmcContDelivWhse,
            @Param("icomCode") String icomCode);
}
