<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmSaleFoMapper">
    <insert id="createMc04IslmSalePlan">
        insert
        into
            mc04_islm_sale_plan(mc04_sale_plan_id,
                                ma02_toba_prod_trade_type_code,
                                za_occurrence_year,
                                mc04_plan_subject_type,
                                mc04_plan_subject_name,
                                mc04_plan_subject_begin_date,
                                mc04_plan_subject_end_date,
                                mc04_sale_plan_status,
                                mc04_cgt_sale_fo_period_type,
                                mc04_cgt_sale_fo_period_code,
                                mc04_org_type_kind,
                                mc04_org_type_code)
        values (#{planId},
                '0',
                #{year},
                #{mc04PlanSubjectType},
                #{mc04PlanSubjectName},
                #{startDate},
                #{endDate},
                '10',
                'T01',
                #{year},
                'QG',
                '1')
    </insert>
    <insert id="createMc04IslmBrandPlan">
        insert
        into
            mc04_islm_brand_plan(mc04_brand_plan_id,
                                 ma02_toba_prod_trade_type_code,
                                 mc04_brand_plan_version ,
                                 mc04_is_lastest_version ,
                                 za_occurrence_year,
                                 mc04_plan_subject_type,
                                 mc04_plan_subject_name,
                                 mc04_plan_subject_begin_date,
                                 mc04_plan_subject_end_date,
                                 mc04_plan_subject_status,
                                 mc04_cgt_sale_fo_period_type,
                                 mc04_cgt_sale_fo_period_code,
                                 mc04_org_type_kind,
                                 mc04_org_type_code,
                                 create_id,
                                 create_name,
                                 create_time,
                                 update_id,
                                 update_name,
                                 update_time)
        values (#{planId},
                '0',
                '1',
                '1',
                #{year},
                #{mc04PlanSubjectType},
                #{mc04PlanSubjectName},
                #{startDate},
                #{endDate},
                '0',
                'T01',
                #{year},
                'QG',
                '1',
                NULL,                -- create_id (creator ID)
                NULL,              -- create_name (creator name)
                NULL,         -- create_time (auto-generated timestamp)
                NULL,                      -- update_id (initially NULL)
                NULL,                      -- update_name (initially NULL)
                NULL)                       -- update_time (initially NULL))
    </insert>
    <insert id="createMc04IslmSaleFo">
        INSERT INTO mc04_islm_sale_fo (
            mc04_cgt_sale_fo_id,
            ma02_toba_prod_trade_type_code,
            mc04_cgt_sale_fo_version,
            mc04_is_lastest_version,
            za_occurrence_year,
            mc04_plan_subject_type,
            mc04_plan_subject_name,
            mc04_plan_subject_begin_date,
            mc04_plan_subject_end_date,
            mc04_plan_subject_status,
            mc04_cgt_sale_fo_year_sale_qty,
            create_id,
            create_name,
            create_time,
            update_id,
            update_name,
            update_time
        ) VALUES (
                     #{mc04CgtSaleFoId},                  -- mc04_cgt_sale_fo_id (sequence number)
                     '0',                       -- ma02_toba_prod_trade_type_code (default)
                     '1',                       -- mc04_cgt_sale_fo_version (initial version)
                     'Y',                       -- mc04_is_lastest_version (Y/N, default 'Y')
                     #{year},                -- za_occurrence_year (subject year)
                     #{mc04PlanSubjectType},                -- mc04_plan_subject_type (subject type)
                     #{mc04PlanSubjectName},                -- mc04_plan_subject_name (subject name)
                     #{startDate},      -- mc04_plan_subject_begin_date (start date)
                     #{endDate},      -- mc04_plan_subject_end_date (end date)
                     '0',                       -- mc04_plan_subject_status (default status)
                     0,                         -- mc04_cgt_sale_fo_year_sale_qty (default 0)
                     NULL,                -- create_id (creator ID)
                     NULL,              -- create_name (creator name)
                     NULL,         -- create_time (auto-generated timestamp)
                     NULL,                      -- update_id (initially NULL)
                     NULL,                      -- update_name (initially NULL)
                     NULL                       -- update_time (initially NULL)
                 );
    </insert>
    <insert id="createMc04IslmYcSalePlan">
        insert
        into
            mc04_islm_yc_sale_plan(mc04_sale_plan_id,
                                   ma02_toba_prod_trade_type_code,
                                   za_occurrence_year,
                                   mc04_plan_subject_type,
                                   mc04_plan_subject_name,
                                   mc04_plan_subject_begin_date,
                                   mc04_plan_subject_end_date,
                                   mc04_sale_plan_status,
                                   mc04_cgt_sale_fo_period_type,
                                   mc04_cgt_sale_fo_period_code,
                                   mc04_org_type_kind,
                                   mc04_org_type_code,
                                   mc04_org_type_name)
        values (#{mc04SalePlanId},
                '1',
                #{year},
                #{mc04PlanSubjectType},
                concat(#{year}, '年元春销售调拨计划编制'),
                #{startDate},
                #{endDate},
                '10',
                'YC',
                #{year},
                'QG',
                '1',
                '全国')
    </insert>

    <select id="getSaleFolist"
            resultType="com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSaleFoDTO">
        select
        a.mc04_cgt_sale_fo_id,
        a.ma02_toba_prod_trade_type_code,
        a.mc04_plan_subject_name,
        a.za_occurrence_year,
        a.mc04_plan_subject_type,
        sum(b.mc04_cgt_sale_fo_sale_qty) mc04_cgt_sale_fo_sale_qty,
        a.mc04_plan_subject_status,
        a.create_name,
        a.create_time,
        a.mc04_plan_subject_begin_date,
        a.mc04_plan_subject_end_date
        from
        mc04_islm_sale_fo a
        left join mc04_islm_sale_fo_item b
        on a.mc04_cgt_sale_fo_id = b.mc04_cgt_sale_fo_id
        and a.za_occurrence_year = b.za_occurrence_year
        where
            1=1
        and a.icom_code = #{icomCode}
        -- 登录的工业公司编码
        -- and a.mz10_index_item_data_type = '2'
        -- 品牌规划数据
        and a.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
        -- 卷烟业务，controller层参数写死，卷烟业务是0，雪茄烟业务是1
        -- 列表页面只显示最新版本
        and a.mc04_is_lastest_version = '1'
        -- 查询条件中的开始结束年份
        <if test="startYear != null and startYear !='' and endYear != null and endYear !=''">
            AND a.za_occurrence_year BETWEEN #{startYear} AND #{endYear}
        </if>
        -- 查询条件中的类型
        <if test="type != null and type !=''">
            and a.mc04_plan_subject_type = #{type}
        </if>
        -- 查询条件中的状态
        <if test="status != null and status !=''">
            and a.mc04_plan_subject_status = #{status}
        </if>
        group by
        a.mc04_cgt_sale_fo_id,
        a.ma02_toba_prod_trade_type_code,
        a.mc04_plan_subject_name,
        a.za_occurrence_year,
        a.mc04_plan_subject_type,
        a.mc04_plan_subject_status,
        a.create_name,
        a.create_time,
        a.mc04_plan_subject_begin_date,
        a.mc04_plan_subject_end_date
        order by
        a.create_time desc
    </select>
    <select id="getLastThreeYearsData" resultType="java.util.Map">
        select a.ac_cgt_carton_code acCgtCartonCode,
               sum(a.md03_cgt_10th_com_coe_sale_qty_y_a) nationalSaleQty,
               sum(case when b.mc04_com_org_is_imported='0' then a.md03_cgt_10th_com_coe_sale_qty_y_a else 0 end) inSaleQty,
               sum(case when b.mc04_com_org_is_imported='1' then a.md03_cgt_10th_com_coe_sale_qty_y_a else 0 end) outSaleQty
        from mc04_ind_xy_org_cgt_purch_sale_stk_month a,mc04_ikc_com_org b
        where a.mc04_xy_org_code=b.ba_com_org_code
          and a.mc04_cgt_forfeiture_flag='1' -- 非罚没烟
          and a.mc04_purchase_sale_stk_month=concat(#{year},'08')  -- 查询年份12月的年累计销量
          and b.icom_code=#{icomCode} -- 登录的工业公司
        group by a.ac_cgt_carton_code
    </select>
    <select id="getNextYearData" resultType="java.util.Map">


        select b.ac_cgt_carton_code acCgtCartonCode,
               sum(case when a.mc04_org_type_kind='QG' then mc04_cgt_sale_plan_adjusted_qty else 0 end) nationalSaleQty,
               sum(case when a.mc04_org_type_kind='SN' then mc04_cgt_sale_plan_adjusted_qty else 0 end) intSaleQty,
               sum(case when a.mc04_org_type_kind='SW' then mc04_cgt_sale_plan_adjusted_qty else 0 end) outSaleQty
        from mc04_islm_sale_plan a,mc04_islm_sale_plan_item b
        where a.mc04_sale_plan_id=b.mc04_sale_plan_id
          and a.ma02_toba_prod_trade_type_code='0'
          and a.mc04_is_lastest_version='1'
          and a.za_occurrence_year=#{year}
          and a.mc04_plan_subject_type= #{subjectType}  --  上个sql查询出的阶段
          and a.mc04_cgt_sale_fo_period_type='T01'
          and a.mc04_cgt_sale_fo_period_code=#{year}
          and a.mc04_org_type_kind in ('QG','SN','SW')  -- 查询全国传QG,省内传SN,省外传SW
        group by b.ac_cgt_carton_code

    </select>
    <select id="getTotalSumIndex" resultType="com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSumIndexDTO">

        select
            b.mc04_target_index_id,
            b.mc04_target_index_name,
            b.mc04_target_index_type,
            b.mc04_target_index_content,
            b.mz10_conf_field_name
        from
            mc04_islm_resource_sum_index a,
            mc04_islm_sum_index b
        where
            a.mc04_target_index_id=b.mc04_target_index_id
        and a.mz12_info_system_resource_code= #{mz12InfoSystemResourceCode}
        order by a.seq
    </select>
</mapper>