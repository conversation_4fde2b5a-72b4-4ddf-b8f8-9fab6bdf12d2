<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.ycplan.IslmYcSalePlanMapper">

    <select id="getYearEndStkQtySnSw"
            resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">
        SELECT
            ac_cgt_carton_code AS productCode,
            SUM(CASE WHEN org.MC04_COM_ORG_IS_IMPORTED = 0 THEN mc04_mg_cgt_10th_eom_stk_qty ELSE 0 END) AS yearEndStockSn,
            SUM(CASE WHEN org.MC04_COM_ORG_IS_IMPORTED = 1 THEN mc04_mg_cgt_10th_eom_stk_qty ELSE 0 END) AS yearEndStockSw
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month main
                JOIN
            mc04_ikc_com_org org ON main.mc04_xy_org_code = org.BA_COM_ORG_CODE
        WHERE
            main.mc04_cgt_forfeiture_flag = '1'
          AND main.mc04_purchase_sale_stk_month = CONCAT(#{year}, 12)
          AND org.ICOM_CODE = '20420001'
          AND org.MC04_COM_ORG_IS_XY = 1
        GROUP BY
            ac_cgt_carton_code
    </select>

    <select id="getSnSwJanAndFebMaxEndStkQty"
            resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">

        SELECT
            ac_cgt_carton_code AS productCode,
            SUM( CASE WHEN org.MC04_COM_ORG_IS_IMPORTED = 0 THEN mc04_mg_cgt_10th_eom_stk_qty ELSE 0 END ) AS febEndStockSn,
            SUM( CASE WHEN org.MC04_COM_ORG_IS_IMPORTED = 1 THEN mc04_mg_cgt_10th_eom_stk_qty ELSE 0 END ) AS febEndStockSw,
            SUM( CASE WHEN org.MC04_COM_ORG_IS_IMPORTED = 0 THEN mc04_mg_cgt_10th_iom_stk_qty ELSE 0 END ) AS janEndStockSn,
            SUM( CASE WHEN org.MC04_COM_ORG_IS_IMPORTED = 1 THEN mc04_mg_cgt_10th_iom_stk_qty ELSE 0 END ) AS janEndStockSw
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month main
                JOIN mc04_ikc_com_org org ON main.mc04_xy_org_code = org.BA_COM_ORG_CODE
        WHERE
            main.mc04_cgt_forfeiture_flag = '1'
          AND main.mc04_purchase_sale_stk_month = CONCAT( #{year}, 02 )
          AND org.ICOM_CODE = '20420001'
          AND org.MC04_COM_ORG_IS_XY = 1
        GROUP BY
            ac_cgt_carton_code;

    </select>

    <select id="getProvinceLastYearEndStkQty" resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">
        SELECT
            main.ba_prov_org_code AS busiComCode,
            ac_cgt_carton_code AS productCode,
            SUM( mc04_mg_cgt_10th_eom_stk_qty ) AS yearEndStock
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month main
                JOIN mc04_ikc_com_org org ON main.mc04_xy_org_code = org.BA_COM_ORG_CODE
        WHERE
            main.mc04_cgt_forfeiture_flag = '1'
          AND main.mc04_purchase_sale_stk_month = CONCAT( #{year}, 12 )
          AND org.ICOM_CODE = '20420001'
          AND org.MC04_COM_ORG_IS_XY = 1
        GROUP BY
            ac_cgt_carton_code,
            main.ba_prov_org_code
    </select>

    <select id="getMonthSaleData" resultType="java.util.Map">
        SELECT
            SUM(md03_cgt_10th_com_coe_sale_qty_m_a) AS lastJanuarySales,
            SUM(md03_cgt_10th_com_coe_sale_amt_m_a) AS lastFebruarySales
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month main
                JOIN mc04_ikc_com_org org ON main.mc04_xy_org_code = org.BA_COM_ORG_CODE
        WHERE
            main.mc04_cgt_forfeiture_flag = '1'
          AND main.mc04_xy_org_code = #{baComOrgCode}
          AND main.ac_cgt_carton_code = #{acCgtCartonCode}
          AND main.mc04_purchase_sale_stk_month = #{monthCode}
          AND org.ICOM_CODE = '20420001'
          AND org.MC04_COM_ORG_IS_XY = 1
    </select>

    <select id="getSpecialProvinceMonthSaleData" resultType="java.util.Map">
        SELECT
            SUM(md03_cgt_10th_com_coe_sale_qty_m_a) AS lastJanuarySales,
            SUM(md03_cgt_10th_com_coe_sale_amt_m_a) AS lastFebruarySales
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month main
                JOIN mc04_ikc_com_org org ON main.mc04_xy_org_code = org.BA_COM_ORG_CODE
        WHERE
            main.mc04_cgt_forfeiture_flag = '1'
          AND main.mc04_xy_org_code = #{baComOrgCode}
          AND main.ac_cgt_carton_code = #{acCgtCartonCode}
          AND main.mc04_purchase_sale_stk_month = #{yearMonth}
          AND org.ICOM_CODE = '20420001'
          AND org.MC04_COM_ORG_IS_XY = 1
    </select>

    <select id="getNormalProvinceMonthSaleData" resultType="java.util.Map">
        SELECT
            SUM(md03_cgt_10th_com_coe_sale_qty_m_a) AS lastJanuarySales,
            SUM(md03_cgt_10th_com_coe_sale_amt_m_a) AS lastFebruarySales
        FROM
            mc04_ind_xy_org_cgt_purch_sale_stk_month main
                JOIN mc04_ikc_com_org org ON main.mc04_xy_org_code = org.BA_COM_ORG_CODE
        WHERE
            main.mc04_cgt_forfeiture_flag = '1'
          AND main.mc04_purchase_sale_stk_month = #{yearMonth}
          AND org.ICOM_CODE = '20420001'
          AND org.MC04_COM_ORG_IS_XY = 1
          AND org.BA_PROV_ORG_CODE = #{baComOrgCode}
          AND org.MC04_COM_ORG_LEVEL = '02'
          AND main.ac_cgt_carton_code = #{acCgtCartonCode}
        GROUP BY
            main.ba_prov_org_code
    </select>
</mapper>