<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.basic.IslmcForwardWhseComMapper">

    <select id="queryIslmcContDelivWhse" resultType="com.inspur.ind.tree.entity.ComTree">
        SELECT
            'all' id,
            '前置库' title,
            '-1' parent_id
        FROM dual
        union all
        SELECT
            md02_cgt_out_storehouse_code id,
            cb_cb_logt_whse_name_abbrev title,
            'all' parent_id
        FROM mc04_islmc_cont_deliv_whse
        WHERE cb_logt_whse_busi_type1 in ('1', '2')
          and icom_code = #{icomCode}
    </select>

</mapper>
