<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmSalePlanMapper">


    <select id="getAllProductSaleAndAllotQtyListForYearSnSw" resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">
        SELECT
            ac_cgt_carton_code AS productCode,
            SUM( CASE WHEN plan.mc04_org_type_kind = 'SN' THEN mc04_cgt_sale_plan_adjusted_qty ELSE 0 END ) AS saleQtySn,
            SUM( CASE WHEN plan.mc04_org_type_kind = 'SW' THEN mc04_cgt_sale_plan_adjusted_qty ELSE 0 END ) AS saleQtySw,
            SUM( CASE WHEN plan.mc04_org_type_kind = 'SN' THEN ma02_cgt_pl_adjusted_qty ELSE 0 END ) AS allotQtySn,
            SUM( CASE WHEN plan.mc04_org_type_kind = 'SW' THEN ma02_cgt_pl_adjusted_qty ELSE 0 END ) AS allotQtySw
        FROM
            mc04_islm_sale_plan_item item
                JOIN mc04_islm_sale_plan plan ON item.mc04_sale_plan_id = plan.mc04_sale_plan_id
        WHERE
            plan.mc04_is_lastest_version = '1'
          AND plan.mc04_cgt_sale_fo_period_type = 'T01'
          AND plan.mc04_org_type_kind IN ( 'SN', 'SW' )
          AND plan.za_occurrence_year = #{year}
        GROUP BY
            ac_cgt_carton_code;
    </select>
    <select id="getAllProductEndStkSaleAndAllotQtyListForYearProvince"
            resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">
        SELECT
            ac_cgt_carton_code AS productCode,
            mc04_org_type_code AS busiComCode,
            SUM( mc04_cgt_sale_plan_adjusted_qty ) AS saleQty,
            SUM( ma02_cgt_pl_adjusted_qty ) AS allotQty,
            SUM( md03_cgt_10th_com_init_stk_qty ) AS initStock,
            SUM( md03_cgt_10th_com_end_stk_qty ) AS endStock
        FROM
            mc04_islm_sale_plan_item item
                JOIN mc04_islm_sale_plan plan ON item.mc04_sale_plan_id = plan.mc04_sale_plan_id
        WHERE
            plan.mc04_is_lastest_version = '1'
          AND plan.mc04_cgt_sale_fo_period_type = 'T01'
          AND plan.mc04_org_type_kind IN ( '01' )
          AND plan.za_occurrence_year = #{year}
        GROUP BY
            ac_cgt_carton_code,
            mc04_org_type_code
    </select>

</mapper>
