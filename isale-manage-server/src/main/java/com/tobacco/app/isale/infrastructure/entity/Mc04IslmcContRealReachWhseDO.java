/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 公司实际到货仓库表
 *
 * @Author: jinfuli
 * @Since: 2025-07-31
 * @Email: <EMAIL>
 * @Create: 2025-07-31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_cont_real_reach_whse")
public class Mc04IslmcContRealReachWhseDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 卷烟交易合同交货站点编码
             */

            @TableId(value = "mc04_cgt_pra_in_storehouse_code", type = IdType.NONE)
    @Field(value = "卷烟交易合同交货站点编码", name = "卷烟交易合同交货站点编码")
    private String mc04CgtPraInStorehouseCode;
                                /**
             * 商业公司编码
             */

    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;
                                /**
             * 交货站点主数据编码
             */

    @Field(value = "交货站点主数据编码", name = "交货站点主数据编码")
    private String baDelivSiteCode;
                                /**
             * 默认站点标识
             */

    @Field(value = "默认站点标识", name = "默认站点标识")
    private String baDefaultSiteFlag;
                                /**
             * 是否使用 0:停用,1:启用
             */

    @Field(value = "是否使用 0:停用,1:启用", name = "是否使用 0:停用,1:启用")
    private String isUse;
                                                                                                    

}
