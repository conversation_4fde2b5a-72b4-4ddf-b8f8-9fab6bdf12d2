<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmSalePlanItemMapper">

    <select id="itemListBySalePlanId" resultType="java.util.Map">
        SELECT
        s.ac_cgt_carton_code,
        s.ac_cgt_name,
        s.mc04_cgt_prov_product_flag,
        s.mc04_xy_org_code,
        s.mc04_xy_org_short_name,

        <!-- N-1年(上一年)数据 -->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{previousYear}
        THEN s.md03_cgt_10th_com_end_stk_qty ELSE 0 END) as n1_year_end_stock,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{previousYear}
        THEN s.mc04_cgt_xy_carry_over_qty ELSE 0 END) as n1_year_end_carry_over,

        <!-- N年(当年)数据 -->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        THEN s.md03_cgt_10th_com_coe_sale_qty_y_a ELSE 0 END) as n_year_sale_plan,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        THEN s.ma02_cgt_com_from_ind_buy_qty_y_a ELSE 0 END) as n_year_allocation_plan,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        THEN s.mc04_mg_cgt_10th_eom_stk_qty ELSE 0 END) as n_year_expected_stock,

        <!-- 上半年数据(当年) -->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter IN (CONCAT(#{currentYear}, 'Q1'), CONCAT(#{currentYear}, 'Q2'))
        THEN s.md03_cgt_10th_com_coe_sale_qty_q_a ELSE 0 END) as first_half_year_sale_estimate,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter IN (CONCAT(#{currentYear}, 'Q1'), CONCAT(#{currentYear}, 'Q2'))
        THEN s.ma02_cgt_com_from_ind_buy_qty_q_a ELSE 0 END) as first_half_year_allocation_estimate,

        <!-- 季度数据(当年) Q1-->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q1')
        THEN s.md03_cgt_10th_com_coe_sale_qty_q_a ELSE 0 END) as first_quarter_sale_estimate,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q1')
        THEN s.ma02_cgt_com_from_ind_buy_qty_q_a ELSE 0 END) as first_quarter_allocation_estimate,

        <!-- 季度数据(当年) Q2-->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q2')
        THEN s.md03_cgt_10th_com_coe_sale_qty_q_a ELSE 0 END) as first_quarter_sale_estimate,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q2')
        THEN s.ma02_cgt_com_from_ind_buy_qty_q_a ELSE 0 END) as first_quarter_allocation_estimate,

        <!-- 季度数据(当年) Q3-->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q3')
        THEN s.md03_cgt_10th_com_coe_sale_qty_q_a ELSE 0 END) as first_quarter_sale_estimate,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q3')
        THEN s.ma02_cgt_com_from_ind_buy_qty_q_a ELSE 0 END) as first_quarter_allocation_estimate,

        <!-- 季度数据(当年) Q4-->
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q4')
        THEN s.md03_cgt_10th_com_coe_sale_qty_q_a ELSE 0 END) as first_quarter_sale_estimate,
        SUM(CASE WHEN s.mc04_purchase_sale_stk_year = #{currentYear}
        AND s.mc04_purchase_sale_stk_quarter = CONCAT(#{currentYear}, 'Q4')
        THEN s.ma02_cgt_com_from_ind_buy_qty_q_a ELSE 0 END) as first_quarter_allocation_estimate,
        FROM
        mc04_ind_xy_org_cgt_purch_sale_stk_month s
        WHERE
        (s.mc04_purchase_sale_stk_year = #{currentYear}
        OR s.mc04_purchase_sale_stk_year = #{previousYear})
        AND EXISTS (
        SELECT 1 FROM mc04_islm_sale_plan_item i
        WHERE i.ac_cgt_carton_code = s.ac_cgt_carton_code
        AND i.mc04_sale_plan_id = #{mc04SalePlanId}
        )
        GROUP BY
        s.ac_cgt_carton_code,
        s.ac_cgt_name,
        s.mc04_cgt_prov_product_flag,
        s.mc04_xy_org_code,
        s.mc04_xy_org_short_name
    </select>
</mapper>