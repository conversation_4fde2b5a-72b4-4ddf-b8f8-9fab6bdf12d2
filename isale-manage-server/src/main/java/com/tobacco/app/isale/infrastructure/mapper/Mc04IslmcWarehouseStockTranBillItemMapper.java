/*
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 *  版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcWarehouseStockTranBillItemDO;
import org.apache.ibatis.annotations.Param;


/**
 * @Description: Mapper 接口
 *
 * @Author: reny<PERSON><PERSON>
 * @Since: 2025-05-21
 * @Email: <EMAIL>
 * @Create: 2025-05-21
 */
public interface Mc04IslmcWarehouseStockTranBillItemMapper extends BaseMapper<Mc04IslmcWarehouseStockTranBillItemDO> {

    void removeItem(@Param("billId") String billId,@Param("icomCode") String icomCode);
}