<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Copyright (C) 2024 Inspur
  ~ All rights reserved.
  ~ <p>
  ~  版权所有 (C) 浪潮软件股份有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.cont.transfer.TransferOrderMapper">

    <select id="queryTransferData" resultType="com.tobacco.app.isale.domain.model.cont.transfer.WarehouseStockTranBill">
        select
        iwstb.mc04_warehouse_stock_tran_bill_id,
        iwstb.ba_com_org_code,
        iwstb.md02_cgt_out_storehouse_code,
        icdw.md02_cgt_out_storehouse_name,
        iwstb.md02_cgt_in_storehouse_code,
        icdw2.md02_cgt_out_storehouse_name md02_cgt_in_storehouse_name,
        iwstb.mc05_prepos_move_pl_move_date,
        iwstb.mc04_ask_come_date,
        iwstb.ma02_plan_month,
        iwstb.create_name,
        iwstb.create_time,
        iwstb.mc04_warehouse_stock_tran_bill_status,
        iwstb.za_remark,
        coalesce(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty , 0) qty,
        ROUND(
        CAST(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0) AS DECIMAL(19,4)) *
        CAST(COALESCE(iwstbi.ac_cgt_tax_allot_price, 0) AS DECIMAL(19,4)),
        2
        ) AS totalAmount
        from
        mc04_islmc_warehouse_stock_tran_bill iwstb
        inner join mc04_islmc_warehouse_stock_tran_bill_item iwstbi on
        iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
        inner join mc04_islmc_cont_deliv_whse icdw on
        iwstb.md02_cgt_out_storehouse_code = icdw.md02_cgt_out_storehouse_code
        inner join mc04_islmc_cont_deliv_whse icdw2
        on iwstb.md02_cgt_in_storehouse_code = icdw2.md02_cgt_out_storehouse_code
        where iwstb.ma02_toba_prod_trade_type_code = '0'
        and iwstb.is_delete = '0'
        <if test="queryParam.mc04DatePeriodBeginCode != null and queryParam.mc04DatePeriodBeginCode != '' and queryParam.mc04DatePeriodEndCode != null and queryParam.mc04DatePeriodEndCode != ''">
            and iwstb.mc05_prepos_move_pl_move_date between #{queryParam.mc04DatePeriodBeginCode} and
            #{queryParam.mc04DatePeriodEndCode}
        </if>
        <if test="queryParam.mc04WarehouseStockTranBillStatus != null and queryParam.mc04WarehouseStockTranBillStatus != ''">
            and iwstb.mc04_warehouse_stock_tran_bill_status in
            <foreach collection="queryParam.mc04WarehouseStockTranBillStatus.split(',')" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.ma02PlanBeginMonth != null and queryParam.ma02PlanBeginMonth != '' and queryParam.ma02PlanEndMonth != null and queryParam.ma02PlanEndMonth != ''">
            and iwstb.ma02_plan_month between #{queryParam.ma02PlanBeginMonth} and #{queryParam.ma02PlanEndMonth}
        </if>
        <if test="queryParam.baComOrgCode != null and queryParam.baComOrgCode != ''">
            and iwstb.ba_com_org_code in
            <foreach collection="queryParam.baComOrgCode.split(',')" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.mc04WarehouseStockTranBillId != null and queryParam.mc04WarehouseStockTranBillId != ''">
            and iwstb.mc04_warehouse_stock_tran_bill_id like concat('%', #{queryParam.mc04WarehouseStockTranBillId},'%')
        </if>
        group by
        iwstb.mc04_warehouse_stock_tran_bill_id,
        iwstb.ba_com_org_code,
        iwstb.md02_cgt_out_storehouse_code,
        icdw.md02_cgt_out_storehouse_name,
        iwstb.md02_cgt_in_storehouse_code,
        icdw2.md02_cgt_out_storehouse_name,
        iwstb.mc05_prepos_move_pl_move_date,
        iwstb.mc04_ask_come_date,
        iwstb.ma02_plan_month,
        iwstb.create_name,
        iwstb.create_time,
        iwstb.mc04_warehouse_stock_tran_bill_status
        order by
        iwstb.create_time desc
    </select>

    <select id="queryMonthPlanData" resultType="com.tobacco.app.isale.domain.model.cont.transfer.TransferMonthPlan">
        SELECT
        t1.ba_com_org_code,
        t1.icom_code,
        t1.ac_two_level_cig_code,
        t1.ac_two_level_cig_name,
        t1.ac_cgt_carton_code,
        t1.ac_cgt_tax_allot_price,
        t1.qty,
        COALESCE(t1.qty - t2.movedTransferQty, 0) surplQty,
        t3.noorderQty,
        t3.orderedQty,
        t3.storedQty,
        t3.routeQty
        FROM
        (
        SELECT
        msp.ba_com_org_code,
        msp.icom_code,
        msp.ac_two_level_cig_code,
        msp.ac_two_level_cig_name,
        msp.ac_cgt_carton_code,
        msp.ac_cgt_tax_allot_price,
        msp.ma02_cgt_pl_adjusted_qty qty
        FROM
        mc04_islm_month_sale_plan msp
        WHERE
        msp.ma02_plan_month = #{queryParam.ma02PlanMonth}
        )t1
        LEFT JOIN (
        SELECT
        wsti.ba_com_org_code,
        SUM(
        COALESCE ( wstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0 )) movedTransferQty
        FROM
        mc04_islmc_warehouse_stock_tran_bill wsti
        LEFT JOIN mc04_islmc_warehouse_stock_tran_bill_item wstbi ON wsti.mc04_warehouse_stock_tran_bill_id = wstbi.mc04_warehouse_stock_tran_bill_id
        WHERE
        wsti.mc04_warehouse_stock_tran_bill_status IN ( '30', '40' )
        AND wsti.ma02_plan_month = #{queryParam.ma02PlanMonth}
        )t2 ON t1.ba_com_org_code = t2.ba_com_org_code
        LEFT JOIN (
        SELECT
        co.ba_com_org_code,
        sum( CASE WHEN co.mc04_cgt_trade_cont_status &lt; '20' THEN coi.md02_cgt_trade_cont_qty ELSE 0 END ) noorderQty,
        sum( CASE WHEN co.mc04_cgt_trade_cont_status BETWEEN '20' AND '60' THEN coi.md02_cgt_trade_cont_qty ELSE 0 END ) orderedQty,
        sum( CASE WHEN co.mc04_cgt_trade_cont_status BETWEEN '50' AND '60' THEN coi.md02_cgt_trade_cont_qty ELSE 0 END ) storedQty,
        sum( CASE WHEN co.mc04_cgt_trade_cont_status BETWEEN '20' AND '40' THEN coi.md02_cgt_trade_cont_qty ELSE 0 END ) routeQty
        FROM
        mc04_islm_cont_order_item coi
        LEFT JOIN mc04_islm_cont_order co ON co.mc04_cont_order_id = coi.mc04_cont_order_id
        WHERE
        co.ma02_plan_month = #{queryParam.ma02PlanMonth}
        )t3
        ON t1.ba_com_org_code = t3.ba_com_org_code
        WHERE
        t1.icom_code = #{queryParam.icomCode}
        <if test="queryParam.baComOrgCode != null and queryParam.baComOrgCode != ''">
            AND t1.ba_com_org_code in
            <foreach collection="queryParam.baComOrgCode.split(',')" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        t1.ba_com_org_code,
        t1.ac_two_level_cig_code,
        t1.ac_cgt_tax_allot_price
    </select>

    <select id="queryStockData" resultType="com.tobacco.app.isale.domain.model.cont.transfer.Stock">
        SELECT
        t1.md02_cgt_out_storehouse_code,
        t1.md02_cgt_out_storehouse_name,
        t1.ac_cgt_carton_code,
        t1.mc04_cgt_init_stk_qty initQty,
        t1.mc04_cgt_init_stk_act_qty initActQty,
        t1.mc04_cgt_end_stk_qty endQty,
        t1.mc04_cgt_end_stk_act_qty endActQty,
        t1.daySellOutQty dayOutSumQty,
        t1.monthSellOutQty monthOutSumQty,
        t1.yearSellOutQty yearOutSumQty,
        t2.dayContQty,
        t2.monthContQty,
        t2.yearContQty,
        <if test="'10'.toString().equals(queryParam.countType)">
            t1.ac_one_level_cig_code cigCode
        </if>
        <if test="'20'.toString().equals(queryParam.countType)">
            t2.ac_two_level_cig_code cigCode,
            t2.ac_two_level_cig_name cigName
        </if>
        <if test="'30'.toString().equals(queryParam.countType)">
            t2.ac_thr_level_cig_code cigCode,
            t2.ac_thr_level_cig_name cigName
        </if>
        FROM
        (
        SELECT
        icsd.md02_cgt_out_storehouse_code,
        icdw.md02_cgt_out_storehouse_name,
        icsd.ac_cgt_carton_code,
        icsd.ac_one_level_cig_code,
        icsd.mc04_cgt_init_stk_qty,
        icsd.mc04_cgt_init_stk_act_qty,
        icsd.mc04_cgt_end_stk_qty,
        icsd.mc04_cgt_end_stk_act_qty,
        ROUND( sum( IF ( icsd.za_bill_date = #{queryParam.businessDate}, COALESCE ( icsd.mc05_cgt_10th_ind_sellout_qty,
        0 ), 0 )), 4 ) AS daySellOutQty,
        ROUND( sum( IF ( icsd.za_bill_date BETWEEN #{queryParam.monthBeginDate} AND #{queryParam.monthEndDate}, COALESCE
        ( icsd.mc05_cgt_10th_ind_sellout_qty, 0 ), 0 )), 4 ) AS monthSellOutQty,
        ROUND( sum( IF ( icsd.za_bill_date BETWEEN #{queryParam.yearBeginDate} AND #{queryParam.yearEndDate}, COALESCE (
        icsd.mc05_cgt_10th_ind_sellout_qty, 0 ), 0 )), 4 ) AS yearSellOutQty
        FROM
        mc04_islmc_cgt_stock_day icsd
        LEFT JOIN mc04_islmc_cont_deliv_whse icdw ON icsd.md02_cgt_out_storehouse_code =
        icdw.md02_cgt_out_storehouse_code
        ) t1
        LEFT JOIN (
        SELECT
        mico.md04_cgt_order_time,
        mico.md02_cgt_out_storehouse_code,
        mico.icom_code,
        mico.ba_com_org_code,
        micoi.ac_cgt_carton_code,
        micoi.ac_two_level_cig_code,
        micoi.ac_two_level_cig_name,
        micoi.ac_thr_level_cig_code,
        micoi.ac_thr_level_cig_name,
        ROUND(
        sum(
        IF
        ( substring( mico.md04_cgt_order_time, 1, 8 ) = #{queryParam.businessDate}, COALESCE (
        micoi.md02_cgt_trade_cont_qty, 0 ), 0 )),
        4
        ) AS dayContQty,
        ROUND(
        sum(
        IF
        ( substring( mico.md04_cgt_order_time, 1, 8 ) BETWEEN #{queryParam.monthBeginDate} AND
        #{queryParam.monthEndDate}, COALESCE ( micoi.md02_cgt_trade_cont_qty, 0 ), 0 )),
        4
        ) AS monthContQty,
        ROUND(
        sum(
        IF
        ( substring( mico.md04_cgt_order_time, 1, 8 ) BETWEEN #{queryParam.yearBeginDate} AND #{queryParam.yearEndDate},
        COALESCE ( micoi.md02_cgt_trade_cont_qty, 0 ), 0 )),
        4
        ) AS yearContQty
        FROM
        mc04_islm_cont_order mico
        LEFT JOIN mc04_islm_cont_order_item micoi ON mico.mc04_cont_order_id = micoi.mc04_cont_order_id
        ) t2 ON t1.ac_cgt_carton_code = t2.ac_cgt_carton_code
        LEFT JOIN
        mc04_islmc_cont_deliv_whse icdw
        on t1.md02_cgt_out_storehouse_code = icdw.md02_cgt_out_storehouse_code
        WHERE
        t2.icom_code = #{queryParam.icomCode}
        <if test="queryParam.baComOrgCode != null and queryParam.baComOrgCode != ''">
            and t2.ba_com_org_code in
            <foreach collection="queryParam.baComOrgCode.split(',')" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.whseType != null and queryParam.whseType != '' and queryParam.whseType == '10'.toString()">
            AND icdw.cb_logt_whse_busi_type1 = '4'
        </if>
        <if test="queryParam.whseType != null and queryParam.whseType != '' and queryParam.whseType == '20'.toString()">
            AND icdw.cb_logt_whse_busi_type1 in ('1','2')
        </if>
        <if test="'10'.toString().equals(queryParam.countType)">
            group by t1.md02_cgt_out_storehouse_code, t1.ac_one_level_cig_code
        </if>
        <if test="'20'.toString().equals(queryParam.countType)">
            group by t1.md02_cgt_out_storehouse_code, t2.ac_two_level_cig_code
        </if>
        <if test="'30'.toString().equals(queryParam.countType)">
            group by t1.md02_cgt_out_storehouse_code, t2.ac_thr_level_cig_code
        </if>
    </select>

    <select id="getTransferOrderDetail"
            resultType="com.tobacco.app.isale.domain.model.cont.transfer.TransferOrderDetail">
        SELECT
        t1.ac_cgt_carton_code,
        t1.ac_two_level_cig_code,
        t1.ac_two_level_cig_name,
        t1.qty transferQty,
        t4.ma02_logt_ic_tray_pallet_trans_qty palletTransQty,
        t4.md03_logt_tray_comb_tsp_tray_type palletType,
        t1.ac_cgt_name,
        COALESCE(t2.qty, 0) movedTranQty,
        COALESCE(t3.qty, 0) half_year_tran_qty
        FROM
            (SELECT
            iwstbi.ac_two_level_cig_code,
            iwstbi.ac_cgt_carton_code,
            iwstbi.ac_cgt_name,
            iwstbi.ac_two_level_cig_name,
            iwstbi.ac_cgt_tax_allot_price,
            SUM(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0)) qty
            FROM
            mc04_islmc_warehouse_stock_tran_bill iwstb,
            mc04_islmc_warehouse_stock_tran_bill_item iwstbi
            WHERE
            iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
            AND iwstb.mc04_warehouse_stock_tran_bill_id = #{queryParam.mc04WarehouseStockTranBillId}
            GROUP BY
            iwstbi.ac_two_level_cig_code,
            iwstbi.ac_cgt_carton_code,
            iwstbi.ac_cgt_name
            ) t1
        LEFT JOIN (
        SELECT
        iwstbi.ac_two_level_cig_code,
        SUM(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0)) qty
        FROM
        mc04_islmc_warehouse_stock_tran_bill iwstb
        JOIN mc04_islmc_warehouse_stock_tran_bill_item iwstbi
        ON iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
        WHERE
        iwstb.ma02_plan_month = #{queryParam.endMonth}
        AND iwstb.ma02_toba_prod_trade_type_code = '0'
        AND iwstb.ba_com_org_code = #{queryParam.baComOrgCode}
        AND iwstb.icom_code = #{queryParam.icomCode}
        AND iwstb.mc04_warehouse_stock_tran_bill_status IN ('30','40')
        GROUP BY
        iwstbi.ac_two_level_cig_code
        ) t2 ON t1.ac_two_level_cig_code = t2.ac_two_level_cig_code
        LEFT JOIN (
        SELECT
        iwstbi.ac_two_level_cig_code,
        SUM(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0)) qty
        FROM
        mc04_islmc_warehouse_stock_tran_bill iwstb,
        mc04_islmc_warehouse_stock_tran_bill_item iwstbi
        WHERE
        iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
        AND iwstb.ma02_plan_month BETWEEN #{queryParam.beginMonth} AND #{queryParam.endMonth}
        AND iwstb.ma02_toba_prod_trade_type_code = '0'
        AND iwstb.ba_com_org_code = #{queryParam.baComOrgCode}
        AND iwstb.icom_code = #{queryParam.icomCode}
        AND iwstb.mc04_warehouse_stock_tran_bill_status IN ('30','40')
        GROUP BY
        iwstbi.ac_two_level_cig_code
        ) t3 ON t1.ac_two_level_cig_code = t3.ac_two_level_cig_code
        left join (
        SELECT
        icdwit.ac_two_level_cig_code,
        icdwit.ma02_logt_ic_tray_pallet_trans_qty,
        icdwit.md03_logt_tray_comb_tsp_tray_type
        FROM
        mc04_islmc_cont_deliv_whse_item_tray icdwit,
        mc04_islmc_warehouse_stock_tran_bill iwstb
        WHERE
        icdwit.md02_cgt_out_storehouse_code = iwstb.md02_cgt_out_storehouse_code
        AND iwstb.mc04_warehouse_stock_tran_bill_id = #{queryParam.mc04WarehouseStockTranBillId}
         )t4 on t1.ac_two_level_cig_code = t4.ac_two_level_cig_code
    </select>

    <select id="getTransferOrderDetailInAdd"
            resultType="com.tobacco.app.isale.domain.model.cont.transfer.TransferOrderDetail">
        SELECT
        t1.ac_cgt_carton_code,
        t1.ac_two_level_cig_code,
        t4.md03_logt_tray_comb_tsp_tray_type palletType,
        t4.ma02_logt_ic_tray_pallet_trans_qty palletTransQty,
        t5.qzkQty,
        t6.jbkQty,
        t8.weekMa02CgtPlQty,
        COALESCE(t2.qty, 0) movedTranQty,
        COALESCE(t7.qty, 0) weekMovedMa02CgtPlQty,
        COALESCE(t3.qty, 0) halfYearTranQty
        FROM
        (SELECT ac_two_level_cig_code,
                ac_cgt_carton_code,
                md03_logt_tray_comb_tsp_tray_type
        FROM mc04_islmc_com_item
        WHERE ba_com_org_code = #{queryParam.baComOrgCode}
        AND icom_code = #{queryParam.icomCode}
        AND ac_one_level_class_type_code = '01'
        ) t1
        LEFT JOIN (
        SELECT
        iwstbi.ac_two_level_cig_code,
        SUM(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0)) qty
        FROM
        mc04_islmc_warehouse_stock_tran_bill iwstb
        JOIN mc04_islmc_warehouse_stock_tran_bill_item iwstbi
        ON iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
        WHERE
        iwstb.ma02_plan_month = #{queryParam.ma02PlanMonth}
        AND iwstb.ma02_toba_prod_trade_type_code = '0'
        AND iwstb.ba_com_org_code = #{queryParam.baComOrgCode}
        AND iwstb.icom_code = #{queryParam.icomCode}
        AND iwstb.mc04_warehouse_stock_tran_bill_status IN ('30','40')
        GROUP BY
        iwstbi.ac_two_level_cig_code
        ) t2 ON t1.ac_two_level_cig_code = t2.ac_two_level_cig_code
        LEFT JOIN (
            SELECT
                iwstbi.ac_two_level_cig_code,
                SUM(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0)) qty
            FROM
                mc04_islmc_warehouse_stock_tran_bill iwstb
                    JOIN mc04_islmc_warehouse_stock_tran_bill_item iwstbi
                         ON iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
            WHERE
                iwstb.ma02_plan_month = #{queryParam.mc04DatePeriodCode}
              AND iwstb.ma02_toba_prod_trade_type_code = '0'
              AND iwstb.ba_com_org_code = #{queryParam.baComOrgCode}
              AND iwstb.icom_code = #{queryParam.icomCode}
              AND iwstb.mc04_warehouse_stock_tran_bill_status IN ('30','40')
            GROUP BY
                iwstbi.ac_two_level_cig_code
        ) t7 ON t1.ac_two_level_cig_code = t7.ac_two_level_cig_code
        LEFT JOIN (
        SELECT
        iwstbi.ac_two_level_cig_code,
        SUM(COALESCE(iwstbi.md03_logt_tray_comb_tsp_ic_rlc_qty, 0)) qty
        FROM
        mc04_islmc_warehouse_stock_tran_bill iwstb,
        mc04_islmc_warehouse_stock_tran_bill_item iwstbi
        WHERE
        iwstb.mc04_warehouse_stock_tran_bill_id = iwstbi.mc04_warehouse_stock_tran_bill_id
        AND iwstb.ma02_plan_month BETWEEN #{queryParam.beginMonth} AND #{queryParam.endMonth}
        AND iwstb.ma02_toba_prod_trade_type_code = '0'
        AND iwstb.ba_com_org_code = #{queryParam.baComOrgCode}
        AND iwstb.icom_code = #{queryParam.icomCode}
        AND iwstb.mc04_warehouse_stock_tran_bill_status IN ('30','40')
        GROUP BY
        iwstbi.ac_two_level_cig_code
        ) t3 ON t1.ac_two_level_cig_code = t3.ac_two_level_cig_code
        LEFT JOIN (
        SELECT
        icdwit.ac_two_level_cig_code,
        icdwit.ma02_logt_ic_tray_pallet_trans_qty,
        icdwit.md03_logt_tray_comb_tsp_tray_type
        FROM
        mc04_islmc_cont_deliv_whse_item_tray icdwit
        WHERE
        icdwit.md02_cgt_out_storehouse_code = #{queryParam.md02CgtOutStorehouseCode}
        AND icdwit.icom_code = #{queryParam.icomCode}
        )t4 on t1.ac_two_level_cig_code = t4.ac_two_level_cig_code
        LEFT JOIN (
            SELECT
                ac_two_level_cig_code,
                mc05_cig_free_stk_qty qzkQty
            FROM
                mc04_islmc_cgt_stock
            WHERE
                md02_cgt_out_storehouse_code = #{queryParam.md02CgtInStorehouseCode}
        )t5 on t1.ac_two_level_cig_code = t5.ac_two_level_cig_code
        LEFT JOIN (
            SELECT
                ac_two_level_cig_code,
                mc05_cig_free_stk_qty jbkQty
            FROM
                mc04_islmc_cgt_stock
            WHERE
                md02_cgt_out_storehouse_code = #{queryParam.md02CgtOutStorehouseCode}
        )t6 on t1.ac_two_level_cig_code = t6.ac_two_level_cig_code
        LEFT JOIN (
            SELECT
                ma02_cgt_pl_qty weekMa02CgtPlQty,
                ac_two_level_cig_code
            FROM
                mc04_islm_week_sale_plan
            WHERE
                ma02_toba_prod_trade_type_code = '0'
            AND ba_com_org_code = #{queryParam.baComOrgCode}
            AND ma02_plan_month = #{queryParam.ma02PlanMonth}
            AND mc04_date_period_code = #{queryParam.mc04DatePeriodCode}
        )t8 on t1.ac_two_level_cig_code = t8.ac_two_level_cig_code
    </select>

    <select id="queryPallet" resultType="com.tobacco.app.isale.domain.model.cont.transfer.TransferOrderPallet">
        SELECT t1.ba_com_org_code,
               t1.ac_two_level_cig_code,
               t1.md03_logt_tray_comb_tsp_tray_type,
               t3.ma02_logt_ic_tray_pallet_trans_qty
        FROM (SELECT ba_com_org_code,
                     ac_two_level_cig_code,
                     md03_logt_tray_comb_tsp_tray_type
              FROM mc04_islmc_com_item ci
              WHERE ci.ac_one_level_class_type_code = '01'
                AND ci.is_use = '1'
                AND ci.ba_com_org_code = #{baComOrgCode}
                AND ci.icom_code = #{icomCode}) t1
                 INNER JOIN (SELECT ba_com_org_code,
                                    md02_cgt_out_storehouse_code
                             FROM mc04_islmc_forward_whse_com
                             WHERE ba_com_org_code = #{baComOrgCode}) t2 ON t1.ba_com_org_code = t2.ba_com_org_code
                 INNER JOIN (SELECT md02_cgt_out_storehouse_code,
                                    ac_cgt_carton_code,
                                    ac_two_level_cig_code,
                                    ma02_logt_ic_tray_pallet_trans_qty
                             FROM mc04_islmc_cont_deliv_whse_item_tray
                             WHERE
                                 md03_logt_tray_comb_tsp_tray_type='1'
                             ) t3
                            ON t2.md02_cgt_out_storehouse_code = t3.md02_cgt_out_storehouse_code
                                AND t1.ac_two_level_cig_code = t3.ac_two_level_cig_code
    </select>
    <select id="queryWeekSelect" parameterType="com.tobacco.app.isale.domain.model.cont.transfer.TransferOrderWeekSelect"
            resultType="com.tobacco.app.isale.domain.model.cont.transfer.TransferOrderWeekSelect">
        SELECT
            t1.mc04_date_period_code,
            t2.mc04_date_period_name,
            t1.mc04_date_period_begin_date,
            t1.mc04_date_period_end_date,
            t1.ma02_plan_month,
            t1.ac_two_level_cig_code,
            t1.ma02_cgt_pl_qty
        FROM
            ( SELECT
                  mc04_date_period_code,
                  mc04_date_period_begin_date,
                  mc04_date_period_end_date,
                  ac_two_level_cig_code,
                  ma02_cgt_pl_qty,
                  ma02_plan_month
              FROM
                  mc04_islm_week_sale_plan
              WHERE
                  ba_com_org_code=#{queryParam.baComOrgCode}
                AND ma02_plan_month=#{queryParam.ma02PlanMonth}
            ) t1
                LEFT JOIN
            mc04_ind_data_period t2 ON t1.mc04_date_period_code = t2.mc04_date_period_code
        WHERE
            t2.mc04_date_period_type='T06'
        ORDER BY
            t1.mc04_date_period_code ASC
    </select>

    <update id="submit">
        update mc04_islmc_warehouse_stock_tran_bill
        set mc04_warehouse_stock_tran_bill_status = #{status}
        where mc04_warehouse_stock_tran_bill_id = #{billIds}
    </update>
    <update id="setPalletEnabled" parameterType="java.util.List">
        UPDATE mc04_islmc_com_item t1
        INNER JOIN mc04_islmc_forward_whse_com t2 ON t1.ba_com_org_code = t2.ba_com_org_code
        INNER JOIN mc04_islmc_cont_deliv_whse_item_tray t3
        ON t3.md02_cgt_out_storehouse_code = t2.md02_cgt_out_storehouse_code
        AND t1.ac_two_level_cig_code = t3.ac_two_level_cig_code
        SET
        t1.md03_logt_tray_comb_tsp_tray_type = CASE
        <foreach collection="list" item="item" separator="">
            WHEN t1.ac_two_level_cig_code = #{item.acTwoLevelCigCode}
            THEN #{item.md03LogtTrayCombTspTrayType}
        </foreach>
        ELSE t1.md03_logt_tray_comb_tsp_tray_type
        END
        WHERE
        t1.ba_com_org_code = #{baComOrgCode}
        AND t1.icom_code = #{icomCode}
        AND t3.md03_logt_tray_comb_tsp_tray_type = '1'
    </update>
</mapper>