/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有（C）浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.weekplan.weekplansubmit;

import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.IslmWeekPlanSubmitQueryResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/08/05 08:47
 * @description 周计划上报
 */
@Mapper
public interface IslmWeekPlanSubmitMapper {

    /**
     * 根据条件查询周计划数据
     *
     * @param planMonth        计划月份
     * @param authorizedComIds 授权公司编码列表
     * @param acCgtCartonCodes 商品编码列表
     * @return 周计划数据列表
     */
    List<IslmWeekPlanSubmitQueryResult> findPlansByCondition(@Param("planMonth") String planMonth, @Param("authorizedComIds") List<String> authorizedComIds, @Param("acCgtCartonCodes") List<String> acCgtCartonCodes);


    /**
     * 获取月计划总量
     *
     * @param planMonth 计划月份
     * @param acTwoLevelCigCodes 卷烟二级牌号编码
     * @param authorizedComIds 授权公司编码列表
     * @return 月计划总量
     */
    List<Map<String, Object>> getMonthlyPlanTotal(@Param("planMonth") String planMonth, @Param("acTwoLevelCigCodes") List<String> acTwoLevelCigCodes, @Param("authorizedComIds") List<String> authorizedComIds);

    /**
     * 获取解锁状态
     * @param planMonths 计划月份
     * @param comCodes 公司编码列表
     * @return Map<公司编码, 是否解锁>
     */
    List<Map<String, Object>> getUnlockStatus(@Param("planMonths") List<String> planMonths, @Param("comCodes") List<String> comCodes);

    /**
     * 获取全国计划总量
     * @param cigCodes 规格编码列表
     * @return Map<规格编码, 全国计划总量>
     */
    List<Map<String, Object>> getNationalPlanTotals(@Param("cigCodes") List<String> cigCodes);

    /**
     * 查询城市状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @param mc04SalePlanStatus
     * @return
     */
    List<Map<String, Object>> queryOrgStatus(String ma02PlanMonth, String icomCode, String mc04SalePlanStatus);
}
