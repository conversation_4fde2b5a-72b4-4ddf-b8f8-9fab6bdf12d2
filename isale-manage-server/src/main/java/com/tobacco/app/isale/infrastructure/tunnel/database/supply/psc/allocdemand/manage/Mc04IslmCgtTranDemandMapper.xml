<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.manage.Mc04IslmCgtTranDemandMapper">
    <select id="queryAllocdemandMangePage" resultType="com.tobacco.app.isale.dto.supply.psc.allocdemand.manage.AllocdemandMangeDTO">
        select
        -- 计划年月
        a.za_occurrence_month as zaOccurrenceMonth,
        -- 计划版本
        a.mc03_cgt_prod_plan_version as mc03CgtProdPlanVersion,
        -- 计划类型
        a.mc03_cgt_prod_pl_type as mc03CgtProdPlType,
        -- 调拨需求(累加值)
        sum(a.ma02_cgt_pl_adjusted_qty) as ma02CgtPlAdjustedQty,
        -- 制单人
        a.create_name as createName,
        -- 制单日期
        a.create_time as createTime,
        -- 状态
        a.mc04_cgt_tran_demand_status as mc04CgtTranDemandStatus
        from
        mc04_islm_cgt_tran_demand a
        where
        1=1
        -- 时间周期代码查询条件(可为空)
        <if test="zaOccurrenceMonth != null and zaOccurrenceMonth !=''">
            and a.za_occurrence_month = #{zaOccurrenceMonth}
        </if>

        -- 卷烟计划类型代码查询条件(可为空)
        <if test="mc03CgtProdPlType != null and mc03CgtProdPlType !=''">
            and a.mc03_cgt_prod_pl_type = #{mc03CgtProdPlType}
        </if>

        -- 按计划年月和版本分组，满足同一组的聚合需求
        group by
        a.za_occurrence_month,
        a.mc03_cgt_prod_plan_version,
        a.mc03_cgt_prod_pl_type,
        a.create_name,
        a.create_time,
        a.mc04_cgt_tran_demand_status

    </select>
</mapper>
