<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmYearPlanMapper">
    <insert id="createMc04IslmSalePlan">
        insert
        into
            mc04_islm_sale_plan(mc04_sale_plan_id,
                                ma02_toba_prod_trade_type_code,
                                mc04_cgt_sale_plan_version,
                                mc04_is_lastest_version,
                                za_occurrence_year,
                                mc04_plan_subject_type,
                                mc04_plan_subject_name,
                                mc04_plan_subject_begin_date,
                                mc04_plan_subject_end_date,
                                mc04_sale_plan_status,
                                mc04_cgt_sale_fo_period_type,
                                mc04_cgt_sale_fo_period_code,
                                mc04_org_type_kind,
                                mc04_org_type_code)
        values (#{planId},
                '0',
                #{version},
                '1'
                #{year},
                #{mc04PlanSubjectType},
                #{mc04PlanSubjectName},
                #{startDate},
                #{endDate},
                '10',
                'T01',
                #{year},
                'QG',
                '1')
    </insert>
    <insert id="createMc04IslmBrandPlan">
        insert
        into
            mc04_islm_brand_plan(mc04_brand_plan_id,
                                 ma02_toba_prod_trade_type_code,
                                 mc04_brand_plan_version ,
                                 mc04_is_lastest_version ,
                                 za_occurrence_year,
                                 mc04_plan_subject_type,
                                 mc04_plan_subject_name,
                                 mc04_plan_subject_begin_date,
                                 mc04_plan_subject_end_date,
                                 mc04_plan_subject_status,
                                 mc04_cgt_sale_fo_period_type,
                                 mc04_cgt_sale_fo_period_code,
                                 mc04_org_type_kind,
                                 mc04_org_type_code,
                                 create_id,
                                 create_name,
                                 create_time,
                                 update_id,
                                 update_name,
                                 update_time)
        values (#{planId},
                '0',
                '1',
                '1',
                #{year},
                #{mc04PlanSubjectType},
                #{mc04PlanSubjectName},
                #{startDate},
                #{endDate},
                '0',
                'T01',
                #{year},
                'QG',
                '1',
                NULL,                -- create_id (creator ID)
                NULL,              -- create_name (creator name)
                NULL,         -- create_time (auto-generated timestamp)
                NULL,                      -- update_id (initially NULL)
                NULL,                      -- update_name (initially NULL)
                NULL                       -- update_time (initially NULL))
    </insert>
    <insert id="createMc04IslmSaleFo">
        INSERT INTO mc04_islm_sale_fo (
            mc04_cgt_sale_fo_id,
            ma02_toba_prod_trade_type_code,
            mc04_cgt_sale_fo_version,
            mc04_is_lastest_version,
            za_occurrence_year,
            mc04_plan_subject_type,
            mc04_plan_subject_name,
            mc04_plan_subject_begin_date,
            mc04_plan_subject_end_date,
            mc04_plan_subject_status,
            mc04_cgt_sale_fo_year_sale_qty,
            create_id,
            create_name,
            create_time,
            update_id,
            update_name,
            update_time
        ) VALUES (
                     #{mc04CgtSaleFoId},                  -- mc04_cgt_sale_fo_id (sequence number)
                     '0',                       -- ma02_toba_prod_trade_type_code (default)
                     '1',                       -- mc04_cgt_sale_fo_version (initial version)
                     'Y',                       -- mc04_is_lastest_version (Y/N, default 'Y')
                     #{year},                -- za_occurrence_year (subject year)
                     #{mc04PlanSubjectType},                -- mc04_plan_subject_type (subject type)
                     #{mc04PlanSubjectName},                -- mc04_plan_subject_name (subject name)
                     #{startDate},      -- mc04_plan_subject_begin_date (start date)
                     #{endDate},      -- mc04_plan_subject_end_date (end date)
                     '0',                       -- mc04_plan_subject_status (default status)
                     0,                         -- mc04_cgt_sale_fo_year_sale_qty (default 0)
                     NULL,                -- create_id (creator ID)
                     NULL,              -- create_name (creator name)
                     NULL,         -- create_time (auto-generated timestamp)
                     NULL,                      -- update_id (initially NULL)
                     NULL,                      -- update_name (initially NULL)
                     NULL                       -- update_time (initially NULL)
                 );
    </insert>
    <insert id="createMc04IslmDemandFo">
        insert
        into
            mc04_islm_demand_fo(mc04_demand_fo_id,
                                ma02_toba_prod_trade_type_code,
                                ba_com_org_code,
                                za_occurrence_year,
                                mc04_plan_subject_type,
                                mc04_plan_subject_name,
                                mc04_plan_subject_begin_date,
                                mc04_plan_subject_end_date)
        values (#{mc04DemandFoId},
                '0', ,
                '协议单位编码',
                #{year},
                #{mc04PlanSubjectType},
                #{mc04PlanSubjectName},
                #{startDate},
                #{endDate})
    </insert>
    <insert id="createMc04IslmYcSalePlan">
        insert
        into
            mc04_islm_yc_sale_plan(mc04_sale_plan_id,
                                   ma02_toba_prod_trade_type_code,
                                   za_occurrence_year,
                                   mc04_plan_subject_type,
                                   mc04_plan_subject_name,
                                   mc04_plan_subject_begin_date,
                                   mc04_plan_subject_end_date,
                                   mc04_sale_plan_status,
                                   mc04_cgt_sale_fo_period_type,
                                   mc04_cgt_sale_fo_period_code,
                                   mc04_org_type_kind,
                                   mc04_org_type_code,
                                   mc04_org_type_name)
        values (#{mc04SalePlanId},
                '1',
                #{year},
                #{mc04PlanSubjectType},
                concat(#{year}, '年元春销售调拨计划编制'),
                #{startDate},
                #{endDate},
                '10',
                'YC',
                #{year},
                'QG',
                '1',
                '全国')
    </insert>

    <select id="getyearplanlist"
            resultType="com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSalePlanDTO">
        SELECT t1.mc04_plan_subject_id,
               t1.mc04_plan_subject_name,
               t1.za_occurrence_year,
               t1.mc04_plan_subject_type,
               t1.mc04_plan_subject_status,
               CASE
                   WHEN t2.mc04_plan_subject_id IS NOT NULL
                       AND t5.mc04_brand_plan_id IS NULL THEN '未完成'
                   WHEN t2.mc04_plan_subject_id IS NOT NULL
                       AND t5.mc04_brand_plan_id IS NOT NULL THEN '已完成'
                   WHEN t2.mc04_plan_subject_id IS NULL THEN '-'
                   END brand_plan_is_completed,
               CASE
                   WHEN t3.mc04_plan_subject_id IS NOT NULL
                       AND t6.mc04_cgt_sale_fo_id IS NULL THEN '未完成'
                   WHEN t3.mc04_plan_subject_id IS NOT NULL
                       AND t6.mc04_cgt_sale_fo_id IS NOT NULL THEN '已完成'
                   WHEN t3.mc04_plan_subject_id IS NULL THEN '-'
                   END sale_fo_is_completed,
               CASE
                   WHEN t4.mc04_plan_subject_id IS NOT NULL
                       AND COALESCE(t7.fo_count,
                                    0) != #{foCount} THEN '未完成'
                   WHEN t4.mc04_plan_subject_id IS NOT NULL
                       AND COALESCE(t7.fo_count,
                                    0) = #{foCount} THEN '已完成'
                   WHEN t4.mc04_plan_subject_id IS NULL THEN '-'
                   END demand_fo_is_completed,
               t1.create_name,
               t1.create_time
        FROM (SELECT *
              FROM mc04_islm_plan_subject
              WHERE ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}) t1
                 LEFT JOIN (SELECT mc04_plan_subject_id
                            FROM mc04_islm_plan_subject_line
                            WHERE mc04_plan_child_subject_code = '1') t2 ON
            t1.mc04_plan_subject_id = t2.mc04_plan_subject_id
                 LEFT JOIN (SELECT mc04_plan_subject_id
                            FROM mc04_islm_plan_subject_line
                            WHERE mc04_plan_child_subject_code = '2') t3 ON
            t1.mc04_plan_subject_id = t3.mc04_plan_subject_id
                 LEFT JOIN (SELECT mc04_plan_subject_id
                            FROM mc04_islm_plan_subject_line
                            WHERE mc04_plan_child_subject_code = '3') t4 ON
            t1.mc04_plan_subject_id = t4.mc04_plan_subject_id
                 LEFT JOIN mc04_islm_brand_plan t5 ON
            t1.za_occurrence_year = t5.za_occurrence_year
                AND t1.mc04_plan_subject_type = t5.mc04_plan_subject_type
                AND t5.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
                AND t5.mc04_is_lastest_version = '1'
                 LEFT JOIN mc04_islm_sale_fo t6 ON
            t1.za_occurrence_year = t6.za_occurrence_year
                AND t1.mc04_plan_subject_type = t6.mc04_plan_subject_type
                AND t6.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
                AND t6.mc04_is_lastest_version = '1'
                 LEFT JOIN (SELECT za_occurrence_year,
                                   mc04_plan_subject_type,
                                   count(ba_com_org_code) fo_count
                            FROM mc04_islm_demand_fo
                            WHERE ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
                              AND mc04_demand_fo_status = '90'
                            GROUP BY za_occurrence_year,
                                     mc04_plan_subject_type) t7 ON
            t1.za_occurrence_year = t7.za_occurrence_year
                AND t1.mc04_plan_subject_type = t7.mc04_plan_subject_type
        WHERE 1 = 1
        <if test="startYear != null and startYear !='' and endYear != null and endYear !=''">
            AND t1.za_occurrence_year BETWEEN #{startYear} AND #{endYear}
        </if>
        -- 查询条件中的开始结束年份
        <if test="type != null and type !=''">
            AND t1.mc04_plan_subject_type = #{type}
        </if>
        -- 查询条件中的类型
        <if test="status != null and status !=''">
            AND t1.mc04_plan_subject_status = #{status}
        </if>
        -- 查询条件中的状态
        ORDER BY t1.create_time DESC

    </select>
</mapper>