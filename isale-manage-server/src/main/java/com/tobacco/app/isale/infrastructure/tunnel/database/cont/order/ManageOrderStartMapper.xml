<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.cont.order.ManageOrderStartMapper">

    <select id="queryOrderPageFromDayPlan"
            resultType="com.tobacco.app.isale.domain.model.cont.order.ManageContOrderFromDayPlan">
        select result.*
        from (select day_plan.mc04_cgt_day_plan_code,
                     max(day_plan.ba_com_org_code)                             as ba_com_org_code,
                     max(day_plan.mc04_cgt_dist_order_code)                    as mc04_cgt_dist_order_code,
                     max(day_plan.mc04_cgt_xy_period_code)                     as mc04_cgt_xy_period_code,
                     max(day_plan.ma02_plan_month)                             as ma02_plan_month,
                     max(day_plan.mc04_month_sale_plan_type)                   as mc04_month_sale_plan_type,
                     max(day_plan.mc04_date_period_code)                       as mc04_date_period_code,
                     max(data_period.mc04_date_period_begin_date)              as mc04_date_period_begin_date,
                     max(data_period.mc04_date_period_end_date)                as mc04_date_period_end_date,
                     max(day_plan.md02_cgt_xy_no)                              as md02_cgt_xy_no,
                     max(day_plan.md02_cgt_out_storehouse_code)                as md02_cgt_out_storehouse_code,
                     max(cont_deliv_whse.cb_cb_logt_whse_name_abbrev)          as md02_cgt_out_storehouse_name,
                     max(day_plan.md02_cgt_in_storehouse_code)                 as md02_cgt_in_storehouse_code,
                     max(cont_reach_whse.md02_cgt_in_storehouse_name)          as md02_cgt_in_storehouse_name,
                     max(day_plan.mc04_cgt_pra_in_storehouse_code)             as mc04_cgt_pra_in_storehouse_code,
                     max(deliv_site.ba_deliv_site_name)                        as mc04_cgt_pra_in_storehouse_name,
                     max(day_plan.mc04_cont_expec_out_date)                    as mc04_cont_expec_out_date,
                     max(day_plan.mc04_cont_expec_arrival_date)                as mc04_cont_expec_arrival_date,
                     max(day_plan.ma02_toba_prod_trade_type_code)              as ma02_toba_prod_trade_type_code,
                     max(day_plan.md03_logt_tray_comb_tsp_tray_type)           as md03_logt_tray_comb_tsp_tray_type,
                     max(day_plan.mc04_cont_zero_clock_type)                   as mc04_cont_zero_clock_type,
                     max(day_plan.za_remark)                                   as za_remark,
                     max(day_plan.create_time)                                 as create_time
              from mc04_islmc_cgt_day_plan as day_plan
                       left join mc04_islmc_cont_deliv_whse as cont_deliv_whse
                                 on day_plan.md02_cgt_out_storehouse_code = cont_deliv_whse.md02_cgt_out_storehouse_code
                                    and cont_deliv_whse.icom_code = #{icomCode}
                       left join mc04_islmc_cont_reach_whse as cont_reach_whse
                                 on day_plan.md02_cgt_in_storehouse_code = cont_reach_whse.md02_cgt_in_storehouse_code
                                    and cont_reach_whse.icom_code = #{icomCode}
                       left join mc04_islmc_cont_real_reach_whse as cont_real_reach_whse
                                 on day_plan.mc04_cgt_pra_in_storehouse_code =
                                    cont_real_reach_whse.mc04_cgt_pra_in_storehouse_code
                                    and cont_real_reach_whse.icom_code = #{icomCode}
                       left join mc04_islm_deliv_site as deliv_site
                                 on cont_real_reach_whse.ba_deliv_site_code = deliv_site.ba_deliv_site_code
                       left join mc04_ind_data_period as data_period
                                 on day_plan.mc04_date_period_code = data_period.mc04_date_period_code
                                    and data_period.icom_code = #{icomCode}
                       where
                           day_plan.icom_code = #{icomCode}
                         and day_plan.mc04_cgt_day_plan_status = '1'
                         <if test="dayPlanPage.prodTradeTypeCode != null and dayPlanPage.prodTradeTypeCode != ''">
                            and day_plan.ma02_toba_prod_trade_type_code = #{dayPlanPage.prodTradeTypeCode}
                         </if>
                         <if test="dayPlanPage.mc04ContExpecOutDateBegin != null and dayPlanPage.mc04ContExpecOutDateBegin != ''">
                            and day_plan.mc04_cont_expec_out_date >= #{dayPlanPage.mc04ContExpecOutDateBegin}
                         </if>
                         <if test="dayPlanPage.mc04ContExpecOutDateEnd != null and dayPlanPage.mc04ContExpecOutDateEnd != ''">
                            and day_plan.mc04_cont_expec_out_date <![CDATA[<=]]> #{dayPlanPage.mc04ContExpecOutDateEnd}
                         </if>
                         <if test="dayPlanPage.comIdList != null and dayPlanPage.comIdList.size() > 0">
                            and day_plan.ba_com_org_code in
                            <foreach item="item" collection="dayPlanPage.comIdList" index="index"
                                     separator="," open="(" close=")">
                                #{item}
                            </foreach>
                         </if>
                         <if test="dayPlanPage.outStorehouseCodeList != null and dayPlanPage.outStorehouseCodeList.size() > 0">
                            and day_plan.md02_cgt_out_storehouse_code in
                            <foreach item="item" collection="dayPlanPage.outStorehouseCodeList" index="index"
                                     separator="," open="(" close=")">
                                #{item}
                            </foreach>
                         </if>
                         <if test='dayPlanPage.isTransferOrder != null and dayPlanPage.isTransferOrder == "1"'>
                            and cont_deliv_whse.cb_logt_whse_busi_type1 in ('1', '2')
                         </if>
                         <if test='dayPlanPage.isTransferOrder != null and dayPlanPage.isTransferOrder == "0"'>
                            and cont_deliv_whse.cb_logt_whse_busi_type1 in ('0', '3', '4')
                         </if>
              group by day_plan.mc04_cgt_day_plan_code) as result
    </select>


    <resultMap id="contOrderFromDayPlan"
               type="com.tobacco.app.isale.domain.model.cont.order.ManageContOrderFromDayPlan">
        <id property="mc04CgtDayPlanCode" column="mc04_cgt_day_plan_code"/>
        <result property="baComOrgCode" column="ba_com_org_code"/>
        <result property="mc04CgtDistOrderCode" column="mc04_cgt_dist_order_code"/>
        <result property="mc04CgtXyPeriodCode" column="mc04_cgt_xy_period_code"/>
        <result property="ma02PlanMonth" column="ma02_plan_month"/>
        <result property="mc04MonthSalePlanType" column="mc04_month_sale_plan_type"/>
        <result property="mc04DatePeriodCode" column="mc04_date_period_code"/>
        <result property="md02CgtXyNo" column="md02_cgt_xy_no"/>
        <result property="md02CgtOutStorehouseCode" column="md02_cgt_out_storehouse_code"/>
        <result property="md02CgtOutStorehouseName" column="md02_cgt_out_storehouse_name"/>
        <result property="md02CgtInStorehouseCode" column="md02_cgt_in_storehouse_code"/>
        <result property="md02CgtInStorehouseName" column="md02_cgt_in_storehouse_name"/>
        <result property="mc04CgtPraInStorehouseCode" column="mc04_cgt_pra_in_storehouse_code"/>
        <result property="mc04CgtPraInStorehouseName" column="mc04_cgt_pra_in_storehouse_name"/>
        <result property="mc04ContExpecOutDate" column="mc04_cont_expec_out_date"/>
        <result property="mc04ContExpecArrivalDate" column="mc04_cont_expec_arrival_date"/>
        <result property="ma02TobaProdTradeTypeCode" column="ma02_toba_prod_trade_type_code"/>
        <result property="md03LogtTrayCombTspTrayType" column="md03_logt_tray_comb_tsp_tray_type"/>
        <result property="zbSettlementModeCode" column="zb_settlement_mode_code"/>
        <result property="mc04ContZeroClockType" column="mc04_cont_zero_clock_type"/>
        <result property="zaRemark" column="za_remark"/>
        <collection property="contOrderItemList"
                    ofType="com.tobacco.app.isale.domain.model.cont.order.ManageContOrderItemFromDayPlan">
            <result property="acCgtCartonCode" column="ac_cgt_carton_code"/>
            <result property="acTwoLevelCigCode" column="ac_two_level_cig_code"/>
            <result property="ma02CgtPlAdjustedQty" column="ma02_cgt_pl_adjusted_qty"/>
            <result property="remainQty" column="remain_qty"/>
        </collection>
    </resultMap>

    <select id="queryOrderListFromDayPlan" resultMap="contOrderFromDayPlan">
        select *
        from (select day_plan.mc04_cgt_day_plan_code,
                     max(day_plan.ba_com_org_code)                                             as ba_com_org_code,
                     max(day_plan.mc04_cgt_dist_order_code)                                    as mc04_cgt_dist_order_code,
                     max(day_plan.mc04_cgt_xy_period_code)                                     as mc04_cgt_xy_period_code,
                     max(day_plan.ma02_plan_month)                                             as ma02_plan_month,
                     max(day_plan.mc04_month_sale_plan_type)                                   as mc04_month_sale_plan_type,
                     max(day_plan.mc04_date_period_code)                                       as mc04_date_period_code,
                     max(day_plan.md02_cgt_xy_no)                                              as md02_cgt_xy_no,
                     max(day_plan.md02_cgt_out_storehouse_code)                                as md02_cgt_out_storehouse_code,
                     max(day_plan.md02_cgt_in_storehouse_code)                                 as md02_cgt_in_storehouse_code,
                     max(day_plan.mc04_cgt_pra_in_storehouse_code)                             as mc04_cgt_pra_in_storehouse_code,
                     max(day_plan.mc04_cont_expec_out_date)                                    as mc04_cont_expec_out_date,
                     max(day_plan.mc04_cont_expec_arrival_date)                                as mc04_cont_expec_arrival_date,
                     max(day_plan.ma02_toba_prod_trade_type_code)                              as ma02_toba_prod_trade_type_code,
                     max(day_plan.md03_logt_tray_comb_tsp_tray_type)                           as md03_logt_tray_comb_tsp_tray_type,
                     max(com_rec_type.zb_settlement_mode_code)                                 as zb_settlement_mode_code,
                     max(day_plan.mc04_cont_zero_clock_type)                                   as mc04_cont_zero_clock_type,
                     max(day_plan.za_remark)                                                   as za_remark,
                     day_plan.ac_cgt_carton_code,
                     day_plan.ac_two_level_cig_code,
                     sum(day_plan.ma02_cgt_pl_adjusted_qty)                                    as ma02_cgt_pl_adjusted_qty,
                     sum((day_plan.ma02_cgt_pl_adjusted_qty - coalesce(usage_qty.qty_use, 0))) as remain_qty
              from mc04_islmc_cgt_day_plan as day_plan
                       left join (select usage_qty_day_plan.mc04_cgt_day_plan_code,
                                         usage_qty_day_plan.ac_two_level_cig_code,
                                         sum(usage_qty_cont_order_item.md02_cgt_trade_cont_qty) qty_use
                                  from (select mc04_cgt_day_plan_code,
                                               ac_two_level_cig_code
                                        from mc04_islmc_cgt_day_plan
                                        where ma02_cgt_pl_adjusted_qty > 0
                                          and mc04_cont_zero_clock_type = '0'
                                            <foreach collection="dayPlanCodeList" open="and mc04_cgt_day_plan_code in ("
                                                     separator="," close=")" item="dayPlanCode">
                                                #{dayPlanCode}
                                            </foreach>
                                        ) as usage_qty_day_plan
                                           left join mc04_islm_cont_order usage_qty_cont_order
                                                     on usage_qty_cont_order.mc04_cgt_day_plan_code =
                                                        usage_qty_day_plan.mc04_cgt_day_plan_code
                                                         and usage_qty_cont_order.mc04_cgt_trade_cont_status != '80'
                                           left join mc04_islm_cont_order_item usage_qty_cont_order_item
                                                     on usage_qty_cont_order_item.mc04_cont_order_id =
                                                        usage_qty_cont_order.mc04_cont_order_id
                                                         and usage_qty_day_plan.ac_two_level_cig_code =
                                                             usage_qty_cont_order_item.ac_two_level_cig_code
                                  group by usage_qty_day_plan.mc04_cgt_day_plan_code,
                                           usage_qty_day_plan.ac_two_level_cig_code) as usage_qty
                                 on usage_qty.mc04_cgt_day_plan_code = day_plan.mc04_cgt_day_plan_code
                                     and usage_qty.ac_two_level_cig_code = day_plan.ac_two_level_cig_code
                       left join mc04_islmc_com_rec_type as com_rec_type
                                 on com_rec_type.ba_com_org_code = day_plan.ba_com_org_code
                        <where>
                            <foreach collection="dayPlanCodeList" open="and day_plan.mc04_cgt_day_plan_code in ("
                                     separator="," close=")" item="dayPlanCode">
                                #{dayPlanCode}
                            </foreach>
                        </where>
              group by day_plan.mc04_cgt_day_plan_code,
                       day_plan.ac_cgt_carton_code,
                       day_plan.ac_two_level_cig_code) as cont_order_from_day_plan
        where remain_qty > 0
    </select>
</mapper>