package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * 退库申请单规格 视图层对象/值对象
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islmc_ind_return_apply_item")
public class Mc04IslmcIndReturnApplyItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 工业退库申请规格信息编码
     */
                @TableId(value = "mc04_ind_return_apply_item_id", type = IdType.NONE)
    @Field(value = "工业退库申请规格信息编码", name = "工业退库申请规格信息编码")
    private String mc04IndReturnApplyItemId;

    /**
     * 工业退库申请编码
     */
    @Field(value = "工业退库申请编码", name = "工业退库申请编码")
    private String mc04IndReturnApplyId;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 二级牌号编码
     */
    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;

    /**
     * 三级级牌号编码
     */
    @Field(value = "三级级牌号编码", name = "三级级牌号编码")
    private String acThrLevelCigCode;

    /**
     * 卷烟含税调拨价格
     */
    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟交易合同交易量
     */
    @Field(value = "卷烟交易合同交易量", name = "卷烟交易合同交易量")
    private BigDecimal md02CgtTradeContQty;

    /**
     * 卷烟工业销售退库量（万支）
     */
    @Field(value = "卷烟工业销售退库量（万支）", name = "卷烟工业销售退库量（万支）")
    @TableField("mc05_cgt_10th_ind_return_qty")
    private BigDecimal mc05Cgt10thIndReturnQty;

    /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

    /**
     * 创建人ID
     */
    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

    /**
     * 创建人名称
     */
    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @Field(value = "创建时间", name = "创建时间")
    private String createTime;

    /**
     * 修改人ID
     */
    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;

    /**
     * 修改人名称
     */
    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

    /**
     * 修改时间
     */
    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;


}
