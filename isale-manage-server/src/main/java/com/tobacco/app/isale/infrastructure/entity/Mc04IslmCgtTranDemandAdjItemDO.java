package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * 调拨计划变更明细 视图层对象/值对象
 * </p>
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islm_cgt_tran_demand_adj_item")
public class Mc04IslmCgtTranDemandAdjItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 月调拨计划调整单明细编号
     */
                @TableId(value = "mc04_month_sale_plan_adj_item_id", type = IdType.NONE)
    @Field(value = "月调拨计划调整单明细编号", name = "月调拨计划调整单明细编号")
    private String mc04MonthSalePlanAdjItemId;

    /**
     * 月调拨计划调整单编号
     */
    @Field(value = "月调拨计划调整单编号", name = "月调拨计划调整单编号")
    private String mc04MonthSalePlanAdjId;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;

    /**
     * 二级牌号编码
     */
    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;

    /**
     * 二级牌号名称
     */
    @Field(value = "二级牌号名称", name = "二级牌号名称")
    private String acTwoLevelCigName;

    /**
     * 卷烟含税调拨价格 存商品中心返回的含税调拨价,卷烟:元/条,雪茄:元/支
     */
    @Field(value = "卷烟含税调拨价格 存商品中心返回的含税调拨价,卷烟:元/条,雪茄:元/支", name = "卷烟含税调拨价格 存商品中心返回的含税调拨价,卷烟:元/条,雪茄:元/支")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟调拨计划原计划量 万支,对应页面上原备货量
     */
    @Field(value = "卷烟调拨计划原计划量 万支,对应页面上原备货量", name = "卷烟调拨计划原计划量 万支,对应页面上原备货量")
    private BigDecimal ma02CgtPlResolOrigplanQty;

    /**
     * 卷烟调拨计划调整量 万支,对应页面上本次调整量
     */
    @Field(value = "卷烟调拨计划调整量 万支,对应页面上本次调整量", name = "卷烟调拨计划调整量 万支,对应页面上本次调整量")
    private BigDecimal ma02CgtPlAdjusQty;

    /**
     * 卷烟调拨计划计划科确认调整量 万支,对应页面上本次审核量
     */
    @Field(value = "卷烟调拨计划计划科确认调整量 万支,对应页面上本次审核量", name = "卷烟调拨计划计划科确认调整量 万支,对应页面上本次审核量")
    private BigDecimal mc04CgtAllotPlanPdConfirmAdjQty;

    /**
     * 卷烟调拨计划调整后计划量 万支,对应页面上当前备货量
     */
    @Field(value = "卷烟调拨计划调整后计划量 万支,对应页面上当前备货量", name = "卷烟调拨计划调整后计划量 万支,对应页面上当前备货量")
    private BigDecimal ma02CgtPlAdjustedQty;

    /**
     * 备注
     */
    @Field(value = "备注", name = "备注")
    private String zaRemark;

    /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

    /**
     * 创建人ID
     */
    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

    /**
     * 创建人名称
     */
    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @Field(value = "创建时间", name = "创建时间")
    private String createTime;

    /**
     * 修改人ID
     */
    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;

    /**
     * 修改人名称
     */
    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

    /**
     * 修改时间
     */
    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;


}
