/*
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 *  版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DomainObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 前置库移库单规格
 * @Author: wuhaoran01
 * @Since: 2025-05-21
 * @Email: <EMAIL>
 * @Create: 2025-05-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_warehouse_stock_tran_bill_item")
@DomainObject(name = "前置库移库单规格", desc = "前置库移库单规格")
public class Mc04IslmcWarehouseStockTranBillItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
    /**
     * 卷烟移库单明细编码
     */

    @TableId(value = "mc04_warehouse_stock_tran_bill_item_id", type = IdType.NONE)
    @Field(value = "卷烟移库单明细编码", name = "卷烟移库单明细编码")
    private String mc04WarehouseStockTranBillItemId;
    /**
     * 卷烟移库单编码
     */

    @Field(value = "卷烟移库单编码", name = "卷烟移库单编码")
    private String mc04WarehouseStockTranBillId;
    /**
     * 卷烟代码（条）
     */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
    /**
     * 卷烟名称
     */

    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;
    /**
     * 二级牌号编码
     */

    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;
    /**
     * 二级牌号名称
     */

    @Field(value = "二级牌号名称", name = "二级牌号名称")
    private String acTwoLevelCigName;
    /**
     * 卷烟含税调拨价格
     */

    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;
    /**
     * 物流工商共库移库量
     */

    @Field(value = "物流工商共库移库量", name = "物流工商共库移库量")
    private BigDecimal md03LogtTrayCombTspIcRlcQty;


    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;


    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;


    @Field(value = "创建时间", name = "创建时间")
    private String createTime;


    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;


    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;
}
