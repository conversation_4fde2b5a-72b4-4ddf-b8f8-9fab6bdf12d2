package com.tobacco.app.isale.infrastructure.tunnel.database.nation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 行业营销子系统合同数据查询 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface ContractDownloadMapper extends BaseMapper<Map> {
    /**
     * 删除行业子系统合同号
     *
     * @param datas 数据
     * @return 删除结果
     */
    int deleteContractNo(List<Map<String, Object>> datas);

    /**
     * 插入行业子系统合同号
     *
     * @param datas 数据
     * @return int
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-05 11:46:31
     * @description : 插入行业子系统合同号
     */
    int insertContractNo(List<Map<String, Object>> datas);

    /**
     * 处理行业子系统合同号
     *
     * @param codeList 数据
     * @return 处理结果
     */
    int processContractNo(List<String> codeList);

    /**
     * 更新日计划表生效状态
     *
     * @param codeList 数据
     * @return 处理结果
     */
    int updateItemDayPlanStatus(List<String> codeList);

    /**
     * 删除合同
     *
     * @param resultList 数据
     * @return 处理结果
     */
    int deleteContract(List<Map<String, Object>> resultList);

    /**
     * 新增合同
     *
     * @param resultList 数据
     * @return 处理结果
     */
    int insertContract(List<Map<String, Object>> resultList);

    /**
     * 删除合同从表
     *
     * @param items 数据
     * @return 处理结果
     */
    int deleteContractItem(List<Map<String, Object>> items);


    /**
     * 新增合同从表
     *
     * @param datas 数据
     * @return 处理结果
     */
    int insertContractItem(List<Map<String, Object>> datas);

    /**
     * 更新合同
     *
     * @param dataList 数据
     * @return 处理结果
     */
    int updateSignContract(List<Map<String, Object>> dataList);

    /**
     * 更新合同从表
     *
     * @param dataList 数据
     * @return 处理结果
     */
    int updateContractItemFromNation(List<Map<String, Object>> dataList);

    /**
     * 插入合同
     *
     * @param dataList 数据
     * @param icomCode 工业代码
     * @return 处理结果
     */
    int insertContractFromNation(List<Map<String, Object>> dataList, String icomCode);

    /**
     * 插入合同从表
     *
     * @param dataList 数据
     * @param icomCode 工业代码
     * @return 处理结果
     */
    int insertContractItemFromNation(List<Map<String, Object>> dataList, String icomCode);

    /**
     * 合计合同
     *
     * @param dataList 数据
     * @return 处理结果
     */
    int summaryContract(List<Map<String, Object>> dataList);

    /**
     * 获取需要签合同
     *
     * @param dataList 数据
     * @return 处理结果
     */
    List<String> getNeedSignContract(List<Map<String, Object>> dataList);

    /**
     * 获取变更的合同
     *
     * @param contIdList 数据
     * @return 处理结果
     */
    List<String> getChangeSignContract(List<String> contIdList);


    /**
     * 删除合同解除中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationContractCancel(List<Map<String, Object>> datas);

    /**
     * 新增合同解除中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationContractCancel(List<Map<String, Object>> datas);

    /**
     * 获取需要解除合同
     *
     * @param dataList 数据
     * @return 处理结果
     */
    List<String> getNeedCancelContract(List<Map<String, Object>> dataList);

    /**
     * 更新合同到解除状态
     *
     * @param dataList 数据
     * @return 处理结果
     */
    int updateCancelContract(List<Map<String, Object>> dataList);

}
