<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.IslmMonthPlanMapper">


    <select id="getAgreementExecutionByCriteria"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select a.ba_com_org_code baComOrgCode,
        a.ac_cgt_carton_code acCgtCartonCode,
        a.ac_cgt_name acCgtName,
        a.agreement_qty agreementQty,
        coalesce(b.agreement_exe_qty, 0) - coalesce(c.cancel_cont_qty,0) agreementExeQty,
        coalesce(a.agreement_qty,0) - coalesce(b.agreement_exe_qty, 0) + coalesce(c.cancel_cont_qty,0) agreementRemainQty
        from (
        select a.ba_com_org_code,
        b.ac_cgt_carton_code,
        b.ac_cgt_name,
        sum(b.md02_cgt_xy_adjusted_qty) agreement_qty
        from mc04_islmc_xy a,
        mc04_islmc_xy_item b
        where a.mc04_cgt_xy_id = b.mc04_cgt_xy_id
        and a.mc04_cgt_xy_period_code = #{halfYearCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and a.ma02_toba_prod_trade_type_code = #{cgtType}
        and a.mc04_cgt_xy_status = '1'
        -- and a.icom_code = '20420001'
        group by a.ba_com_org_code, b.ac_cgt_carton_code, b.ac_cgt_name) a
        left join
        (
        select
        a.ba_com_org_code,
        a.ac_cgt_carton_code,
        sum(a.ma02_cgt_pl_adjusted_qty) agreement_exe_qty
        from mc04_islmc_cgt_day_plan a
        where a.mc04_cgt_xy_period_code =#{halfYearCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and a.ma02_toba_prod_trade_type_code = #{cgtType}
        -- and a.icom_code = '20420001'
        group by a.ba_com_org_code, a.ac_cgt_carton_code) b
        on a.ba_com_org_code = b.ba_com_org_code and a.ac_cgt_carton_code = b.ac_cgt_carton_code
        left join
        (select ba_com_org_code,
        ac_cgt_carton_code,
        sum(b.md02_cgt_trade_cont_qty) cancel_cont_qty
        from mc04_islmc_cont_order a,
        mc04_islmc_cont_order_item b
        where a.mc04_cont_order_id = b.mc04_cont_order_id
        and a.mc04_cgt_trade_cont_status = '90'
        and a.mc04_cgt_xy_period_code = #{halfYearCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by ba_com_org_code,
        ac_cgt_carton_code) c
        on a.ba_com_org_code = c.ba_com_org_code and a.ac_cgt_carton_code = c.ac_cgt_carton_code
    </select>
    <select id="getYearSalesPlan"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select a.mc04_org_type_code    baComOrgCode,
               b.ac_cgt_carton_code   acCgtCartonCode,
               b.ac_cgt_name          acCgtName,
               coalesce(sum(b.mc04_cgt_sale_plan_adjusted_qty),0)      salePlanQty,
               COALESCE(sum(b.ac_cgt_trade_price * b.mc04_cgt_sale_plan_adjusted_qty),0)   salePlanAmt,
               COALESCE(sum(b.ma02_cgt_pl_adjusted_qty),0) allocatePlanQty,
               sum(b.ac_cgt_tax_allot_price * b.mc04_cgt_sale_plan_adjusted_qty)  as allocatePlanAmt
        from mc04_islm_sale_plan a,
             mc04_islm_sale_plan_item b
        where a.mc04_sale_plan_id = b.mc04_sale_plan_id
          and a.za_occurrence_year = #{yearCode}
          and a.mc04_cgt_sale_fo_period_code = #{yearCode}
          and a.mc04_sale_plan_status = '50'
          and a.mc04_cgt_sale_fo_period_type = 'T01'
          and a.mc04_org_type_kind = '02'
          <if test="comIds != null and comIds.size() > 0">
            and a.mc04_org_type_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
          and a.mc04_is_lastest_version = '1'
        group by a.mc04_org_type_code, b.ac_cgt_carton_code, b.ac_cgt_name
    </select>
    <select id="getAnnualSalesExecution"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">

        select
        a.mc04_xy_org_code as baComOrgCode,
        a.AC_CGT_CARTON_CODE as acCgtCartonCode,
        sum(MD03_CGT_10TH_COM_COE_SALE_QTY_Y_A) as yearSaleQty,
        sum(MD03_CGT_10TH_COM_COE_SALE_AMT_Y_A) as yearSaleAmt
        from
        MC04_IND_XY_ORG_CGT_PURCH_SALE_STK_MONTH a
        where
        a.mc04_cgt_forfeiture_flag = '1'
        and MC04_PURCHASE_SALE_STK_MONTH = #{monthCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.mc04_xy_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by a.mc04_xy_org_code , AC_CGT_CARTON_CODE

    </select>
    <select id="getQuarterSalesPlan"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">

        select a.mc04_org_type_code                    as baComOrgCode,
               b.ac_cgt_carton_code as acCgtCartonCode,
               b.ac_cgt_name as acCgtName,
               coalesce(sum(b.mc04_cgt_sale_plan_adjusted_qty),0) sale_plan_qty as quarterSalePlanQty
        from mc04_islm_sale_plan a,
             mc04_islm_sale_plan_item b
        where a.mc04_sale_plan_id = b.mc04_sale_plan_id
          and a.za_occurrence_year = #{yearCode}
          and a.mc04_cgt_sale_fo_period_code = #{quarterCode}
          and a.mc04_sale_plan_status = '50'
          and a.mc04_cgt_sale_fo_period_type = 'T03'
          and a.mc04_org_type_kind = '02'
          <if test="comIds != null and comIds.size() > 0">
            and a.mc04_org_type_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
          and a.mc04_is_lastest_version = '1'
        group by a.mc04_org_type_code, b.ac_cgt_carton_code, b.ac_cgt_name
    </select>
    <select id="getQuarterSalesExecution"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select
            a.mc04_xy_org_code baComOrgCode,
            a.AC_CGT_CARTON_CODE as acCgtCartonCode,
            coalesce(sum(a.MD03_CGT_10TH_COM_COE_SALE_QTY_Q_A),0) quarterSaleQty
        from
            MC04_IND_XY_ORG_CGT_PURCH_SALE_STK_MONTH a
        where
            a.mc04_cgt_forfeiture_flag = '1'
          and a.MC04_PURCHASE_SALE_STK_MONTH = #{monthCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.mc04_xy_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by   a.mc04_xy_org_code , a.AC_CGT_CARTON_CODE

    </select>
    <select id="getMonthActualSale"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">

        select a.mc04_xy_org_code                                     as baComOrgCode,
               a.AC_CGT_CARTON_CODE                                   as acCgtCartonCode,
               coalesce(sum(a.MD03_CGT_10TH_COM_COE_SALE_QTY_M_A), 0) as monthSaleQty,
               coalesce(sum(a.MC04_MG_CGT_10TH_EOM_STK_QTY), 0)       as monthStockQty
        from MC04_IND_XY_ORG_CGT_PURCH_SALE_STK_MONTH a
        where a.mc04_cgt_forfeiture_flag = '1'
          and MC04_PURCHASE_SALE_STK_MONTH = #{monthCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.mc04_xy_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by a.mc04_xy_org_code, AC_CGT_CARTON_CODE
    </select>
    <select id="getMonthActualAllocate"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select  a.ba_com_org_code as baComOrgCode,
               a.ac_cgt_carton_code as acCgtCartonCode,
               coalesce(a.month_allocate_qty,0) - coalesce(c.cancel_cont_qty,0) as monthAllocateQty
        from (select a.ba_com_org_code,
                     a.ac_cgt_carton_code,
                     sum(a.ma02_cgt_pl_adjusted_qty) month_allocate_qty
              from mc04_islmc_cgt_day_plan a
              where a.ma02_plan_month = #{monthCode}
                <if  test="comIds != null and comIds.size() > 0">
                  and a.ba_com_org_code in
                  <foreach item="item" index="index" collection="comIds"
                           open="(" close=")" separator=",">
                      #{item}
                  </foreach>
              </if>
                and a.ma02_toba_prod_trade_type_code = #{cgtType}
--                 and a.icom_code = '20420001'
              group by a.ba_com_org_code, a.ac_cgt_carton_code) a
                 left join
             (select a.ba_com_org_code,
                     b.ac_cgt_carton_code,
                     sum(b.md02_cgt_trade_cont_qty) cancel_cont_qty
              from mc04_islmc_cont_order a,
                   mc04_islmc_cont_order_item b
              where a.mc04_cont_order_id = b.mc04_cont_order_id
                and a.mc04_cgt_trade_cont_status = '90'
                and a.ma02_plan_month = #{monthCode}
              <if  test="comIds != null and comIds.size() > 0">
                  and a.ba_com_org_code in
                  <foreach item="item" index="index" collection="comIds"
                           open="(" close=")" separator=",">
                      #{item}
                  </foreach>
              </if>
              group by a.ba_com_org_code,
                       b.ac_cgt_carton_code) c
             on a.ba_com_org_code = c.ba_com_org_code and a.ac_cgt_carton_code = c.ac_cgt_carton_code
    </select>
    <select id="getMonthAllocatePlan"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select a.mc04_org_type_code baComOrgCode,
        b.ac_cgt_carton_code,
        b.ac_cgt_name,
        coalesce(sum(b.mc04_cgt_sale_plan_adjusted_qty),0) monthSalePlanQty,
        COALESCE(sum(b.ma02_cgt_pl_adjusted_qty),0) monthAllocatePlanQty
        from mc04_islm_sale_plan a,
        mc04_islm_sale_plan_item b
        where a.mc04_sale_plan_id = b.mc04_sale_plan_id
        and a.za_occurrence_year = #{yearCode}
        and a.mc04_cgt_sale_fo_period_code = #{monthCode}
        and a.mc04_sale_plan_status = '50'
        and a.mc04_cgt_sale_fo_period_type = 'T04'
        and a.mc04_org_type_kind = '02'

        <if test="comIds != null and comIds.size() > 0">
            and a.mc04_org_type_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and a.mc04_is_lastest_version = '1'
        group by a.mc04_org_type_code, b.ac_cgt_carton_code, b.ac_cgt_name

    </select>
    <select id="getMonthReasonableSaleRatio"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select
        case
        when b.MC04_CGT_SALE_FO_PARAM_CODE='20' then '11'
        when b.MC04_CGT_SALE_FO_PARAM_CODE='30' then '12'
        else '00' end   as mc04CgtSaleFoParamCode,
        b.MC04_CGT_SALE_FO_PARAM_VALUE as mc04CgtSaleFoParamValue,
        from
        mc04_ipm_sale_fo_param_config a,
        mc04_ipm_sale_fo_param_config_line b ,
        mc04_ipm_sale_fo_custom_period c
        where
        a.MC04_CGT_SALE_FO_PARAM_CONFIG_ID = b.MC04_CGT_SALE_FO_PARAM_CONFIG_ID
        and b.MC04_CGT_SALE_FO_CUSTOM_PERIOD_CODE = c.MC04_CGT_SALE_FO_CUSTOM_PERIOD_CODE
        and a.MC04_CGT_SALE_FO_PERIOD_CODE = #{yearCode}
        and a.MC04_CGT_SALE_FO_PARAM_TYPE='SALE_FORE_MONTH_CXB'
        and a.MC04_IS_LASTEST_VERSION='1'
        and c.MC04_CGT_SALE_FO_BEGIN_PERIOD_CODE &lt;= #{monthCode}
        and c.MC04_CGT_SALE_FO_END_PERIOD_CODE>=#{monthCode}
        <if test="comIds != null and comIds.size() > 0">
            and b.MC04_ORG_TYPE_CODE in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="getMonthPlan"
            resultType="com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan">
        select ba_com_org_code,
               ma02_plan_month,
               mc04_month_sale_plan_type,
               mc04_month_sale_plan_status,
               ac_cgt_carton_code,
               ac_two_level_cig_code,
               mc04_cgt_sale_plan_report_qty,
               mc04_cgt_sale_plan_report_qty,
               mc04_cgt_sale_plan_region_confirm_qty,
               mc04_cgt_sale_plan_bd_confirm_qty,
               mc04_cgt_sale_plan_pd_publish_qty,
               mc04_cgt_allot_plan_report_qty,
               mc04_cgt_allot_plan_region_confirm_qty,
               mc04_cgt_allot_plan_bd_confirm_qty,
               mc04_cgt_allot_plan_pd_publish_qty
        from mc04_islm_month_sale_plan
        where  za_occurrence_month = #{monthCode}
        <if test="comIds != null and comIds.size() > 0">
            and ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
          and ma02_toba_prod_trade_type_code = #{cgtType}
          and mc04_month_sale_plan_status = '20'
    </select>
    <select id="getExeQyt1" resultType="java.util.Map">
        select plan.ba_com_org_code,plan.ac_cgt_carton_code,plan.ac_two_level_cig_code,coalesce(plan.qty_dist,0)-coalesce(cont.qty_cont,0) qty_exe
        from
        (SELECT ba_com_org_code,ac_cgt_carton_code,ac_two_level_cig_code,sum(ma02_cgt_pl_adjusted_qty) qty_dist
        from mc04_islmc_cgt_day_plan
        where ma02_toba_prod_trade_type_code=#{MA02_TOBA_PROD_TRADE_TYPE_CODE} and ma02_plan_month=#{MA02_PLAN_MONTH}
        <if test="BA_COM_ORG_CODE != null and BA_COM_ORG_CODE !=''">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY ba_com_org_code,ac_cgt_carton_code,ac_two_level_cig_code) plan
        left join
        (select a.ba_com_org_code,b.ac_cgt_carton_code,b.ac_two_level_cig_code,sum(b.md02_cgt_trade_cont_qty) qty_cont
        from mc04_islm_cont_order a,mc04_islm_cont_order_item b
        where a.mc04_cont_order_id=b.mc04_cont_order_id and a.ma02_toba_prod_trade_type_code=#{MA02_TOBA_PROD_TRADE_TYPE_CODE} and a.ma02_plan_month=#{MA02_PLAN_MONTH}
        and a.mc04_cgt_trade_cont_status in ('80','90')
        <if test="BA_COM_ORG_CODE != null and BA_COM_ORG_CODE !=''">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY a.ba_com_org_code,b.ac_cgt_carton_code,b.ac_two_level_cig_code) cont
        on plan.ba_com_org_code=cont.ba_com_org_code and plan.ac_cgt_carton_code=cont.ac_cgt_carton_code and plan.ac_two_level_cig_code=cont.ac_two_level_cig_code
    </select>

    <select id="getExeQyt2" resultType="java.util.Map">
        select a.ba_com_org_code,b.ac_cgt_carton_code,b.ac_two_level_cig_code,sum(b.md03_logt_tray_comb_tsp_ic_rlc_qty)
        qty_exe
        from mc04_islmc_warehouse_stock_tran_bill a,mc04_islmc_warehouse_stock_tran_bill_item b
        where a.mc04_warehouse_stock_tran_bill_id=b.mc04_warehouse_stock_tran_bill_id
        and a.ma02_toba_prod_trade_type_code=#{MA02_TOBA_PROD_TRADE_TYPE_CODE}
        and a.ma02_plan_month=#{MA02_PLAN_MONTH}
        and a.mc04_warehouse_stock_tran_bill_status in ('30','40')
        <if test="BA_COM_ORG_CODE != null and BA_COM_ORG_CODE !=''">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY a.ba_com_org_code,b.ac_cgt_carton_code,b.ac_two_level_cig_code

        union all

        select
        plan.ba_com_org_code,plan.ac_cgt_carton_code,plan.ac_two_level_cig_code,coalesce(plan.qty_dist,0)-coalesce(cont.qty_cont,0)
        qty_exe
        from
        (SELECT ba_com_org_code,ac_cgt_carton_code,ac_two_level_cig_code,sum(ma02_cgt_pl_adjusted_qty) qty_dist
        from mc04_islmc_cgt_day_plan
        where ma02_toba_prod_trade_type_code=#{MA02_TOBA_PROD_TRADE_TYPE_CODE} and ma02_plan_month=#{MA02_PLAN_MONTH}
        <if test="BA_COM_ORG_CODE != null and BA_COM_ORG_CODE !=''">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and ba_com_org_code not in (SELECT ba_com_org_code from mc04_islmc_forward_whse_com) -- 排产前置库地市
        GROUP BY ba_com_org_code,ac_cgt_carton_code,ac_two_level_cig_code) plan
        left join
        (select a.ba_com_org_code,b.ac_cgt_carton_code,b.ac_two_level_cig_code,sum(b.md02_cgt_trade_cont_qty) qty_cont
        from mc04_islm_cont_order a,mc04_islm_cont_order_item b
        where a.mc04_cont_order_id=b.mc04_cont_order_id and
        a.ma02_toba_prod_trade_type_code=#{MA02_TOBA_PROD_TRADE_TYPE_CODE} and a.ma02_plan_month= #{MA02_PLAN_MONTH}
        and a.mc04_cgt_trade_cont_status in ('80','90')
        <if test="BA_COM_ORG_CODE != null and BA_COM_ORG_CODE !=''">
            and a.ba_com_org_code in
            <foreach item="item" index="index" collection="comIds"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY a.ba_com_org_code,b.ac_cgt_carton_code,b.ac_two_level_cig_code) cont
        on plan.ba_com_org_code=cont.ba_com_org_code and plan.ac_cgt_carton_code=cont.ac_cgt_carton_code and
        plan.ac_two_level_cig_code=cont.ac_two_level_cig_code
    </select>
</mapper>