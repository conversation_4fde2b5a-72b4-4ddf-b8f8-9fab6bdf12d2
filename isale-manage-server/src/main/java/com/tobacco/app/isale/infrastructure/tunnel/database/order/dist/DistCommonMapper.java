/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.order.dist;


import com.tobacco.app.isale.domain.model.order.dist.DistStatusNav;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @email : <EMAIL>
 * @create_time : 2025/07/16 16:23
 * @description : 配货相关公共部分
 */
@Mapper
public interface DistCommonMapper {

    /**
     * 获取状态导航
     *
     * @param busiType  业务类型 10:配货参数,20:配货订单
     * @param distType  网配订单类型 10:实时配货,20:周配货,30:前置库配货,40:日常配货,50:备货配货,00:配货参数
     * @param curStatus 当前状态
     * @param icomCode  工业公司编码
     * @return 返回数据
     */
    DistStatusNav getStatusNav(String busiType, String distType, String curStatus, String icomCode);

    /**
     * 获取所有50商业待确认状态的配货单号
     *
     * @return 返回数据
     */
    List<String> getDistOrderCodes();

    /**
     * 获取所有50商业待确认状态的配货参数申请对应的行业主键
     *
     * @return 返回数据
     */
    List<String> getDistParamPks();


}
