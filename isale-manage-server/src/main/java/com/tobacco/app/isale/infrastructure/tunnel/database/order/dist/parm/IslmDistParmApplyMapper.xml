<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.parm.IslmDistParmApplyMapper">
    <select id="queryDistParmApplyPage" resultType="com.tobacco.app.isale.domain.model.order.dist.parm.DistParmApply">
        select mc04_cgt_dist_parm_apply_id,
               mc04_cgt_dist_parm_apply_code,
               ma02_cgt_trade_supp_memb_code,
               md02_cgt_trade_supp_memb_name,
               ma02_cgt_trade_req_memb_code,
               md02_cgt_trade_req_memb_name,
               md02_cgt_dist_region_code,
               md02_cgt_dist_region_name,
               md02_cgt_dist_prep_days,
               md02_cgt_dist_on_way_days,
               md02_cgt_dist_daily_sale_days,
               md02_cgt_dist_dayily_sale_model_code,
               mc04_cgt_dist_parm_apply_status,
               mc04_cgt_dist_parm_apply_upload_status,
               za_remark,
               icom_code,
               create_id,
               create_name,
               create_time,
               update_id,
               update_name,
               update_time
        from mc04_islmc_dist_parm_apply a
        where a.icom_code = #{queryParam.icomCode}
    </select>

    <select id="queryDistParmApplyStatus"
            resultType="com.tobacco.app.isale.domain.model.order.dist.parm.DistParmApplyStatus">
        select ma02_cgt_trade_req_memb_code,
               min(mc04_cgt_dist_parm_apply_status) mc04_cgt_dist_parm_apply_status
        from mc04_islmc_dist_parm_apply a
        where a.ma02_cgt_trade_supp_memb_code = #{ma02CgtTradeSuppMembCode}
        group by ma02_cgt_trade_req_memb_code
    </select>

    <select id="queryNationParmStatus" resultType="com.tobacco.app.isale.domain.model.order.dist.parm.DistParmStatus">
        select b.reqmember_code     ma02_cgt_trade_req_memb_code,
               min(b.effect_status) mc04_cgt_dist_parm_effect_status
        from nation_parm b
        where b.supmember_code = #{ma02CgtTradeSuppMembCode}
        group by b.reqmember_code
    </select>


</mapper>