package com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.adjust;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.dto.supply.psc.allocdemand.adjust.AllocdemandAdjustDTO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 */
@Mapper
public interface Mc04IslmCgtTranDemandAdjMapper extends BaseMapper<Mc04IslmCgtTranDemandAdjDO> {
    /**
     * 获取调拨需求变更制定page
     **/
    Page<AllocdemandAdjustDTO> queryAllocdemandAdjustPage(Page page, @Param("ma02PlanMonth") String ma02PlanMonth, @Param("mc03CgtProdPlType") String mc03CgtProdPlType,@Param("mc04CgtTranDemandAdjDate") String mc04CgtTranDemandAdjDate, @Param("mc04CgtTranDemandAdjStatus") String mc04CgtTranDemandAdjStatus);
}