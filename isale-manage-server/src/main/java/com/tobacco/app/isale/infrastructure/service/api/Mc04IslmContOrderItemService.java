/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderItemDO;


/**
 * @description : 服务类
 *
 * <AUTHOR> wanglu<PERSON>01
 * @since : 2025-05-19
 * @email : <EMAIL>
 * @create_time : 2025-05-19
 */
public interface Mc04IslmContOrderItemService extends IService<Mc04IslmContOrderItemDO>{

}