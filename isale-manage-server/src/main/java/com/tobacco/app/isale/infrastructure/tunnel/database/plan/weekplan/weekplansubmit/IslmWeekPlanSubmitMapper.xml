<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.weekplan.weekplansubmit.IslmWeekPlanSubmitMapper">

    <resultMap id="BaseResultMap" type="com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.IslmWeekPlanSubmitQueryResult">
        <id column="mc04_week_plan_id" property="mc04WeekPlanId"/>
        <result column="mc04_week_plan_code" property="mc04WeekPlanCode"/>
        <result column="ma02_plan_month" property="ma02PlanMonth"/>
        <result column="ba_com_org_code" property="baComOrgCode"/>
        <result column="mc04_date_period_code" property="mc04DatePeriodCode"/>
        <result column="mc04_date_period_name" property="mc04DatePeriodName"/>
        <result column="mc04_date_period_begin_date" property="mc04DatePeriodBeginDate"/>
        <result column="mc04_date_period_end_date" property="mc04DatePeriodEndDate"/>
        <result column="mc04_sale_plan_status" property="mc04SalePlanStatus"/>
        <result column="ac_cgt_carton_code" property="acCgtCartonCode"/>
        <result column="ac_two_level_cig_code" property="acTwoLevelCigCode"/>
        <result column="ac_two_level_cig_name" property="acTwoLevelCigName"/>
        <result column="ac_cgt_tax_allot_price" property="acCgtTaxAllotPrice"/>
        <result column="ac_cgt_trade_price" property="acCgtTradePrice"/>
        <result column="ma02_cgt_pl_qty" property="ma02CgtPlQty"/>
        <result column="mc04_cgt_allot_plan_report_qty" property="mc04CgtAllotPlanReportQty"/>
        <result column="mc04_cgt_allot_plan_region_confirm_qty" property="mc04CgtAllotPlanRegionConfirmQty"/>
        <result column="mc04_cgt_allot_plan_pd_confirm_qty" property="mc04CgtAllotPlanPdConfirmQty"/>
        <result column="za_remark" property="zaRemark"/>
        <result column="ma02_toba_prod_trade_type_code" property="ma02TobaProdTradeTypeCode"/>
        <result column="ac_cgt_price_segment_name" property="acCgtPriceSegmentName"/>
        <result column="ac_cgt_price_segment_code" property="acCgtPriceSegmentCode"/>
        <result column="mc04_month_sale_plan_id" property="mc04MonthSalePlanId"/>
    </resultMap>

    <!-- 查询语句 -->
    <select id="findPlansByCondition" resultMap="BaseResultMap">
        SELECT
            m.mc04_month_sale_plan_id,
            m.ba_com_org_code,
            m.ac_two_level_cig_code,
            m.ac_two_level_cig_name,
            m.ac_cgt_tax_allot_price,
            m.ac_cgt_trade_price,
            m.ma02_cgt_pl_adjusted_qty as ma02_cgt_pl_qty,
            m.ma02_plan_month,
            m.ac_cgt_carton_code,
            m.ma02_toba_prod_trade_type_code,
            m.ac_cgt_price_segment_name,
            m.ac_cgt_price_segment_code,
            p.mc04_date_period_code,
            p.mc04_date_period_name,
            p.mc04_date_period_begin_date,
            p.mc04_date_period_end_date,
            w.mc04_week_plan_id,
            w.mc04_week_plan_code,
            w.mc04_cgt_allot_plan_report_qty,
            w.mc04_cgt_allot_plan_region_confirm_qty,
            w.mc04_cgt_allot_plan_pd_confirm_qty,
            w.mc04_sale_plan_status,
            w.za_remark
        FROM
            mc04_islm_month_sale_plan m
            INNER JOIN mc04_ind_data_period p 
                ON p.MD04_MG_OCCURRENCE_MONTH = m.ma02_plan_month
                    AND p.MC04_DATE_PERIOD_TYPE = 'T11'
            LEFT JOIN mc04_islm_week_sale_plan w 
                ON m.ba_com_org_code = w.ba_com_org_code
                AND m.ac_two_level_cig_code = w.ac_two_level_cig_code
                AND w.mc04_date_period_code = p.MC04_DATE_PERIOD_CODE
                AND w.ma02_plan_month = m.ma02_plan_month
                AND w.ma02_toba_prod_trade_type_code = '0'
            LEFT JOIN (
                SELECT
                    ba_com_org_code,
                    ac_two_level_cig_code,
                    mc04_date_period_code,
                    SUM(qty_req) AS qty_req
                FROM (
                    SELECT
                        a.ba_com_org_code,
                        b.ac_two_level_cig_code,
                        a.mc04_date_period_code,
                        SUM(b.md03_logt_tray_comb_tsp_ic_rlc_qty) AS qty_req
                    FROM
                        mc04_islmc_warehouse_stock_tran_bill a
                        JOIN mc04_islmc_warehouse_stock_tran_bill_item b 
                            ON a.mc04_warehouse_stock_tran_bill_id = b.mc04_warehouse_stock_tran_bill_id
                    WHERE
                        a.mc04_warehouse_stock_tran_bill_status IN ('10')
                    GROUP BY
                        a.ba_com_org_code,
                        b.ac_two_level_cig_code,
                        a.mc04_date_period_code
                    
                    UNION ALL
                    
                    SELECT
                        plan.ba_com_org_code,
                        plan.ac_two_level_cig_code,
                        plan.mc04_date_period_code,
                        COALESCE(plan.ma02_cgt_pl_adjusted_qty,0) - COALESCE(cont.md02_cgt_trade_cont_qty,0) AS qty_req
                    FROM
                        mc04_islmc_cgt_day_plan plan
                        LEFT JOIN (
                            SELECT
                                a.ba_com_org_code,
                                b.ac_two_level_cig_code,
                                a.mc04_date_period_code,
                                SUM(b.md02_cgt_trade_cont_qty) AS md02_cgt_trade_cont_qty
                            FROM
                                mc04_islm_cont_order a
                                JOIN mc04_islm_cont_order_item b 
                                    ON a.mc04_cont_order_id = b.mc04_cont_order_id
                            WHERE
                                a.mc04_cgt_trade_cont_status NOT IN ('80','90')
                            GROUP BY
                                a.ba_com_org_code,
                                b.ac_two_level_cig_code,
                                a.mc04_date_period_code
                        ) cont 
                            ON plan.ba_com_org_code = cont.ba_com_org_code
                            AND plan.ac_two_level_cig_code = cont.ac_two_level_cig_code
                            AND plan.mc04_date_period_code = cont.mc04_date_period_code
                ) combined
                GROUP BY
                    ba_com_org_code,
                    ac_two_level_cig_code,
                    mc04_date_period_code
            ) req 
                ON m.ba_com_org_code = req.ba_com_org_code
                AND m.ac_two_level_cig_code = req.ac_two_level_cig_code
                AND p.MC04_DATE_PERIOD_CODE = req.mc04_date_period_code
            LEFT JOIN (
                SELECT
                    a.ba_com_org_code,
                    b.ac_two_level_cig_code,
                    a.mc04_date_period_code,
                    SUM(b.md02_cgt_trade_cont_qty) AS qty_act
                FROM
                    mc04_islm_cont_order a
                    JOIN mc04_islm_cont_order_item b 
                        ON a.mc04_cont_order_id = b.mc04_cont_order_id
                WHERE
                    a.mc04_cgt_trade_cont_status BETWEEN '00' AND '60'
                GROUP BY
                    a.ba_com_org_code,
                    b.ac_two_level_cig_code,
                    a.mc04_date_period_code
            ) act 
                ON m.ba_com_org_code = act.ba_com_org_code
                AND m.ac_two_level_cig_code = act.ac_two_level_cig_code
                AND p.MC04_DATE_PERIOD_CODE = act.mc04_date_period_code
        WHERE
            m.ma02_plan_month = #{planMonth}
            AND m.mc04_month_sale_plan_status = '90'
            AND m.ma02_toba_prod_trade_type_code = '0'
            <if test="authorizedComIds != null and authorizedComIds.size() > 0">
                AND m.ba_com_org_code IN
                <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                    #{comId}
                </foreach>
            </if>
            <if test="acCgtCartonCodes != null and acCgtCartonCodes.size() > 0">
                AND m.ac_cgt_carton_code IN
                <foreach collection="acCgtCartonCodes" item="cartonCode" open="(" separator="," close=")">
                    #{cartonCode}
                </foreach>
            </if>
        ORDER BY
            m.ac_two_level_cig_code,
            p.MC04_DATE_PERIOD_CODE
    </select>

    <!-- 获取指定规格在某个月份的月计划总量 -->
    <select id="getMonthlyPlanTotal" resultType="java.util.Map">
        SELECT
            ac_two_level_cig_code,
            ma02_plan_month,
            ba_com_org_code,
            SUM(ma02_cgt_pl_adjusted_qty) AS monthlyTotal
        FROM
            mc04_islm_month_sale_plan
        WHERE
            mc04_month_sale_plan_status = '90'
            AND ma02_plan_month = #{planMonth}
            <if test="acTwoLevelCigCodes != null and acTwoLevelCigCodes.size() > 0">
                AND ac_two_level_cig_code IN
                <foreach collection="acTwoLevelCigCodes" item="cartonCode" open="(" separator="," close=")">
                    #{cartonCode}
                </foreach>
            </if>
            <if test="authorizedComIds != null and authorizedComIds.size() > 0">
                AND ba_com_org_code IN
                <foreach collection="authorizedComIds" item="comId" open="(" separator="," close=")">
                    #{comId}
                </foreach>
            </if>
    </select>

    <!-- 获取解锁状态 -->
    <select id="getUnlockStatus" resultType="java.util.Map">
        SELECT
            l.ba_com_org_code AS comCode,
            l.za_occurrence_month AS planMonth,
            l.ma02_as_lock_state AS unlocked
        FROM
            mc04_islm_month_sale_plan_lock l
        WHERE
            l.mz04_business_type_code = '50'
            AND l.za_occurrence_month in
            <foreach item="planMonth" collection="planMonths" open="(" separator="," close=")">
                #{planMonth}
            </foreach>
            AND l.ba_com_org_code IN
            <foreach item="comCode" collection="comCodes" open="(" separator="," close=")">
                #{comCode}
            </foreach>
    </select>

    <!-- 获取全国计划总量 -->
    <select id="getNationalPlanTotals" resultType="java.util.Map">
        SELECT
            ac_two_level_cig_code,
            SUM(ma02_cgt_pl_adjusted_qty) AS national_total
        FROM
            mc04_islm_month_sale_plan
        WHERE
            ac_two_level_cig_code IN
            <foreach item="cigCode" collection="cigCodes" open="(" separator="," close=")">
                #{cigCode}
            </foreach>
            AND mc04_month_sale_plan_status = '90'
        GROUP BY
            ac_two_level_cig_code
    </select>
    <select id="queryOrgStatus" resultType="java.util.Map">
		SELECT 
			BA_COM_ORG_CODE,
			MIN(mc04_sale_plan_status) AS STATUS
		FROM
			mc04_islm_week_sale_plan
		WHERE
			ma02_toba_prod_trade_type_code = '0'
			AND ma02_plan_month = #{ma02PlanMonth}
			AND icom_code = #{icomCode}
		GROUP BY
			ba_com_org_code
		HAVING
			MIN(mc04_sale_plan_status) >= #{mc04SalePlanStatus};
    </select>
</mapper>