/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 月计划锁定状态
 *
 * @Author: jinfuli
 * @Since: 2025-06-09
 * @Email: <EMAIL>
 * @Create: 2025-06-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islm_month_sale_plan_lock")
public class Mc04IslmMonthSalePlanLockDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 月计划上报数据编号 
             */

            @TableId(value = "mc04_month_sale_plan_id", type = IdType.NONE)
    @Field(value = "月计划上报数据编号 ", name = "月计划上报数据编号 ")
    private String mc04MonthSalePlanId;
                                /**
             * 烟草制品交易业务类型代码
             */

    @Field(value = "烟草制品交易业务类型代码", name = "烟草制品交易业务类型代码")
    private String ma02TobaProdTradeTypeCode;
                                /**
             * 商业公司编码
             */

    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;
                                /**
             * 业务类型代码
             */

    @Field(value = "业务类型代码", name = "业务类型代码")
    private String mz04BusinessTypeCode;
                                /**
             * 锁定状态
             */

    @Field(value = "锁定状态", name = "锁定状态")
    private String ma02AsLockState;


    @Field(name = "工业公司编码")
    private String icomCode;

    @Field(name = "创建人ID")
    private String createId;

    @Field(name = "创建人名称")
    private String createName;

    @Field(name = "创建时间")
    private String createTime;

    @Field(name = "修改人ID")
    private String updateId;

    @Field(name = "修改人名称")
    private String updateName;

    @Field(name = "最近修改时间")
    private String updateTime;



}
