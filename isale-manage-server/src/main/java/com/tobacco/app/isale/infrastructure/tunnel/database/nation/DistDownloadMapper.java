package com.tobacco.app.isale.infrastructure.tunnel.database.nation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tobacco.app.isale.third.service.api.inter.NationSubsysPullREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmItemREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmSeasonREQ;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 行业营销子系统配货数据查询 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface DistDownloadMapper extends BaseMapper<Map> {
    /**
     * 删除配货发运地区
     *
     * @param datas 数据
     */
    void deleteNationDistRegion(List<Map<String, Object>> datas);

    /**
     * 新增配货发运地区
     *
     * @param datas 数据
     */
    void insertNationDistRegion(List<Map<String, Object>> datas);

    /**
     * 删除配货收货地区
     *
     * @param datas 数据
     */
    void deleteNationDistReceiveRegion(List<Map<String, Object>> datas);

    /**
     * 新增配货收货地区
     *
     * @param datas 数据
     */
    void insertNationDistReceiveRegion(List<Map<String, Object>> datas);

    /**
     * 删除配货预览
     *
     * @param datas 数据
     */
    void deleteNationDistPreview(List<Map<String, Object>> datas);

    /**
     * 新增配货预览
     *
     * @param datas 数据
     */
    void insertNationDistPreview(List<Map<String, Object>> datas);

    /**
     * 删除配货预览卷烟
     *
     * @param datas 数据
     */
    void deleteNationDistPreviewItem(List<Map<String, Object>> datas);

    /**
     * 新增配货预览卷烟
     *
     * @param datas 数据
     */
    void insertNationDistPreviewItem(List<Map<String, Object>> datas);

    /**
     * 删除配货模式设置
     *
     * @param req 数据
     */
    void deleteNationParm(NationSubsysPullREQ req);

    /**
     * 新增配货模式设置
     *
     * @param datas 数据
     */
    void insertNationParm(List<Map<String, Object>> datas);

    /**
     * 删除配货模式设置卷烟
     *
     * @param req 数据
     */
    void deleteNationParmItem(NationSubsysPullREQ req);

    /**
     * 新增配货模式设置卷烟
     *
     * @param datas 数据
     */
    void insertNationParmItem(List<Map<String, Object>> datas);

    /**
     * 删除配货模式设置季节因子
     *
     * @param datas 数据
     */
    void deleteNationParmSeason(List<Map<String, Object>> datas);

    /**
     * 新增配货模式设置季节因子
     *
     * @param datas 数据
     */
    void insertNationParmSeason(List<Map<String, Object>> datas);

    /**
     * 删除配货订单
     *
     * @param datas 数据
     */
    void deleteNationDist(List<Map<String, Object>> datas);

    /**
     * 新增配货订单
     *
     * @param datas 数据
     */
    void insertNationDist(List<Map<String, Object>> datas);

    /**
     * 删除配货订单卷烟
     *
     * @param datas 数据
     */
    void deleteNationDistItem(List<Map<String, Object>> datas);

    /**
     * 新增配货订单卷烟
     *
     * @param datas 数据
     */
    void insertNationDistItem(List<Map<String, Object>> datas);

    /**
     * 获取配货参数业务主键
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<String> getIsmDistParms(List<Map<String, Object>> datas);

    /**
     * 删除配货参数业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmDistParm(List<String> datas);

    /**
     * 从中间表处理配货参数业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmDistParmFromNation(List<Map<String, Object>> datas);

    /**
     * 从中间表取配货参数
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<Mc04IslmcDistParmREQ> getIsmDistParmFromNation(List<Map<String, Object>> datas);

    /**
     * 删除配货参数规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmDistParmItem(List<String> datas);

    /**
     * 从中间表处理配货参数规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmDistParmItemFromNation(List<Map<String, Object>> datas);

    /**
     * 从中间表取配货参数规格业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<Mc04IslmcDistParmItemREQ> getIsmDistParmItemFromNation(List<Map<String, Object>> datas);

    /**
     * 删除配货参数季节因子业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteIsmDistParmSeason(List<String> datas);

    /**
     * 从中间表处理配货参数季节因子业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertIsmDistParmSeasonFromNation(List<Map<String, Object>> datas);

    /**
     * 从中间表取配货参数季节因子业务表
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<Mc04IslmcDistParmSeasonREQ> getIsmDistParmSeasonFromNation(List<Map<String, Object>> datas);

    /**
     * 确认的配货参数申请
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<String> getConfirmIsmDistParmApplys(List<Map<String, Object>> datas);

    /**
     * 驳回的配货参数申请
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<String> getRejectIsmDistParmApplys(List<Map<String, Object>> datas);

    /**
     * 从中间表处理配货参数申请表状态
     *
     * @param datas  数据
     * @param status 要改的状态
     * @return 返回数据
     */
    int updateIsmDistParmApply(List<String> datas, String status);

    /**
     * 确认的配货单号
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<String> getConfirmDistOrders(List<Map<String, Object>> datas);

    /**
     * 驳回的配货单号
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<String> getRejectDistOrders(List<Map<String, Object>> datas);

    /**
     * 作废的配货单号
     *
     * @param datas 数据
     * @return 返回数据
     */
    List<String> getCancelDistOrders(List<Map<String, Object>> datas);

    /**
     * 更新网配订单商业确认量和最终量
     *
     * @param datas 数据
     * @return 返回数据
     */
    int updateDistOrderQty(List<String> datas);

    /**
     * 更新网配订单状态
     *
     * @param datas  数据
     * @param status 状态
     * @return 返回数据
     */
    int updateDistOrderStatus(List<String> datas, String status);

    /**
     * 已审核的配货单更新日计划表生效状态
     *
     * @param datas 数据
     * @return 返回数据
     */
    int updateIsmItemDayPlanEffect(List<String> datas);

    /**
     * 已驳回到保存的配货单和作废的配货单删除日计划表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int removeIsmItemDayPlans(List<String> datas);

    /**
     * 插入商业确认或者驳回操作记录
     *
     * @param datas  数据
     * @param status 状态
     * @return 返回数据
     */
    int insertIsmDistLog(List<String> datas, String status);

    /**
     * 获取上传行业子系统的配货参数数据
     *
     * @param mc04CgtDistParmApplyCode 配货参数申请编码
     * @return 返回数据
     */
    Map<String, Object> getParm(String mc04CgtDistParmApplyCode);

    /**
     * 获取上传行业子系统的配货参数卷烟数据
     *
     * @param mc04CgtDistParmApplyCode  配货参数申请编码
     * @param supmemberCode             卷烟供方交易会员代码
     * @param reqmemberCode             卷烟需方交易会员代码
     * @param distregionCode            网配订单发货地区代码
     * @param md02DistReceiveregionCode 配货收货地区编码
     * @return 返回数据
     */
    List<Map<String, Object>> getParmItems(String mc04CgtDistParmApplyCode,
                                           String supmemberCode, String reqmemberCode,
                                           String distregionCode, String md02DistReceiveregionCode);

    /**
     * 获取上传行业子系统的配货参数季节系数数据
     *
     * @param mc04CgtDistParmApplyCode  配货参数申请编码
     * @param supmemberCode             卷烟供方交易会员代码
     * @param reqmemberCode             卷烟需方交易会员代码
     * @param distregionCode            网配订单发货地区代码
     * @param md02DistReceiveregionCode 配货收货地区编码
     * @return 返回数据
     */
    List<Map<String, Object>> getParmSeasons(String mc04CgtDistParmApplyCode,
                                             String supmemberCode, String reqmemberCode,
                                             String distregionCode, String md02DistReceiveregionCode);


    /**
     * 查询传输行业子系统新增配货单
     *
     * @param mc04CgtDistOrderCode 配货单号
     * @return 返回数据
     */
    Map<String, Object> getAddDist(String mc04CgtDistOrderCode);

    /**
     * 查询传输行业子系统删除配货单
     *
     * @param mc04CgtDistOrderCode 配货单号
     * @return 返回数据
     */
    Map<String, Object> getDeleteDist(String mc04CgtDistOrderCode);

    /**
     * 查询传输行业子系统更新配货单
     *
     * @param mc04CgtDistOrderCode 配货单号
     * @return 返回数据
     */
    Map<String, Object> getUpdateDist(String mc04CgtDistOrderCode);

    /**
     * 查询传输行业子系统配货单规格列表
     *
     * @param mc04CgtDistOrderCode 配货单号
     * @return 返回数据
     */
    List<Map<String, Object>> getDistItems(String mc04CgtDistOrderCode);

}
