/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplansubmit;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanQuarterItem;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmit;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitBasicItem;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: hzs
 * @Date: 2025/8/5
 * @Description:
 */
@Mapper
public interface MonthPlanSubmitMapper {

    String getPlanSubjectType(String year);

    List<MonthPlanSubmitBasicItem> getBacicItem(String year, String currentMonth, List<String> comIds, String subjectType, String quarter, String halfYear, String isCigar, String nextQuarter, String lastHalfYear, String isAdj);

    @DS("dws")
        //todo 联合查询库加模式名
    List<MonthPlanSubmit> getPurchSaleStk(String currentMonth, String nextMonth3, String monthMinus1, String monthMinus2, String monthMinus3, String sameMonth1, String sameMonth2, String sameMonth3, List<String> comIds, List<String> monthList);

    List<MonthPlanSubmit> getLastThreeMonthQty(String monthMinus1, String monthMinus2, String monthMinus3, String icomCode, String isCigar, List<String> comIds, List<String> monthList);

    List<MonthPlanSubmitBasicItem> getSelfYearPlan(List<String> comIds, String halfYear, String isCigar, String lastHalfYear);

    List<Map> getComItem(String comId);

    @DS("dws")
    List<Map> getParamConfig(String year, String month, String comId);

    @DS("dws")
    List<Map> getPurchSaleStkDay(String lastDay, String comId);

    List<MonthPlanQuarterItem> getQuarterItemList(String year, List<String> comIds, String subjectType, List<String> quarters);

    List<MonthPlanQuarterItem> getLastMonthQty(List<String> comIds, String month, String icomCode);



}
