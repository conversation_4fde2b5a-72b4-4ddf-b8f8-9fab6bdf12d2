/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanreview;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanreview.Mc04MonthPlanReviewQuery;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanreview.Mc04MonthPlanReviewQueryResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: jxy
 * @Date: 2025/8/5
 * @Description:
 */
@Mapper
public interface IslmMonthPlanReviewMapper {

    /**
     * 获取月计划上报品牌部审核列表
     *
     * @param condition
     * @return
     */
    List<Mc04MonthPlanReviewQueryResult> getMonthPlanReviewList(Mc04MonthPlanReviewQuery condition);

    /**
     * 查询年度计划最新主题类型
     *
     * @param zaOccurrenceYear
     * @param ma02TobaProdTradeTypeCode
     * @return
     */
    String getPlanSubjectType(String zaOccurrenceYear, String ma02TobaProdTradeTypeCode);

    /**
     * 在联合查询库, 获取月计划上报品牌部审核列表
     *
     * @param acCgtCartonCode
     * @param mc04PurchaseSaleStkDate
     * @return
     */
    @DS("dws")
    List<Map<String, Object>> getSaleStkDayList(String acCgtCartonCode, String mc04PurchaseSaleStkDate);

    /**
     * 在联合查询库, induniondb查最近汇总日期
     *
     * @return
     */
    @DS("dws")
    String getDataDate();

    /**
     * 查询行情价格
     *
     * @param acCgtCartonCode 卷烟编码
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @return
     */
    List<Map<String, Object>> getMarketPrice(String acCgtCartonCode, String startTime, String endTime);

    /**
     * 在联合查询库查询当前月实际销量和当前月商业库存
     *
     * @param baComOrgCodeList
     * @param zaOccurrenceMonth
     * @return
     */
    @DS("dws")
    List<Map<String, Object>> getPurchSaleStk(List<String> baComOrgCodeList, String zaOccurrenceMonth, String acCgtCartonCode);

    /**
     * 在联合查询库查询调拨计划执行量
     *
     * @param baComOrgCodeList
     * @param zaOccurrenceMonth
     * @return
     */
    @DS("dws")
    List<Map<String, Object>> getPlanMAllotActualtyExe(List<String> baComOrgCodeList, String zaOccurrenceMonth);

    @DS("dws")
    List<Map<String, Object>> getPurchSaleStkList(List<String> baComOrgCodeList, String zaOccurrenceMonth, List<String> acCgtCartonCodes);
}
