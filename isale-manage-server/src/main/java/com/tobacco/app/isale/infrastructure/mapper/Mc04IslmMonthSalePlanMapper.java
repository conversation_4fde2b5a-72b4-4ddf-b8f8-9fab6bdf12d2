/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;

import java.util.List;
import java.util.Map;

/**
 * @Description: Mapper 接口
 *
 * @Author: jinfuli
 * @Since: 2025-06-12
 * @Email: <EMAIL>
 * @Create: 2025-06-12
 */
public interface Mc04IslmMonthSalePlanMapper extends BaseMapper<Mc04IslmMonthSalePlanDO> {

    /**
     * 查询卷烟状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @return
     */
    List<Map<String, Object>> queryCgtStatus(String ma02PlanMonth, String icomCode);

    /**
     * 查询城市状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @param mc04MonthSalePlanStatus
     * @return
     */
    List<Map<String, Object>> queryOrgStatus(String ma02PlanMonth, String icomCode, String mc04MonthSalePlanStatus);
}