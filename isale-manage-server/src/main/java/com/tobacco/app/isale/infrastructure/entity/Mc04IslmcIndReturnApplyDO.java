package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 退库申请单 视图层对象/值对象
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islmc_ind_return_apply")
public class Mc04IslmcIndReturnApplyDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 工业退库申请编码
     */
                @TableId(value = "mc04_ind_return_apply_id", type = IdType.NONE)
    @Field(value = "工业退库申请编码", name = "工业退库申请编码")
    private String mc04IndReturnApplyId;

    /**
     * 烟草制品交易退货类型代码
     */
    @Field(value = "烟草制品交易退货类型代码", name = "烟草制品交易退货类型代码")
    private String md02TobaProdTradeReturnTypeCpde;

    /**
     * 商业公司编码
     */
    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;

    /**
     * 卷烟交易合同代码
     */
    @Field(value = "卷烟交易合同代码", name = "卷烟交易合同代码")
    private String md02CgtTradeContNo;

    /**
     * 工业退库申请状态
     */
    @Field(value = "工业退库申请状态", name = "工业退库申请状态")
    private String mc04IndReturnApplyStatus;

    /**
     * 备注
     */
    @Field(value = "备注", name = "备注")
    private String zaRemark;

    /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

    /**
     * 创建人ID
     */
    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

    /**
     * 创建人名称
     */
    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @Field(value = "创建时间", name = "创建时间")
    private String createTime;

    /**
     * 修改人ID
     */
    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;

    /**
     * 修改人名称
     */
    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

    /**
     * 修改时间
     */
    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;


}
