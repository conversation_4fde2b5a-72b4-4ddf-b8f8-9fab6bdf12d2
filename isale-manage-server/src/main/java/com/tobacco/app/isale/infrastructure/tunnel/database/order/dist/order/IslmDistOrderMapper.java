/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.order;

import com.inspur.ind.base.CustomPage;
import com.tobacco.app.isale.domain.model.order.dist.order.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create_time : 2025/06/04 09:58
 * @description : 配货订单持久层
 */
@Mapper
public interface IslmDistOrderMapper {

    /**
     * 分页查询网配订单信息
     *
     * @param page              分页信息
     * @param domainReq 分页查询请求参数
     */
    CustomPage<DistOrder> queryDistOrderWithPage(CustomPage<DistOrder> page,@Param("param") DistOrderPage domainReq);

    /**
     * 获取周期内日计划量
     */
    List<DistOrderCols>  getDayPlanData(DistOrderColsParam param);

    /**
     * 查询网配订单信息
     * @param distOrder
     * @return
     */
    List<DistOrder> queryDistOrderList(DistOrder distOrder);

    /**
     * 查询二级牌号的日计划列表
     *
     * @return 日计划列表
     */
    List<DistOrderDayPlan> getTwoLevelDayPlans();
}
