/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 备货移库需求规格
 *
 * @Author: hujiarong
 * @Since: 2025-08-27
 * @Email: <EMAIL>
 * @Create: 2025-08-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_nc_stock_move_demand_item")
public class Mc04IslmcNcStockMoveDemandItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 备货移库需求规格信息编码
             */

            @TableId(value = "mc04_nc_stock_move_demand_item_id", type = IdType.NONE)
    @Field(value = "备货移库需求规格信息编码", name = "备货移库需求规格信息编码")
    private String mc04NcStockMoveDemandItemId;
                                /**
             * 备货移库需求编码
             */

    @Field(value = "备货移库需求编码", name = "备货移库需求编码")
    private String mc04NcStockMoveDemandId;
                                /**
             * 卷烟代码（条）
             */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
                                /**
             * 卷烟名称
             */

    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;
                                /**
             * 二级牌号编码
             */

    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;
                                /**
             * 二级牌号名称
             */

    @Field(value = "二级牌号名称", name = "二级牌号名称")
    private String acTwoLevelCigName;
                                /**
             * 卷烟含税调拨价格
             */

    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;
                                /**
             * 商业可供库容
             */

    @Field(value = "商业可供库容", name = "商业可供库容")
    private BigDecimal mc04ComAvailableStorage;
                                /**
             * 备货移库需求量
             */

    @Field(value = "备货移库需求量", name = "备货移库需求量")
    private BigDecimal mc04NcStockMoveDemandQty;
                                /**
             * 预计年末商业库存
             */

    @Field(value = "预计年末商业库存", name = "预计年末商业库存")
    private BigDecimal mc04YearFoEndComStkQty;
                                                                                                    

}
