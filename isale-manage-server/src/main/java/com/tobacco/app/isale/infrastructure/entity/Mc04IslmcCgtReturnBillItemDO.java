package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 卷烟退货规格信息 视图层对象/值对象
 * </p>
 *
 * @Author: qifengyu
 * @Since: 2025-07-25
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islmc_cgt_return_bill_item")
public class Mc04IslmcCgtReturnBillItemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 卷烟退货规格信息编码
     */
                @TableId(value = "mc04_cgt_return_bill_item_id", type = IdType.NONE)
    @Field(value = "卷烟退货规格信息编码", name = "卷烟退货规格信息编码")
    private String mc04CgtReturnBillItemId;

    /**
     * 卷烟退货编码
     */
    @Field(value = "卷烟退货编码", name = "卷烟退货编码")
    private String mc04CgtReturnBillId;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;

    /**
     * 二级牌号编码
     */
    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;

    /**
     * 二级牌号名称
     */
    @Field(value = "二级牌号名称", name = "二级牌号名称")
    private String acTwoLevelCigName;

    /**
     * 卷烟含税调拨价格
     */
    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 上报数量
     */
    @Field(value = "上报数量", name = "上报数量")
    private BigDecimal mc04CgtReturnBillSaleAreaReqQty;

    /**
     * 确认数量
     */
    @Field(value = "确认数量", name = "确认数量")
    private BigDecimal mc04CgtReturnBillSaleAreaAuditQty;

    /**
     * 审核数量
     */
    @Field(value = "审核数量", name = "审核数量")
    private BigDecimal mc04CgtReturnBillSaleCenterAuditQty;


    /**
     * 卷烟工业退货入库量（万支）
     */
    @Field(value = "卷烟工业退货入库量（万支）", name = "卷烟工业退货入库量（万支）")
    @TableField("mc05_cgt_10th_ind_reback_in_qty")
    private BigDecimal mc05Cgt10thIndRebackInQty;

    /**
     * 卷烟发运单准运证号
     */
    @Field(value = "卷烟发运单准运证号", name = "卷烟发运单准运证号")
    private String md02CgtTransTranscertNo;

    /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

    /**
     * 创建人ID
     */
    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

    /**
     * 创建人名称
     */
    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @Field(value = "创建时间", name = "创建时间")
    private String createTime;

    /**
     * 修改人ID
     */
    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;

    /**
     * 修改人名称
     */
    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

    /**
     * 修改时间
     */
    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;


}
