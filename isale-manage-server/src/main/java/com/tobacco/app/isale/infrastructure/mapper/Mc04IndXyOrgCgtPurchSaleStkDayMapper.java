/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tobacco.app.isale.domain.model.agreement.sign.ManageIndXyOrgCgtPurchSaleStkDay;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndXyOrgCgtPurchSaleStkDayDO;

import java.util.List;


/**
 * <AUTHOR> wangluhao01
 * @description : Mapper 接口
 * @email : <EMAIL>
 * @create_time : 2025-05-13
 * @since : 2025-05-13
 */
public interface Mc04IndXyOrgCgtPurchSaleStkDayMapper extends BaseMapper<Mc04IndXyOrgCgtPurchSaleStkDayDO> {


    /**
     * @param tableName 表名
     * @return String
     * <AUTHOR> wanglu<PERSON>01
     * @create_time : 2025-05-14 08:58:37
     * @description : 获取某张汇总表的汇总结束日期
     */

    String getEndDate(String tableName);

    /**
     * 获取前半年销售
     *
     * @param baComOrgCode 公司机构代码
     * @param endDate      结束日期
     * @return 前半年销售
     */
    List<ManageIndXyOrgCgtPurchSaleStkDay> getFirstHalfYearSalesGroupByProduct(
            String baComOrgCode, String endDate);

}