<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanMapper">

    <select id="queryCgtStatus" resultType="java.util.Map">
	    SELECT
			  AC_CGT_CARTON_CODE,
			  SUM(CASE WHEN MC04_MONTH_SALE_PLAN_STATUS = '30' THEN 1 ELSE 0 END) TODO_COUNT,
              SUM(CASE WHEN MC04_MONTH_SALE_PLAN_STATUS > '30' THEN 1 ELSE 0 END) DONE_COUNT
   		  FROM
			  MC04_ISLM_MONTH_SALE_PLAN
		 WHERE
			  MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
			  AND MC04_CGT_SALE_PLAN_VERSION = '1'
			  AND MA02_PLAN_MONTH = #{ma02PlanMonth}
			  AND MC04_MONTH_SALE_PLAN_STATUS >= '30'
			  AND ICOM_CODE = #{icomCode}
		GROUP BY
			AC_CGT_CARTON_CODE
    </select>
    <select id="queryOrgStatus" resultType="java.util.Map">
		SELECT 
			BA_COM_ORG_CODE,
			MIN(MC04_MONTH_SALE_PLAN_STATUS) AS STATUS
		FROM
			MC04_ISLM_MONTH_SALE_PLAN
		WHERE
			MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
			AND MC04_CGT_SALE_PLAN_VERSION = '1'
			AND MA02_PLAN_MONTH = #{ma02PlanMonth}
			AND ICOM_CODE = #{icomCode}
		GROUP BY
			BA_COM_ORG_CODE
		HAVING
			MIN(MC04_MONTH_SALE_PLAN_STATUS) >= #{mc04MonthSalePlanStatus};
    </select>
</mapper>
