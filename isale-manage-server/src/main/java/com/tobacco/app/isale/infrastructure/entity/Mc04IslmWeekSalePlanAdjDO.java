/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 周销售计划实体类
 * @Author: jxy
 * @Since: 2025-8-5
 * @Create: 2025-8-5
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islm_week_sale_plan_adj")
public class Mc04IslmWeekSalePlanAdjDO extends BaseFactor {

    private static final long serialVersionUID = 5226479429431032463L;

    /**
     * 周计划ID（主键）
     * 对应数据库列：mc04_week_plan_id
     */
    @TableId(value = "mc04_month_sale_plan_adj_item_id", type = IdType.NONE)
    @Field(value = "周计划调整上报明细编号")
    private String mc04MonthSalePlanAdjItemId;

    /**
     * 周销售计划调整ID
     */
    @Field(value = "周销售计划调整ID，关联调整主表，长度32")
    private String mc04MonthSalePlanAdjId;

    /**
     * 是否最新调整标识
     */
    @Field(value = "是否最新调整标识，长度1，如1表示是，0表示否")
    private String mc04IsLatestAdjust;

    /**
     * 烟草产品贸易类型编码
     */
    @Field(value = "烟草产品贸易类型编码，长度2")
    private String ma02TobaProdTradeTypeCode;

    /**
     * 计划月份
     */
    @Field(value = "计划月份，格式为YYYYMM，长度6")
    private String ma02PlanMonth;

    /**
     * 商业机构编码
     */
    @Field(value = "商业机构编码，关联机构信息表，长度32")
    private String baComOrgCode;

    /**
     * 日期周期编码
     */
    @Field(value = "日期周期编码，标识调整所属周期，长度32")
    private String mc04DatePeriodCode;

    /**
     * 周期开始日期
     */
    @Field(value = "周期开始日期，格式为YYYYMMDD，长度8")
    private String mc04DatePeriodBeginDate;

    /**
     * 周期结束日期
     */
    @Field(value = "周期结束日期，格式为YYYYMMDD，长度8")
    private String mc04DatePeriodEndDate;

    /**
     * 卷烟纸箱编码
     */
    @Field(value = "卷烟纸箱编码，对应具体产品包装，长度32")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟产品名称，长度32")
    private String acCgtName;

    /**
     * 二级卷烟分类编码
     */
    @Field(value = "二级卷烟分类编码，用于产品分类统计，长度32")
    private String acTwoLevelCigCode;

    /**
     * 二级卷烟分类名称
     */
    @Field(value = "二级卷烟分类名称，长度32")
    private String acTwoLevelCigName;

    /**
     * 卷烟价格段编码
     */
    @Field(value = "卷烟价格段编码，区分高中低价位，长度32")
    private String acCgtPriceSegmentCode;

    /**
     * 卷烟价格段名称
     */
    @Field(value = "卷烟价格段名称，如高端、中端、低端，长度32")
    private String acCgtPriceSegmentName;

    /**
     * 卷烟含税调拨价
     */
    @Field(value = "卷烟含税调拨价，精度18位，小数6位")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟交易价
     */
    @Field(value = "卷烟交易价，精度18位，小数6位")
    private BigDecimal acCgtTradePrice;

    /**
     * 卷烟计划分解原始数量
     */
    @Field(value = "卷烟计划分解原始数量，精度18位，小数6位")
    private BigDecimal ma02CgtPlResolOrigplanQty;

    /**
     * 卷烟计划调整后数量
     */
    @Field(value = "卷烟计划调整后数量，精度18位，小数6位")
    private BigDecimal ma02CgtPlAdjustedQty;
}
