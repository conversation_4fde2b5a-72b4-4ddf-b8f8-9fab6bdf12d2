/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.plan.yearplan.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 年度品牌规划数据通道
 * <AUTHOR> loongxi
 * @date ： 2025/08/12
 */
@Mapper
public interface IslmStockInitMapper {
    /**
     * 查询指定年度的库存数据
     * @param year 年度
     * @return 年度库存数据
     **/
    @DS("dws")
    List<IslmStockItemHistoryModel> selectStockHistoryByYear(String year);

    /**
     * 获取指定年度的销量数据
     * @param year 年度
     * @return 年度销量数据
     **/
    @DS("dws")
    List<IslmSaleItemHistoryModel> selectSaleHistoryByYear(String year);

    /**
     * 获取指定年度的规划数据
     * @param year 年度
     * @return 年度规划数据
     **/
    @DS("dws")
    List<IslmPlItemHistoryModel> selectPlHistoryByYear(String year);

    /**
     * 获取指定年份地市公司的销量数据
     * @param year 年度
     * @param month 月份
     * @return 销量数据
     **/
    List<IslmSaleItemHistoryMonthModel> selectHistoryMonthSaleItem(String year, String month);

    /**
     * 获取指定年份地市公司的调拨数据
     * @param year 年度
     * @param month 月份
     * @return 调拨数据
     **/
    List<IslmPlItemHistoryMonthModel> selectHistoryMonthPlItem(String year, String month);
}
