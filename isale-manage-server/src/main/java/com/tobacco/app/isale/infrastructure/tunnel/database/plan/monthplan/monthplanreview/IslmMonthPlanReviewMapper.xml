<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanreview.IslmMonthPlanReviewMapper">

    <select id="getMonthPlanReviewList" resultType="com.tobacco.app.isale.domain.model.plan.monthplan.monthplanreview.Mc04MonthPlanReviewQueryResult">
		SELECT
			t1.n1_normal_mc04_month_sale_plan_item_id,
			t1.n1_transfer_mc04_month_sale_plan_item_id,
			t1.n1_zero_mc04_month_sale_plan_item_id,
			t1.ba_prov_org_code,
			t1.ba_com_org_code,
			t3.md02_cgt_trade_cont_qty qty_xy_exe,
			t1.qty_plan_this_month,
			t1.qty_plan_this_month - t4.ma02_cgt_pl_adjusted_qty + t5.md02_cgt_trade_cont_qty qty_plan_remain_this_month,
			t6.ma02_cgt_pl_adjusted_qty qty_xy_exe_last,
			t7.md02_cgt_trade_cont_qty qty_cont_cancel_last,
			t2.ma02_cgt_pl_adjusted_qty qty_plan_quarter,
			t1.qty_plan_last_month,
			t1.n1_normal_mc04_cgt_allot_plan_report_qty,
			t1.n1_transfer_mc04_cgt_allot_plan_report_qty,
			t1.n1_zero_mc04_cgt_allot_plan_report_qty,
			t1.n1_normal_mc04_cgt_allot_plan_region_confirm_qty,
			t1.n1_transfer_mc04_cgt_allot_plan_region_confirm_qty,
			t1.n1_zero_mc04_cgt_allot_plan_region_confirm_qty,
			t1.n1_normal_mc04_cgt_allot_plan_bd_confirm_qty,
			t1.n1_transfer_mc04_cgt_allot_plan_bd_confirm_qty,
			t1.n1_zero_mc04_cgt_allot_plan_bd_confirm_qty,
			t1.mc04_cgt_sale_plan_region_confirm_qty as mc04CgtSalePlanRegionConfirmQty,
			t1.ac_two_level_cig_code as acTwoLevelCigCode,
			t1.ac_two_level_cig_name as acTwoLevelCigName,
		    t1.mc04_month_sale_plan_type
		FROM
			(SELECT
				ba_prov_org_code,
				ba_com_org_code,
				ac_two_level_cig_code,
		        ac_two_level_cig_name,
				mc04_month_sale_plan_type,
				CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '10' THEN mc04_month_sale_plan_item_id END n1_normal_mc04_month_sale_plan_item_id,
				CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '20' THEN mc04_month_sale_plan_item_id END n1_transfer_mc04_month_sale_plan_item_id,
				CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '30' THEN mc04_month_sale_plan_item_id END n1_zero_mc04_month_sale_plan_item_id,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '10' THEN mc04_cgt_allot_plan_report_qty ELSE NULL END) n1_normal_mc04_cgt_allot_plan_report_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '20' THEN mc04_cgt_allot_plan_report_qty ELSE NULL END) n1_transfer_mc04_cgt_allot_plan_report_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_report_qty ELSE NULL END) n1_zero_mc04_cgt_allot_plan_report_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '10' THEN mc04_cgt_allot_plan_region_confirm_qty ELSE NULL END) n1_normal_mc04_cgt_allot_plan_region_confirm_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '20' THEN mc04_cgt_allot_plan_region_confirm_qty ELSE NULL END) n1_transfer_mc04_cgt_allot_plan_region_confirm_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_region_confirm_qty ELSE NULL END) n1_zero_mc04_cgt_allot_plan_region_confirm_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '10' THEN mc04_cgt_allot_plan_bd_confirm_qty ELSE NULL END) n1_normal_mc04_cgt_allot_plan_bd_confirm_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_bd_confirm_qty ELSE NULL END) n1_transfer_mc04_cgt_allot_plan_bd_confirm_qty,
				SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_bd_confirm_qty ELSE NULL END) n1_zero_mc04_cgt_allot_plan_bd_confirm_qty,
				SUM(CASE WHEN ma02_plan_month = #{zaOccurrenceMonth} THEN ma02_cgt_pl_adjusted_qty ELSE 0 END) qty_plan_this_month,
				SUM(CASE WHEN ma02_plan_month = #{monthLast} THEN ma02_cgt_pl_adjusted_qty ELSE 0 END) qty_plan_last_month,
		        SUM(CASE WHEN ma02_plan_month = #{planMonth} AND mc04_month_sale_plan_type = '10' THEN mc04_cgt_sale_plan_region_confirm_qty ELSE 0 END) mc04_cgt_sale_plan_region_confirm_qty
			FROM
				MC04_ISLM_MONTH_SALE_PLAN
			WHERE
				MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
				AND MC04_CGT_SALE_PLAN_VERSION = '1'
				AND MA02_PLAN_MONTH IN (#{monthLast}, #{zaOccurrenceMonth}, #{planMonth})
				AND AC_CGT_CARTON_CODE = #{acCgtCartonCode}
				AND ICOM_CODE = #{icomCode}
				<if test="baComOrgCodes != null and baComOrgCodes.size() > 0">
					AND ba_com_org_code in
					<foreach item="item" index="index" collection="baComOrgCodes"
							 open="(" close=")" separator=",">
						#{item}
					</foreach>
				</if>
				<choose>
					<when test="acCgtCartonCodeStatus != null and acCgtCartonCodeStatus == 0">
						AND MC04_MONTH_SALE_PLAN_STATUS = '30'
					</when>
					<when test="acCgtCartonCodeStatus != null and acCgtCartonCodeStatus == 1">
						AND MC04_MONTH_SALE_PLAN_STATUS > '30'
					</when>
					<otherwise>
						AND MC04_MONTH_SALE_PLAN_STATUS >= '30'
					</otherwise>
				</choose>
			GROUP BY
				BA_PROV_ORG_CODE,
				BA_COM_ORG_CODE
			) T1
		LEFT JOIN
			(SELECT
				ISP.MC04_ORG_TYPE_CODE,
				SUM(ISPI.MA02_CGT_PL_ADJUSTED_QTY) MA02_CGT_PL_ADJUSTED_QTY
			FROM
				MC04_ISLM_SALE_PLAN ISP,
				MC04_ISLM_SALE_PLAN_ITEM ISPI
			WHERE
				ISP.MC04_SALE_PLAN_ID = ISPI.MC04_SALE_PLAN_ID
				AND ISP.MC04_IS_LASTEST_VERSION = '1'
				AND ISP.ZA_OCCURRENCE_YEAR = #{zaOccurrenceYear}
				AND ISP.MA02_TOBA_PROD_TRADE_TYPE_CODE = #{ma02TobaProdTradeTypeCode}
				AND ISP.MC04_SALE_PLAN_STATUS = '90'
				AND ISP.MC04_CGT_SALE_FO_PERIOD_TYPE = 'T03'
				AND ISP.MC04_CGT_SALE_FO_PERIOD_CODE = #{mc04CgtSaleFoPeriodCode}
				AND ISP.MC04_PLAN_SUBJECT_TYPE = #{mc04PlanSubjectType}
				AND ISP.MC04_ORG_TYPE_KIND = '02'
				AND ISPI.AC_CGT_CARTON_CODE = #{acCgtCartonCode}
			GROUP BY
				ISP.MC04_ORG_TYPE_CODE
			) T2 ON T1.BA_COM_ORG_CODE = T2.MC04_ORG_TYPE_CODE
		LEFT JOIN
			(SELECT
				ICO.BA_COM_ORG_CODE,
				SUM(ICOI.MD02_CGT_TRADE_CONT_QTY) AS MD02_CGT_TRADE_CONT_QTY
			FROM
				MC04_ISLM_CONT_ORDER ICO,
				MC04_ISLM_CONT_ORDER_ITEM ICOI
			WHERE
				ICO.MC04_CONT_ORDER_ID = ICOI.MC04_CONT_ORDER_ID
				AND ICO.MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
				AND ICOI.AC_CGT_CARTON_CODE = #{acCgtCartonCode}
				AND ICO.MC04_CGT_XY_PERIOD_CODE = #{mc04CgtXyPeriodCode}
				AND ICO.MC04_CGT_TRADE_CONT_STATUS = '10'
			GROUP BY
				ICO.BA_COM_ORG_CODE
			) T3 ON T1.BA_COM_ORG_CODE = T3.BA_COM_ORG_CODE
		LEFT JOIN
			(SELECT
				ICDP.BA_COM_ORG_CODE,
				SUM(ICDP.MA02_CGT_PL_ADJUSTED_QTY) AS MA02_CGT_PL_ADJUSTED_QTY
			FROM
				MC04_ISLMC_CGT_DAY_PLAN ICDP
			WHERE
				ICDP.MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
				AND ICDP.AC_CGT_CARTON_CODE = #{acCgtCartonCode}
				AND ICDP.MA02_PLAN_MONTH = #{zaOccurrenceMonth}
			GROUP BY
				ICDP.BA_COM_ORG_CODE
			) T4 ON T1.BA_COM_ORG_CODE = T4.BA_COM_ORG_CODE
		LEFT JOIN
			(SELECT
				ICO.BA_COM_ORG_CODE,
				SUM(ICOI.MD02_CGT_TRADE_CONT_QTY) AS MD02_CGT_TRADE_CONT_QTY
			FROM
				MC04_ISLM_CONT_ORDER ICO,
				MC04_ISLM_CONT_ORDER_ITEM ICOI
			WHERE
				ICO.MC04_CONT_ORDER_ID = ICOI.MC04_CONT_ORDER_ID
				AND ICO.MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
				AND ICOI.AC_CGT_CARTON_CODE = #{acCgtCartonCode}
				AND ICO.MA02_PLAN_MONTH = #{planMonth}
				AND ICO.MC04_CGT_TRADE_CONT_STATUS IN ('80', '90')
			GROUP BY
				ICO.BA_COM_ORG_CODE
			) T5 ON T1.BA_COM_ORG_CODE = T5.BA_COM_ORG_CODE
		LEFT JOIN
			(SELECT 
				ICDP.BA_COM_ORG_CODE,
				SUM(ICDP.MA02_CGT_PL_ADJUSTED_QTY) AS MA02_CGT_PL_ADJUSTED_QTY
			FROM 
				MC04_ISLMC_CGT_DAY_PLAN ICDP
			WHERE 
				ICDP.MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
				AND ICDP.AC_CGT_CARTON_CODE = #{acCgtCartonCode}
				AND ICDP.MC04_CGT_XY_PERIOD_CODE = #{mc04CgtXyPeriodCode}
			GROUP BY 
				ICDP.BA_COM_ORG_CODE
			) T6 ON T1.BA_COM_ORG_CODE = T4.BA_COM_ORG_CODE
		LEFT JOIN
			(SELECT 
				ICO.BA_COM_ORG_CODE,
				SUM(ICOI.MD02_CGT_TRADE_CONT_QTY) AS MD02_CGT_TRADE_CONT_QTY
			FROM 
				MC04_ISLM_CONT_ORDER ICO,
				MC04_ISLM_CONT_ORDER_ITEM ICOI
			WHERE 
				ICO.MC04_CONT_ORDER_ID = ICOI.MC04_CONT_ORDER_ID
				AND ICO.MA02_TOBA_PROD_TRADE_TYPE_CODE = '0'
				AND ICOI.AC_CGT_CARTON_CODE = #{acCgtCartonCode}
				AND ICO.MC04_CGT_XY_PERIOD_CODE = #{mc04CgtXyPeriodCode}
				AND ICO.MC04_CGT_TRADE_CONT_STATUS IN ('80', '90')
			GROUP BY
				ICO.BA_COM_ORG_CODE
			) T7 ON T1.BA_COM_ORG_CODE = T5.BA_COM_ORG_CODE
    </select>
	<select id="getPlanSubjectType" resultType="String">
		SELECT 
			MAX(ISP.MC04_PLAN_SUBJECT_TYPE) AS MC04_PLAN_SUBJECT_TYPE
		FROM 
			MC04_ISLM_SALE_PLAN ISP
		WHERE 
			ISP.MC04_IS_LASTEST_VERSION = '1'
			AND ISP.ZA_OCCURRENCE_YEAR = #{zaOccurrenceYear}
			AND ISP.MA02_TOBA_PROD_TRADE_TYPE_CODE = #{ma02TobaProdTradeTypeCode}
			AND ISP.MC04_SALE_PLAN_STATUS = '90'
	</select>
	<select id="getSaleStkDayList" resultType="java.util.Map">
		SELECT 
			BA_PROV_ORG_CODE AS baProvOrgCode,
			MC04_XY_ORG_CODE AS mc04XyOrgCode,
			SUM(MD03_CGT_10TH_COM_COE_SALE_QTY_Y_A) AS qtySaleYear,
			SUM(MC04_MG_CGT_10TH_EOD_STK_QTY) AS qtyStkEod
		FROM 
			MC04_IND_XY_ORG_CGT_PURCH_SALE_STK_DAY
		WHERE 
			AC_CGT_CARTON_CODE = #{acCgtCartonCode}
			AND MC04_CGT_FORFEITURE_FLAG = '1'
			AND MC04_PURCHASE_SALE_STK_DATE = #{mc04PurchaseSaleStkDate}
		GROUP BY 
			BA_PROV_ORG_CODE,
			MC04_XY_ORG_CODE
	</select>

	<select id="getDataDate" resultType="String">
		SELECT
			DATA_DATE
		FROM
			ISM_GATHER_LOG
		WHERE
			DATA_TABLE = 'mc04_ind_xy_org_cgt_purch_sale_stk_day'
	</select>
	<select id="getMarketPrice" resultType="java.util.Map">
		select
			r.ba_city_org_code,
			pc.mc04_cgt_type_code,
			ROUND(AVG(pc.mc04_info_coll_mark_stat_avg_circ_pric_curr), 2) as price
		from
			ind_mkt.mc04_imm_info_coll_result r
		inner join ind_mkt.mc04_imm_info_coll_result_cust_line pc 
		on
			r.MC04_INFO_COLL_RESULT_ID = pc.MC04_INFO_COLL_RESULT_ID
		where
			r.MC04_INFO_COLL_FORM_ID = 'TEL'
			and pc.MC04_INFO_COLL_FORM_ID = 'TEL'
			and r.MC04_INFO_COLL_RESULT_DATE between #{startTime} and #{endTime}
			and pc.MC04_CGT_TYPE_CODE = #{acCgtCartonCode}
		group by
			r.BA_CITY_ORG_CODE,
			pc.MC04_CGT_TYPE_CODE;
	</select>
	<select id="getPurchSaleStk" resultType="java.util.Map">
		SELECT
			mc04_xy_org_code ba_com_org_code,
			ac_cgt_carton_code ac_cgt_carton_code,
			SUM(CASE
				WHEN mc04_purchase_sale_stk_month = #{zaOccurrenceMonth}
				THEN md03_cgt_10th_com_coe_sale_qty_m_a 
				ELSE 0 
			END) AS mc04_cgt_plan_m_sale_actual_qty,
			SUM(CASE
				WHEN mc04_purchase_sale_stk_month = #{zaOccurrenceMonth}
				THEN mc04_mg_cgt_10th_eom_stk_qty 
				ELSE 0 
			END) AS mc04_cgt_com_invt_qty
		FROM
			mc04_ind_xy_org_cgt_purch_sale_stk_month
		WHERE
			mc04_xy_org_code IN
			<foreach collection="baComOrgCodeList" item="comId" open="(" separator="," close=")">
				#{comId}
			</foreach>
			AND mc04_cgt_forfeiture_flag = '1'
			AND mc04_purchase_sale_stk_month = #{zaOccurrenceMonth}
		    AND ac_cgt_carton_code = #{acCgtCartonCode}
		GROUP BY
			mc04_xy_org_code
	</select>
    <select id="getPurchSaleStkList" resultType="java.util.Map">
        SELECT
        mc04_xy_org_code ba_com_org_code,
        ac_cgt_carton_code ac_cgt_carton_code,
        SUM(CASE
        WHEN mc04_purchase_sale_stk_month = #{zaOccurrenceMonth}
        THEN md03_cgt_10th_com_coe_sale_qty_m_a
        ELSE 0
        END) AS mc04_cgt_plan_m_sale_actual_qty,
        SUM(CASE
        WHEN mc04_purchase_sale_stk_month = #{zaOccurrenceMonth}
        THEN mc04_mg_cgt_10th_eom_stk_qty
        ELSE 0
        END) AS mc04_cgt_com_invt_qty
        FROM
        mc04_ind_xy_org_cgt_purch_sale_stk_month
        WHERE
        mc04_xy_org_code IN
        <foreach collection="baComOrgCodeList" item="comId" open="(" separator="," close=")">
            #{comId}
        </foreach>
        AND mc04_cgt_forfeiture_flag = '1'
        AND mc04_purchase_sale_stk_month = #{zaOccurrenceMonth}
        AND ac_cgt_carton_code IN
        <foreach collection="acCgtCartonCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        GROUP BY
        mc04_xy_org_code
    </select>
	<select id="getPlanMAllotActualtyExe" resultType="java.util.Map">
		SELECT 
			plan.ba_com_org_code,
			plan.ac_cgt_carton_code,
			plan.ac_two_level_cig_code,
			coalesce(plan.qty_dist, 0) - coalesce(cont.qty_cont, 0) qty_exe
		FROM
		(
			SELECT 
				ba_com_org_code,
				ac_cgt_carton_code,
				ac_two_level_cig_code,
				sum(ma02_cgt_pl_adjusted_qty) qty_dist 
			FROM 
				mc04_islmc_cgt_day_plan
			WHERE 
				ma02_toba_prod_trade_type_code = '0' 
				AND ma02_plan_month = #{zaOccurrenceMonth}
				AND ba_com_org_code IN
				<foreach collection="baComOrgCodeList" item="comId" open="(" separator="," close=")">
					#{comId}
				</foreach>
			GROUP BY 
				ba_com_org_code,
				ac_cgt_carton_code,
				ac_two_level_cig_code
		) plan
		LEFT JOIN
		(
			SELECT 
				a.ba_com_org_code,
				b.ac_cgt_carton_code,
				b.ac_two_level_cig_code,
				sum(b.md02_cgt_trade_cont_qty) qty_cont
			FROM 
				mc04_islm_cont_order a,
				mc04_islm_cont_order_item b
			WHERE 
				a.mc04_cont_order_id = b.mc04_cont_order_id 
				AND a.ma02_toba_prod_trade_type_code = '0' 
				AND a.ma02_plan_month = #{zaOccurrenceMonth}
				AND a.mc04_cgt_trade_cont_status IN ('80', '90') 
				AND a.ba_com_org_code IN
				<foreach collection="baComOrgCodeList" item="comId" open="(" separator="," close=")">
					#{comId}
				</foreach>
			GROUP BY 
				a.ba_com_org_code,
				b.ac_cgt_carton_code,
				b.ac_two_level_cig_code
		) cont
		ON 
			plan.ba_com_org_code = cont.ba_com_org_code 
			AND plan.ac_cgt_carton_code = cont.ac_cgt_carton_code 
			AND plan.ac_two_level_cig_code = cont.ac_two_level_cig_code
	</select>
</mapper>
