/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 前置库选公司
 * @Author: renyonghui
 * @Since: 2025-05-20
 * @Email: <EMAIL>
 * @Create: 2025-05-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_forward_whse_com")
@DataObject(name = "前置库选公司", desc = "前置库选公司")
public class Mc04IslmcForwardWhseComDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 商业公司编码
     */
    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;

    /**
     * 集并库编码
     */
    @Field(value = "集并库编码", name = "集并库编码")
    private String md02CgtOutStorehouseCode;

    /**
     * 前置库编码
     */
    @Field(value = "前置库编码", name = "前置库编码")
    private String md02CgtInStorehouseCode;

}
