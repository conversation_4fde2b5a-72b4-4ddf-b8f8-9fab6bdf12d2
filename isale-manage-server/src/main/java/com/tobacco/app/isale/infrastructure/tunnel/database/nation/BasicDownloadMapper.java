package com.tobacco.app.isale.infrastructure.tunnel.database.nation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 行业营销子系统基础数据查询 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
public interface BasicDownloadMapper extends BaseMapper<Map> {
    /**
     * 删除交易目录中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationProductControl(List<Map<String, Object>> datas);

    /**
     * 删新增交易目录中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationProductControl(List<Map<String, Object>> datas);

    /**
     * 删除交易目录含价格中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationProductControlPrice(List<Map<String, Object>> datas);

    /**
     * 新增交易目录含价格中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationProductControlPrice(List<Map<String, Object>> datas);

    /**
     * 删除产品目录中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationProduct(List<Map<String, Object>> datas);

    /**
     * 新增产品目录中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationProduct(List<Map<String, Object>> datas);

    /**
     * 删除会员信息中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationMember(List<Map<String, Object>> datas);

    /**
     * 新增会员信息中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationMember(List<Map<String, Object>> datas);

    /**
     * 删除会员代表中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationDeployee(List<Map<String, Object>> datas);

    /**
     * 新增会员代表中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationDeployee(List<Map<String, Object>> datas);

    /**
     * 删除会员仓库中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationWhse(List<Map<String, Object>> datas);

    /**
     * 新增会员仓库中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationWhse(List<Map<String, Object>> datas);

    /**
     * 下载会员仓库后处理到一体化合同到货仓库表(is_use=0)
     *
     * @param datas    数据
     * @param icomCode 工业编码
     */
    int insertNationWhseFromNation(List<Map<String, Object>> datas, String icomCode);

    /**
     * 删除会员税号中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationTax(List<Map<String, Object>> datas);

    /**
     * 新增会员税号中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationTax(List<Map<String, Object>> datas);

    /**
     * 删除会员银行账号中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationBank(List<Map<String, Object>> datas);

    /**
     * 新增会员银行账号中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationBank(List<Map<String, Object>> datas);

    /**
     * 删除协议核定量中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int deleteNationRatifyQty(List<String> datas);

    /**
     * 批量插入协议核定量中间表
     *
     * @param datas 数据
     * @return 返回数据
     */
    int insertNationRatifyQty(List<Map<String, Object>> datas);

}
