/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 配货收货地区中间表
 *
 * @Author: jinfuli
 * @Since: 2025-08-05
 * @Email: <EMAIL>
 * @Create: 2025-08-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("nation_dist_receive_region")
public class NationDistReceiveRegionDO  {

    private static final long serialVersionUID = 1L;
                /**
             * 标识编码
             */

    @Field(value = "标识编码", name = "标识编码")
    private String pk;
                                /**
             * 需方会员编码
             */

    @Field(value = "需方会员编码", name = "需方会员编码")
    private String ma02CgtTradeReqMembCode;
                                /**
             * 需方会员名称
             */

    @Field(value = "需方会员名称", name = "需方会员名称")
    private String md02CgtTradeReqMembName;
                                /**
             * 配货收货地区编码
             */

    @Field(value = "配货收货地区编码", name = "配货收货地区编码")
    private String md02DistReceiveregionCode;
                                /**
             * 配货收货地区名称
             */

    @Field(value = "配货收货地区名称", name = "配货收货地区名称")
    private String md02DistReceiveregionName;
                                /**
             * 是否封存（Y：封存，N：未封存）
             */

    @Field(value = "是否封存（Y：封存，N：未封存）", name = "是否封存（Y：封存，N：未封存）")
    private String isseal;
                                /**
             * 备注
             */

    @Field(value = "备注", name = "备注")
    private String remark;
                

}
