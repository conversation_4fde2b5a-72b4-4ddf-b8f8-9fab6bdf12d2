/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;


/**
 * @description : 服务类
 *
 * <AUTHOR> wang<PERSON><PERSON>01
 * @since : 2025-05-20
 * @email : <EMAIL>
 * @create_time : 2025-05-20
 */
public interface Mc04IslmContOrderService extends IService<Mc04IslmContOrderDO>{

}