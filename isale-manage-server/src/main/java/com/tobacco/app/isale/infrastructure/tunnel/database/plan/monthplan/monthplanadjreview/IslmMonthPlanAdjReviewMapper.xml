<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanadjreview.IslmMonthPlanAdjReviewMapper">
    <select id="queryCgtStatus" resultType="java.util.Map">
		select
			ac_cgt_carton_code,
			sum(case when mc04_month_sale_plan_status = '30' then 1 else 0 end) todo_count,
			sum(case when mc04_month_sale_plan_status>'30' then 1 else 0 end) done_count
		from
			mc04_islm_month_sale_plan_adj
		where
			ma02_toba_prod_trade_type_code = '0'
			and mc04_is_latest_adjust = '1'
			and ma02_plan_month = #{ma02PlanMonth}
			and mc04_month_sale_plan_status >= '30'
			and icom_code = #{icomCode}
		group by
			ac_cgt_carton_code
    </select>
	<select id="getPlanSubjectType" resultType="String">
		select
			max(isp.mc04_plan_subject_type) as mc04_plan_subject_type
		from
			mc04_islm_sale_plan isp
		where
			isp.mc04_is_lastest_version = '1'
			and isp.za_occurrence_year = #{zaOccurrenceYear}
			and isp.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
			and isp.mc04_sale_plan_status = '90'
	</select>



	<select id="getSaleStkDayList" resultType="java.util.Map">
		select
			ba_prov_org_code,
			mc04_xy_org_code,
			sum(md03_cgt_10th_com_coe_sale_qty_y_a) qty_sale_year,
			sum(mc04_mg_cgt_10th_eod_stk_qty) qty_stk_eod
		from
			mc04_ind_xy_org_cgt_purch_sale_stk_day
		where
			ac_cgt_carton_code = #{acCgtCartonCode}
			and mc04_cgt_forfeiture_flag = '1'
			and mc04_purchase_sale_stk_date = #{mc04PurchaseSaleStkDate}
		group by
			ba_prov_org_code,
			mc04_xy_org_code
	</select>

	<select id="getDataDate" resultType="String">
		select
			data_date
		from
			ism_gather_log
		where
			data_table = 'mc04_ind_xy_org_cgt_purch_sale_stk_day'
	</select>

	<select id="getMonthPlanAdjBrandList" resultType="com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04MonthPlanAdjReviewQueryResult">
		SELECT
			t1.n1_normal_mc04_month_sale_plan_adj_item_id,
			t1.n1_transfer_mc04_month_sale_plan_adj_item_id,
			t1.n1_zero_mc04_month_sale_plan_adj_item_id,
			t1.ba_prov_org_code,
			t1.ba_com_org_code,
			t4.md02_cgt_trade_cont_qty qty_xy_exe,
			t2.qty_plan_this_month,
			t2.qty_plan_this_month - t5.ma02_cgt_pl_adjusted_qty + t6.md02_cgt_trade_cont_qty qty_plan_remain_this_month,
			t7.ma02_cgt_pl_adjusted_qty qty_xy_exe_last,
			t8.md02_cgt_trade_cont_qty qty_cont_cancel_last,
			t1.qty_plan_carry_over,
			t3.ma02_cgt_pl_adjusted_qty qty_plan_quarter,
			t2.qty_plan_last_month,
			t1.n1_normal_mc04_cgt_allot_plan_report_adj_qty,
			t1.n1_transfer_mc04_cgt_allot_plan_report_adj_qty,
			t1.n1_zero_mc04_cgt_allot_plan_report_adj_qty,
			t1.n1_normal_mc04_cgt_allot_plan_region_confirm_adj_qty,
			t1.n1_transfer_mc04_cgt_allot_plan_region_confirm_adj_qty,
			t1.n1_zero_mc04_cgt_allot_plan_region_confirm_adj_qty,
			t1.n1_normal_mc04_cgt_allot_plan_bd_confirm_adj_qty,
			t1.n1_transfer_mc04_cgt_allot_plan_bd_confirm_adj_qty,
			t1.n1_zero_mc04_cgt_allot_plan_bd_confirm_adj_qty,
		    t1.mc04_month_sale_plan_type
		FROM
			(
				SELECT
					ba_prov_org_code,
					ba_com_org_code,
		            mc04_month_sale_plan_type,
					CASE WHEN mc04_month_sale_plan_type = '10' THEN mc04_month_sale_plan_adj_item_id END n1_normal_mc04_month_sale_plan_adj_item_id,
					CASE WHEN mc04_month_sale_plan_type = '20' THEN mc04_month_sale_plan_adj_item_id END n1_transfer_mc04_month_sale_plan_adj_item_id,
					CASE WHEN mc04_month_sale_plan_type = '30' THEN mc04_month_sale_plan_adj_item_id END n1_zero_mc04_month_sale_plan_adj_item_id,
					SUM(CASE WHEN mc04_month_sale_plan_type = '10' THEN mc04_cgt_allot_plan_report_adj_qty ELSE NULL END) n1_normal_mc04_cgt_allot_plan_report_adj_qty,
					SUM(CASE WHEN mc04_month_sale_plan_type = '20' THEN mc04_cgt_allot_plan_report_adj_qty ELSE NULL END) n1_transfer_mc04_cgt_allot_plan_report_adj_qty,
					SUM(CASE WHEN mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_report_adj_qty ELSE NULL END) n1_zero_mc04_cgt_allot_plan_report_adj_qty,
					SUM(CASE WHEN mc04_month_sale_plan_type = '10' THEN mc04_cgt_allot_plan_region_confirm_adj_qty ELSE NULL END) n1_normal_mc04_cgt_allot_plan_region_confirm_adj_qty, -- 区域确认量
		            SUM(CASE WHEN mc04_month_sale_plan_type = '20' THEN mc04_cgt_allot_plan_region_confirm_adj_qty ELSE NULL END) n1_transfer_mc04_cgt_allot_plan_region_confirm_adj_qty, -- 区域确认量
					SUM(CASE WHEN mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_region_confirm_adj_qty ELSE NULL END) n1_zero_mc04_cgt_allot_plan_region_confirm_adj_qty -- 区域确认量
					SUM(CASE WHEN mc04_month_sale_plan_type = '10' THEN mc04_cgt_allot_plan_bd_confirm_adj_qty ELSE NULL END) n1_normal_mc04_cgt_allot_plan_bd_confirm_adj_qty, -- 品牌部审核量
					SUM(CASE WHEN mc04_month_sale_plan_type = '20' THEN mc04_cgt_allot_plan_bd_confirm_adj_qty ELSE NULL END) n1_transfer_mc04_cgt_allot_plan_bd_confirm_adj_qty, -- 品牌部审核量
					SUM(CASE WHEN mc04_month_sale_plan_type = '30' THEN mc04_cgt_allot_plan_bd_confirm_adj_qty ELSE NULL END) n1_zero_mc04_cgt_allot_plan_bd_confirm_adj_qty, -- 品牌部审核量
				FROM
					mc04_islm_month_sale_plan_adj
				WHERE
					ma02_toba_prod_trade_type_code = '0'
					AND mc04_is_latest_adjust = '1'
					AND ma02_plan_month = #{ma02PlanMonth}
					AND ac_cgt_carton_code = #{acCgtCartonCode}
					AND icom_code = #{icomCode}
					<if test="baComOrgCodes != null and baComOrgCodes.size() > 0">
						AND ba_com_org_code in
						<foreach item="item" index="index" collection="baComOrgCodes"
								 open="(" close=")" separator=",">
							#{item}
						</foreach>
				    </if>
					<choose>
						<when test="acCgtCartonCodeStatus != null and acCgtCartonCodeStatus == 0">
							AND MC04_MONTH_SALE_PLAN_STATUS = '30'
						</when>
						<when test="acCgtCartonCodeStatus != null and acCgtCartonCodeStatus == 1">
							AND MC04_MONTH_SALE_PLAN_STATUS > '30'
						</when>
						<otherwise>
							AND MC04_MONTH_SALE_PLAN_STATUS >= '30'
						</otherwise>
					</choose>
				GROUP BY
					ba_prov_org_code,
					ba_com_org_code
			) t1
		LEFT JOIN
			(
				SELECT
					ba_prov_org_code,
					ba_com_org_code,
					SUM(CASE WHEN ma02_plan_month = #{ma02PlanMonth} THEN ma02_cgt_pl_adjusted_qty ELSE 0 END) qty_plan_this_month, -- 本月计划量
					SUM(CASE WHEN ma02_plan_month = #{monthLast} THEN ma02_cgt_pl_adjusted_qty ELSE 0 END) qty_plan_last_month -- 上月计划量
				FROM
					mc04_islm_month_sale_plan
				WHERE
					ma02_toba_prod_trade_type_code = '0'
					AND mc04_cgt_sale_plan_version = '1'
					AND ma02_plan_month IN (#{monthLast}, #{ma02PlanMonth})
					AND ac_cgt_carton_code = #{acCgtCartonCode}
					AND icom_code = #{icomCode}
				GROUP BY
					ba_prov_org_code,
					ba_com_org_code
			) t2 ON
			t1.ba_com_org_code = t2.ba_com_org_code
		LEFT JOIN
			(
				SELECT
					isp.mc04_org_type_code,
					SUM(ispi.ma02_cgt_pl_adjusted_qty) ma02_cgt_pl_adjusted_qty
				FROM
					mc04_islm_sale_plan isp,
					mc04_islm_sale_plan_item ispi
				WHERE
					isp.mc04_sale_plan_id = ispi.mc04_sale_plan_id
					AND isp.mc04_is_lastest_version = '1'
					AND isp.za_occurrence_year = #{zaOccurrenceYear}
					AND isp.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
					AND isp.mc04_sale_plan_status = '90'
					AND isp.mc04_cgt_sale_fo_period_type = 'T03'
					AND isp.mc04_cgt_sale_fo_period_code = #{mc04CgtSaleFoPeriodCode}
					AND isp.mc04_plan_subject_type = #{mc04PlanSubjectType}
					AND isp.mc04_org_type_kind = '02'
					AND ispi.ac_cgt_carton_code = #{acCgtCartonCode}
				GROUP BY
					isp.mc04_org_type_code
			) t3 ON
			t1.ba_com_org_code = t3.mc04_org_type_code
		LEFT JOIN 
			(
				SELECT
					ico.ba_com_org_code,
					SUM(icoi.md02_cgt_trade_cont_qty) AS md02_cgt_trade_cont_qty
				FROM
					mc04_islm_cont_order ico,
					mc04_islm_cont_order_item icoi
				WHERE
					ico.mc04_cont_order_id = icoi.mc04_cont_order_id
					AND ico.ma02_toba_prod_trade_type_code = '0'
					AND icoi.ac_cgt_carton_code = #{acCgtCartonCode}
					AND ico.mc04_cgt_xy_period_code = #{mc04CgtXyPeriodCode}
					AND ico.mc04_cgt_trade_cont_status = '10'
				GROUP BY
					ico.ba_com_org_code
			) t4 ON
			t1.ba_com_org_code = t4.ba_com_org_code
		LEFT JOIN
			(
				SELECT
					icdp.ba_com_org_code,
					SUM(icdp.ma02_cgt_pl_adjusted_qty) AS ma02_cgt_pl_adjusted_qty
				FROM
					mc04_islmc_cgt_day_plan icdp
				WHERE
					icdp.ma02_toba_prod_trade_type_code = '0'
					AND icdp.ac_cgt_carton_code = #{acCgtCartonCode}
					AND icdp.ma02_plan_month = #{ma02PlanMonth}
				GROUP BY
					icdp.ba_com_org_code
			) t5 ON
			t1.ba_com_org_code = t5.ba_com_org_code
		LEFT JOIN
			(
				SELECT
					ico.ba_com_org_code,
					SUM(icoi.md02_cgt_trade_cont_qty) AS md02_cgt_trade_cont_qty
				FROM
					mc04_islm_cont_order ico,
					mc04_islm_cont_order_item icoi
				WHERE
					ico.mc04_cont_order_id = icoi.mc04_cont_order_id
					AND ico.ma02_toba_prod_trade_type_code = '0'
					AND icoi.ac_cgt_carton_code = #{acCgtCartonCode}
					AND ico.ma02_plan_month = #{ma02PlanMonth}
					AND ico.mc04_cgt_trade_cont_status IN ('80', '90')
				GROUP BY
					ico.ba_com_org_code
			) t6 ON
			t1.ba_com_org_code = t6.ba_com_org_code
		LEFT JOIN
			(
				SELECT
					icdp.ba_com_org_code,
					SUM(icdp.ma02_cgt_pl_adjusted_qty) AS ma02_cgt_pl_adjusted_qty
				FROM
					mc04_islmc_cgt_day_plan icdp
				WHERE
					icdp.ma02_toba_prod_trade_type_code = '0'
					AND icdp.ac_cgt_carton_code = #{acCgtCartonCode}
					AND icdp.mc04_cgt_xy_period_code = #{mc04CgtXyPeriodCode}
				GROUP BY
					icdp.ba_com_org_code
			) t7 ON
			t1.ba_com_org_code = t7.ba_com_org_code
		LEFT JOIN
			(
				SELECT
					ico.ba_com_org_code,
					SUM(icoi.md02_cgt_trade_cont_qty) AS md02_cgt_trade_cont_qty
				FROM
					mc04_islm_cont_order ico,
					mc04_islm_cont_order_item icoi
				WHERE
					ico.mc04_cont_order_id = icoi.mc04_cont_order_id
					AND ico.ma02_toba_prod_trade_type_code = '0'
					AND icoi.ac_cgt_carton_code = #{acCgtCartonCode}
					AND ico.mc04_cgt_xy_period_code = #{mc04CgtXyPeriodCode}
					AND ico.mc04_cgt_trade_cont_status IN ('80', '90')
				GROUP BY
					ico.ba_com_org_code
			) t8 ON
			t1.ba_com_org_code = t8.ba_com_org_code
	</select>
	<select id="getMarketPrice" resultType="java.util.Map">
		select
			r.ba_city_org_code,
			pc.mc04_cgt_type_code,
			ROUND(AVG(pc.mc04_info_coll_mark_stat_avg_circ_pric_curr), 2) as price
		from
			ind_mkt.mc04_imm_info_coll_result r
		inner join ind_mkt.mc04_imm_info_coll_result_cust_line pc 
		on
			r.MC04_INFO_COLL_RESULT_ID = pc.MC04_INFO_COLL_RESULT_ID
		where
			r.MC04_INFO_COLL_FORM_ID = 'TEL'
			and pc.MC04_INFO_COLL_FORM_ID = 'TEL'
			and r.MC04_INFO_COLL_RESULT_DATE between #{startTime} and #{endTime}
			and pc.MC04_CGT_TYPE_CODE = #{acCgtCartonCode}
		group by
			r.BA_CITY_ORG_CODE,
			pc.MC04_CGT_TYPE_CODE;
	</select>
</mapper>
