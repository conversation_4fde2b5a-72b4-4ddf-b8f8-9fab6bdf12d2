/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 配货参数季节系数申请表
 * @Author: liuwancheng
 * @Since: 2025-05-28
 * @Email: <EMAIL>
 * @Create: 2025-05-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_dist_parm_apply_season")
@DataObject(name = "配货参数季节系数申请表", desc = "配货参数季节系数申请表")
public class Mc04IslmcDistParmApplySeasonDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
    /**
     * 配货参数季节系数记录编号
     */

    @TableId(value = "mc04_cgt_dist_parm_apply_season_id", type = IdType.NONE)
    @Field(value = "配货参数季节系数记录编号", name = "配货参数季节系数记录编号")
    private String mc04CgtDistParmApplySeasonId;
    /**
     * 配货参数申请编号
     */

    @Field(value = "配货参数申请编号", name = "配货参数申请编号")
    private String mc04CgtDistParmApplyId;
    /**
     * 工商交易业务结束日期
     */

    @Field(value = "工商交易业务开始日期", name = "工商交易业务开始日期")
    private String md02IcTradeBusiStartDate;
    /**
     * 工商交易业务结束日期
     */

    @Field(value = "工商交易业务结束日期", name = "工商交易业务结束日期")
    private String md02IcTradeBusiEndDate;
    /**
     * 季节系数
     */

    @Field(value = "季节系数", name = "季节系数")
    private BigDecimal md02CgtDistSeasonFactor;
    /**
     * 备注
     */

    @Field(value = "备注", name = "备注")
    private String zaRemark;


}
