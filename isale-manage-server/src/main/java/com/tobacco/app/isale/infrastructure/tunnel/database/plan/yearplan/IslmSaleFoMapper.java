/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSaleFoDTO;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSumIndexDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: jinfuli
 * @Date: 2025/6/14
 * @Description:
 */
@Mapper
public interface IslmSaleFoMapper {
    /**
     * 获取年度计划列表
     **/
    Page<Mc04IslmSaleFoDTO> getSaleFolist(
            Page page,
            String startYear,
            String endYear,
            String type,
            String status,
            String ma02TobaProdTradeTypeCode,
            String icomCode
    );


    /**
     * 年度预测销量单据创建
     **/
    boolean	createMc04IslmSalePlan(
            String planId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );


    /**
     * 品牌计划单据创建
     **/
    boolean	createMc04IslmBrandPlan(
            String planId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );

    /**
     * 年度预测单据创建
     **/
    boolean	createMc04IslmSaleFo(
            String mc04CgtSaleFoId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );

    /**
     * 月计划量
     **/
    boolean	createMc04IslmYcSalePlan(
            String mc04SalePlanId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate
    );

    /**
     * 获取前几年的销售数据
     * @param year
     * @param icomCode
     * @return
     */
    @DS("dws")
    List<Map<String,Object>> getLastThreeYearsData(String year,String icomCode);

    /**
     * 获取下一年的销售预测
     * @param year
     * @param subjectType
     * @return
     */
    List<Map<String, Object>> getNextYearData(String year, String subjectType);

    /**
     * 获取指标表的各种数据
     * @param mz12InfoSystemResourceCode
     * @return
     */
    List<Mc04IslmSumIndexDTO> getTotalSumIndex(String mz12InfoSystemResourceCode);
}
