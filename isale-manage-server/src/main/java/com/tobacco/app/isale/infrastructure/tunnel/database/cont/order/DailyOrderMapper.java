/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.cont.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.order.preshipment.DailyOrderDomainREQ;
import com.tobacco.app.isale.domain.model.order.preshipment.MonthData;
import com.tobacco.app.isale.domain.model.order.preshipment.PreshipmentPlan;
import com.tobacco.app.isale.domain.model.order.preshipment.XyOrgSaleDay;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/8/27
 * @Description: 日要货上报数据Mapper
 */
@Mapper
public interface DailyOrderMapper {
    /**
     * 查询协议单位日销售数据
     * @param dailyOrderREQ 查询参数
     * @return List<XyOrgSaleDay>
     */
    @DS("dws")
    List<XyOrgSaleDay> queryXyOrgSaleData(DailyOrderDomainREQ dailyOrderREQ);

    /**
     * 查询备货移库计划量剩余量
     * @param req 查询参数
     * @return 备货移库计划量剩余量
     */
    List<PreshipmentPlan> queryPreshipmentPlanData(DailyOrderDomainREQ req);

    /**
     * 查询日计划数据（执行量）
     *
     */
    List<MonthData> getDayPlan(DailyOrderDomainREQ req);
}
