/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic;

import com.tobacco.app.isale.domain.model.agreement.basic.IslmcXy;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> jinfuli
 * @Email : @inspur.com
 * @Create : 2025/05/07 14:09
 * @Description : TODO
 */
@Mapper
public interface AgreementDownMapper {
    List<IslmcXy> queryNationAgreement(Map map);
}
