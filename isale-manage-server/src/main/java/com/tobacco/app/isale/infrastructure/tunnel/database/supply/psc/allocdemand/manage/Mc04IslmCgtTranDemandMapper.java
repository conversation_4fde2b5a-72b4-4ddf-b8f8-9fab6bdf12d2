package com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.manage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.dto.supply.psc.allocdemand.manage.AllocdemandMangeDTO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 */
@Mapper
public interface Mc04IslmCgtTranDemandMapper extends BaseMapper<Mc04IslmCgtTranDemandDO> {
    /**
     * 获取调拨计划管理
     **/
    Page<AllocdemandMangeDTO> queryAllocdemandMangePage(Page page, @Param("zaOccurrenceMonth") String zaOccurrenceMonth,  @Param("mc03CgtProdPlType") String mc03CgtProdPlType);
}