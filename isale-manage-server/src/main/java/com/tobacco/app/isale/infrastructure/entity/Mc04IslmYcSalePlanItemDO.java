/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 元春销售调拨计划规格明细 视图层对象/值对象
 * </p>
 *
 * @Author: loongxi
 * @Since: 2025-08-06
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islm_yc_sale_plan_item")
public class Mc04IslmYcSalePlanItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 销售调拨计划规格明细编号
     */
                @TableId(value = "mc04_sale_plan_item_id", type = IdType.NONE)
    @Field(value = "销售调拨计划规格明细编号", name = "销售调拨计划规格明细编号")
    private String mc04SalePlanItemId;

    /**
     * 销售调拨计划编号
     */
    @Field(value = "销售调拨计划编号", name = "销售调拨计划编号")
    private String mc04SalePlanId;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;

    /**
     * 卷烟价位段代码
     */
    @Field(value = "卷烟价位段代码", name = "卷烟价位段代码")
    private String acCgtPriceSegmentCode;

    /**
     * 卷烟价位段名称
     */
    @Field(value = "卷烟价位段名称", name = "卷烟价位段名称")
    private String acCgtPriceSegmentName;

    /**
     * 卷烟含税调拨价格
     */
    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟统一批发价
     */
    @Field(value = "卷烟统一批发价", name = "卷烟统一批发价")
    private BigDecimal acCgtTradePrice;

    /**
     * 卷烟中支烟标识
     */
    @Field(value = "卷烟中支烟标识", name = "卷烟中支烟标识")
    private String acCgtMediumBranceFlag;

    /**
     * 卷烟细支烟标识
     */
    @Field(value = "卷烟细支烟标识", name = "卷烟细支烟标识")
    private String acCgtTinyFlag;

    /**
     * 卷烟焦油量
     */
    @Field(value = "卷烟焦油量", name = "卷烟焦油量")
    private BigDecimal acCgtTarVal;

    /**
     * 卷烟商业期初库存量（万支）
     */
    @Field(value = "卷烟商业期初库存量（万支）", name = "卷烟商业期初库存量（万支）")
    @TableField("md03_cgt_10th_com_init_stk_qty")
    private BigDecimal md03Cgt10thComInitStkQty;

    /**
     * 卷烟销售计划原计划量
     */
    @Field(value = "卷烟销售计划原计划量", name = "卷烟销售计划原计划量")
    private BigDecimal mc04CgtSalePlanOriginQty;

    /**
     * 卷烟调拨计划原计划量
     */
    @Field(value = "卷烟调拨计划原计划量", name = "卷烟调拨计划原计划量")
    private BigDecimal ma02CgtPlResolOrigplanQty;

    /**
     * 卷烟商业期末库存量（万支）
     */
    @Field(value = "卷烟商业期末库存量（万支）", name = "卷烟商业期末库存量（万支）")
    @TableField("md03_cgt_10th_com_end_stk_qty")
    private BigDecimal md03Cgt10thComEndStkQty;

    /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

}
