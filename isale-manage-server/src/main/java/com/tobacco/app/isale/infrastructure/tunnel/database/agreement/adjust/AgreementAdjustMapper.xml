<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.agreement.adjust.AgreementAdjustMapper">

    <select id="queryLastApplyList"
            resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">
        select
        a.mc04_xy_adjust_id as mc04XyAdjustId,
        b.mc04_xy_adjust_item_id as mc04XyAdjustItemId,
        a.mc04_xy_adjust_status as mc04XyAdjustStatus,
        b.ac_cgt_carton_code as acCgtCartonCode,
        b.ac_two_level_cig_code as acTwoLevelCigCode,
        a.ba_com_org_code as baComOrgCode,
        a.mc04_xy_adjust_status as mc04XyAdjustStatus,
        coalesce(sum(b.md02_cgt_xy_adjust_qty), 0) as md02CgtXyAdjustQty,
        abs(coalesce(sum(b.md02_cgt_xy_adjust_qty), 0)) as md02CgtXyAdjustQtyAbs,
        coalesce(do.reqQty,0) as reqQty,
        coalesce(b.mc04_xy_adjust_report_qty, 0) AS mc04XyAdjustReportQty,
        coalesce(b.mc04_xy_adjust_region_confirm_qty,0) AS mc04XyAdjustRegionConfirmQty,
        coalesce(b.mc04_xy_adjust_director_audit_qty , 0) AS mc04XyAdjustDirectorAuditQty,
        coalesce(b.mc04_xy_adjust_brand_audit_qty,0) AS mc04XyAdjustBrandAuditQty,
        coalesce(b.mc04_xy_adjust_busi_audit_qty,0) AS mc04XyAdjustBusiAuditQty,
        coalesce(b.mc04_xy_adjust_leader_audit_qty,0) as mc04XyAdjustLeaderAuditQty,
        coalesce(b.mc04_xy_adjust_plan_confirm_qty, 0) AS mc04XyAdjustPlanConfirmQty,
        a.md02_cgt_xy_no as md02CgtXyNo,
        b.create_name as createName,
        b.za_remark as zaRemark
        from mc04_islmc_xy_adjust_apply a
        inner join mc04_islmc_xy_adjust_apply_item b on a.mc04_xy_adjust_id = b.mc04_xy_adjust_id
        left join (
        select
        xy.md02_cgt_xy_no,
        xyi.md02_cgt_xy_original_qty,
        xyi.ac_cgt_carton_code,
        xy.ba_com_org_code
        from mc04_islmc_xy xy
        left join mc04_islmc_xy_item xyi on xy.mc04_cgt_xy_id = xyi.mc04_cgt_xy_id
        where xy.mc04_cgt_xy_period_code = #{dateCode}
        <if test="comIds != null and comIds.size() > 0">
            and xy.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and xyi.ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and xy.ma02_toba_prod_trade_type_code = #{cgtType}
        ) as x
        on a.md02_cgt_xy_no = x.md02_cgt_xy_no
        and b.ac_cgt_carton_code = x.ac_cgt_carton_code
        and x.ba_com_org_code = a.ba_com_org_code
        left join (
        select
        ac_cgt_carton_code,
        ac_two_level_cig_code,
        ba_com_org_code,
        sum(md02_cgt_dist_confirm_qty) reqQty
        from mc04_islmc_cgt_dist_order
        where mc04_cgt_xy_period_code = #{dateCode} -- 周期
        and ma02_toba_prod_trade_type_code = #{cgtType} -- 卷烟

        <if test="comIds != null and comIds.size() > 0">
            and ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and mc04_cgt_dist_order_status != '90' -- 非作废
        group by ac_cgt_carton_code, ac_two_level_cig_code, ba_com_org_code
        ) as do
        on do.ac_cgt_carton_code = b.ac_cgt_carton_code
        and do.ac_two_level_cig_code = b.ac_two_level_cig_code
        and do.ba_com_org_code = a.ba_com_org_code
        where a.mc04_is_latest_adjust = '1'
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and b.ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null and !''.equals(status)">
            and a.mc04_xy_adjust_status = #{status}
        </if>
        and a.mc04_cgt_xy_period_code = #{dateCode}
        and a.ma02_toba_prod_trade_type_code = #{cgtType}
        group by b.ac_cgt_carton_code,a.ba_com_org_code,b.ac_two_level_cig_code
        order by a.ba_com_org_code
    </select>
    <!--    <select id="queryAdjustList" resultType="com.tobacco.app.isalecenter.domain.model.basic.IslmcXyAdjust">-->
    <!--        select ixai.ac_cgt_carton_code as acCgtCartonCode,-->
    <!--               sum(COALESCE(ixai.md02_cgt_xy_adjust_qty,0)) as md02CgtXyAdjustQty-->
    <!--        from mc04_islmc_xy_adjust ixa inner join mc04_islmc_xy_adjust_item ixai on ixa.mc04_xy_adjust_id = ixai.mc04_xy_adjust_id-->
    <!--        where ixa.mc04_cgt_xy_period_code = #{dateCode} &#45;&#45; 周期-->
    <!--          and ixa.ma02_toba_prod_trade_type_code = #{cgtType} &#45;&#45; 卷烟-->
    <!--          and ixa.ba_com_org_code = #{comId}-->
    <!--        group by ixai.ac_cgt_carton_code-->
    <!--    </select>-->
    <select id="queryInitApply"
            resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">
        select ici.ac_cgt_carton_code as acCgtCartonCode,
        ici.ac_two_level_cig_code as acTwoLevelCigCode,
        COALESCE(b.qty, 0) as reqQty, --- reqQty, -- 要货量
        COALESCE(c.qty, 0) as md02CgtXyAdjustedQty,-- adjusted_qty,已调整量
        COALESCE(d.qty, 0) adjust_qty
        '00' as mc04XyAdjustStatus -- 生成一条新的变更申请单,状态暂定00吧
        from mc04_islmc_com_item ici
        left join (select ac_cgt_carton_code, sum(md02_cgt_dist_confirm_qty) qty
        from mc04_islmc_cgt_dist_order
        where mc04_cgt_xy_period_code = #{dateCode} -- 周期
        and ma02_toba_prod_trade_type_code = #{cgtType} -- 卷烟
        and ba_com_org_code = #{comId}
        and mc04_cgt_dist_order_status != '90' -- 非作废
        group by ac_cgt_carton_code) b on ici.ac_cgt_carton_code = b.ac_cgt_carton_code
        left join (select ixaai.ac_cgt_carton_code,
        ixaai.ac_two_level_cig_code,
        sum(COALESCE(ixaai.md02_cgt_xy_adjust_qty, 0)) qty
        from mc04_islmc_xy_adjust_apply ixaa
        inner join mc04_islmc_xy_adjust_apply_item ixaai
        on ixaa.mc04_xy_adjust_id = ixaai.mc04_xy_adjust_id
        where ixaa.mc04_cgt_xy_period_code = #{dateCode} -- 周期
        and ixaa.ma02_toba_prod_trade_type_code = #{cgtType} -- 卷烟
        and ixaa.mc04_xy_adjust_status = '90' -- 申请单状态 完成
        and ixaa.ba_com_org_code = #{comId}
        and ixaa.mc04_xy_adjust_type = #{adjustType}
        group by ixaai.ac_cgt_carton_code, ixaai.ac_two_level_cig_code) c
        on ici.ac_cgt_carton_code = c.ac_cgt_carton_code and
        ici.ac_two_level_cig_code = c.ac_two_level_cig_code
        left join (select ixaai.ac_cgt_carton_code,
        ixaai.ac_two_level_cig_code,
        sum(COALESCE(ixaai.md02_cgt_xy_adjust_qty, 0)) qty
        from mc04_islmc_xy_adjust_apply ixaa
        inner join mc04_islmc_xy_adjust_apply_item ixaai
        on ixaa.mc04_xy_adjust_id = ixaai.mc04_xy_adjust_id
        where ixaa.mc04_cgt_xy_period_code = #{dateCode} -- 周期
        and ixaa.ma02_toba_prod_trade_type_code = #{cgtType} -- 卷烟
        and ixaa.mc04_xy_adjust_status != '90' -- 申请单状态 完成
        and ixaa.ba_com_org_code = #{comId}
        group by ixaai.ac_cgt_carton_code, ixaai.ac_two_level_cig_code) d
        on ici.ac_cgt_carton_code = d.ac_cgt_carton_code and
        ici.ac_two_level_cig_code = d.ac_two_level_cig_code
        where ici.ba_com_org_code = #{comId}
        <choose>
            <when test="'0'.equals(cgtType)">
                and ici.ac_one_level_class_type_code = '01'
            </when>
            <otherwise>
                and ici.ac_one_level_class_type_code = '02'
            </otherwise>
        </choose>
        and ici.ac_one_level_class_type_code = '01'

        and ici.is_use = '1' -- 使用
    </select>
    <select id="queryExcepLastApplyList"
            resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">

        select
        b.ac_cgt_carton_code as acCgtCartonCode,
        sum(b.md02_cgt_xy_adjust_qty) as md02CgtXyAdjustQty,
        sum(abs(b.md02_cgt_xy_adjust_qty)) as md02CgtXyAdjustQtyAbs,
        a.ba_com_org_code as baComOrgCode,
        b.ac_two_level_cig_code as acTwoLevelCigCode
        from mc04_islmc_xy_adjust_apply a
        inner join mc04_islmc_xy_adjust_apply_item b on a.mc04_xy_adjust_id = b.mc04_xy_adjust_id
        where a.mc04_is_latest_adjust = '0'
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and b.ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and a.mc04_cgt_xy_period_code = #{dateCode}
        and a.ma02_toba_prod_trade_type_code = #{cgtType}
        group by b.ac_cgt_carton_code,a.ba_com_org_code,b.ac_two_level_cig_code
    </select>
    <select id="queryFnishedApplyList"
            resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">
        select ixaa.ba_com_org_code as baComOrgCode,
        ixaai.ac_cgt_carton_code as acCgtCartonCode,
        sum(COALESCE(ixaai.md02_cgt_xy_adjust_qty, 0)) as md02CgtXyAdjustQty
        from mc04_islmc_xy_adjust_apply ixaa
        inner join mc04_islmc_xy_adjust_apply_item ixaai on ixaa.mc04_xy_adjust_id = ixaai.mc04_xy_adjust_id
        where ixaa.mc04_cgt_xy_period_code = #{dateCode} -- 周期
        and ixaa.ma02_toba_prod_trade_type_code = #{cgtType} -- 卷烟
        and ixaa.mc04_xy_adjust_status = '90' -- 申请单状态 完成
        <if test="comIds != null and comIds.size() > 0">
            and ixaa.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and ixaai.ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by ixaai.ac_cgt_carton_code, ixaa.ba_com_org_code
    </select>
    <select id="queryInitXyAdjustApply"
            resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">
        select ici.ac_cgt_carton_code as acCgtCartonCode,
        ici.ac_two_level_cig_code as acTwoLevelCigCode,
        ici.ba_com_org_code as baComOrgCode,
        COALESCE(b.qty, 0) as reqQty,
        COALESCE(c.qty, 0) as md02CgtXyAdjustedQty,
        #{cgtType} as ma02TobaProdTradeTypeCode,
        #{dateCode} as mc04CgtXyPeriodCode
        from mc04_islmc_com_item ici
        left join (select ac_cgt_carton_code, sum(md02_cgt_dist_confirm_qty) qty
        from mc04_cgt_dist_order
        where mc04_cgt_xy_period_code = #{dateCode}
        and ma02_toba_prod_trade_type_code = #{cgtType}
        and ba_com_org_code = #{comId}
        and mc04_cgt_dist_order_status != '90'
        group by ac_cgt_carton_code) b on ici.ac_cgt_carton_code = b.ac_cgt_carton_code
        left join (select ixaai.ac_cgt_carton_code,
        ixaai.ac_two_level_cig_code,
        sum(COALESCE(ixaai.md02_cgt_xy_adjust_qty, 0)) qty
        from mc04_islmc_xy_adjust_apply ixaa
        inner join mc04_islmc_xy_adjust_apply_item ixaai
        on ixaa.mc04_xy_adjust_id = ixaai.mc04_xy_adjust_id
        where ixaa.mc04_cgt_xy_period_code = #{dateCode}
        and ixaa.ma02_toba_prod_trade_type_code = #{cgtType}
        and ixaa.mc04_xy_adjust_status = '90'
        and ixaa.ba_com_org_code = #{comId}
        group by ixaai.ac_cgt_carton_code, ixaai.ac_two_level_cig_code) c
        on ici.ac_cgt_carton_code = c.ac_cgt_carton_code and
        ici.ac_two_level_cig_code = c.ac_two_level_cig_code
        where ici.ba_com_org_code = #{comId}
        and ici.is_use = '1'
        <choose>
            <when test="'0'.equals(cgtType)">
                and ici.ac_one_level_class_type_code = '01'
            </when>
            <otherwise>
                and ici.ac_one_level_class_type_code = '02'
            </otherwise>
        </choose>
    </select>
    <select id="queryAllApplyList" resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">
        select b.ac_cgt_carton_code as acCgtCartonCode,
        sum(b.md02_cgt_xy_adjust_qty) as md02CgtXyAdjustQty,
        sum(abs(b.md02_cgt_xy_adjust_qty)) as md02CgtXyAdjustQtyAbs,
        a.ba_com_org_code as baComOrgCode,
        b.ac_two_level_cig_code as acTwoLevelCigCode
        from mc04_islmc_xy_adjust_apply a
        inner join mc04_islmc_xy_adjust_apply_item b on a.mc04_xy_adjust_id = b.mc04_xy_adjust_id
        where mc04_cgt_xy_period_code = #{dateCode}
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and b.ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and a.ma02_toba_prod_trade_type_code = #{cgtType}
        group by b.ac_cgt_carton_code,a.ba_com_org_code,b.ac_two_level_cig_code
    </select>
    <select id="queryApplyList" resultType="com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply">
        select
        a.mc04_xy_adjust_id as mc04XyAdjustId,
        b.mc04_xy_adjust_item_id as mc04XyAdjustItemId,
        a.mc04_xy_adjust_status as mc04XyAdjustStatus,
        b.ac_cgt_carton_code as acCgtCartonCode,
        b.ac_two_level_cig_code as acTwoLevelCigCode,
        a.ba_com_org_code as baComOrgCode,
        coalesce(sum(b.md02_cgt_xy_adjust_qty), 0) as md02CgtXyAdjustQty,
        abs(coalesce(sum(b.md02_cgt_xy_adjust_qty), 0)) as md02CgtXyAdjustQtyAbs,
        coalesce(do.reqQty,0) as reqQty,
        coalesce(b.mc04_xy_adjust_report_qty, 0) AS mc04XyAdjustReportQty,
        coalesce(b.mc04_xy_adjust_region_confirm_qty,0) AS mc04XyAdjustRegionConfirmQty,
        coalesce(b.mc04_xy_adjust_director_audit_qty , 0) AS mc04XyAdjustDirectorAuditQty,
        coalesce(b.mc04_xy_adjust_brand_audit_qty,0) AS mc04XyAdjustBrandAuditQty,
        coalesce(b.mc04_xy_adjust_busi_audit_qty,0) AS mc04XyAdjustBusiAuditQty,
        coalesce(b.mc04_xy_adjust_leader_audit_qty,0) as mc04XyAdjustLeaderAuditQty,
        coalesce(b.mc04_xy_adjust_plan_confirm_qty, 0) AS mc04XyAdjustPlanConfirmQty,
        a.md02_cgt_xy_no as md02CgtXyNo,
        b.create_name as createName,
        b.za_remark as zaRemark,
        al.allAdjustQty as allAdjustQty,
        al.allAdjustQtyAbs as allAdjustQtyAbs,
        fin.finshedAdjustQty as finshedAdjustQty,
        exc.excepLastAdjustQty as excepLastAdjustQty,
        coalesce(exc.excepLastAdjustQtyAbs,0) as excepLastAdjustQtyAbs,
        a.mc04_xy_adjust_type as mc04XyAdjustType,
        coalesce(exc.excepLastAdjustQtyAbs,0) as adjustedQty,
        a.ma02_toba_prod_trade_type_code as ma02TobaProdTradeTypeCode,
        a.mc04_cgt_xy_period_code as mc04CgtXyPeriodCode
        <include refid="fromSql"/>
        left join(
        select
        <include refid="queryAttr"/>
        coalesce(sum(b.md02_cgt_xy_adjust_qty), 0) as allAdjustQty,
        sum(abs(b.md02_cgt_xy_adjust_qty)) as allAdjustQtyAbs
        <include refid="fromSql"></include>
        <include refid="filterSql"/>
        -- 查询所有变更申请单
        <include refid="groupSql"/>
        ) as al on al.acCgtCartonCode = b.ac_cgt_carton_code and a.ba_com_org_code = al.baComOrgCode and
        al.acTwoLevelCigCode = b.ac_two_level_cig_code
        left join (
        select
        <include refid="queryAttr"/>
        sum(COALESCE(b.md02_cgt_xy_adjust_qty, 0)) as finshedAdjustQty
        <include refid="fromSql"></include>
        <include refid="filterSql"/>
        and a.mc04_xy_adjust_status = '90' -- 子表查已完成的变更申请单
        <include refid="groupSql"/>
        ) as fin on fin.acCgtCartonCode = b.ac_cgt_carton_code and a.ba_com_org_code = fin.baComOrgCode and
        fin.acTwoLevelCigCode = b.ac_two_level_cig_code
        left join (
        select
        <include refid="queryAttr"/>
        sum(b.md02_cgt_xy_adjust_qty) as excepLastAdjustQty,
        sum(abs(b.md02_cgt_xy_adjust_qty)) as excepLastAdjustQtyAbs
        <include refid="fromSql"></include>
        <include refid="filterSql"/>
        and a.mc04_is_latest_adjust != '1'-- 子表查除最后一次变更申请单
        <include refid="groupSql"/>
        ) as exc on exc.acCgtCartonCode = b.ac_cgt_carton_code and a.ba_com_org_code = exc.baComOrgCode and
        exc.acTwoLevelCigCode = b.ac_two_level_cig_code


        left join (
        select
        ac_cgt_carton_code,
        ac_two_level_cig_code,
        ba_com_org_code,
        sum(md02_cgt_dist_confirm_qty) reqQty
        from mc04_cgt_dist_order
        where mc04_cgt_xy_period_code = #{dateCode} -- 周期
        and ma02_toba_prod_trade_type_code = #{cgtType} -- 卷烟
        <if test="comIds != null and comIds.size() > 0">
            and ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and mc04_cgt_dist_order_status != '90' -- 非作废
        group by ac_cgt_carton_code, ac_two_level_cig_code, ba_com_org_code
        ) as do
        on do.ac_cgt_carton_code = b.ac_cgt_carton_code
        and do.ac_two_level_cig_code = b.ac_two_level_cig_code
        and do.ba_com_org_code = a.ba_com_org_code

        <include refid="filterSql"/>
        and a.mc04_is_latest_adjust = '1' -- 主表查最新一次变更申请单
        <if test="adjustType != null and !''.equals(adjustType)">
            and a.mc04_xy_adjust_type = #{adjustType}
        </if>
        <if test="finshEndDate != null and !''.equals(finshEndDate)">
            and SUBSTRING(a.update_time, 1, 8) &lt; = #{finshEndDate}
        </if>
        <if test="finshBeginDate != null and !''.equals(finshBeginDate)">
            and SUBSTRING(a.update_time, 1, 8) >= #{finshBeginDate}
        </if>
        <if test="applyStatus != null and !''.equals(applyStatus)">
            and a.mc04_xy_adjust_status = #{applyStatus}
        </if>
        <include refid="groupSql"/>
    </select>

    <sql id="fromSql">
        from mc04_islmc_xy_adjust_apply a
        inner join mc04_islmc_xy_adjust_apply_item b
        on a.mc04_xy_adjust_id = b.mc04_xy_adjust_id
    </sql>
    <sql id="filterSql">
        where a.mc04_cgt_xy_period_code = #{dateCode}
        and a.ma02_toba_prod_trade_type_code = #{cgtType}
        <if test="comIds != null and comIds.size() > 0">
            and a.ba_com_org_code in
            <foreach collection="comIds" item="comId" open="(" separator="," close=")">
                #{comId}
            </foreach>
        </if>
        <if test="itemIds != null and itemIds.size() > 0">
            and b.ac_cgt_carton_code in
            <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="groupSql">
        group by b.ac_cgt_carton_code,a.ba_com_org_code,b.ac_two_level_cig_code
    </sql>
    <sql id="queryAttr">
        a.ba_com_org_code
        as baComOrgCode,
        b.ac_cgt_carton_code    as acCgtCartonCode,
        b.ac_two_level_cig_code as acTwoLevelCigCode,
    </sql>
</mapper>