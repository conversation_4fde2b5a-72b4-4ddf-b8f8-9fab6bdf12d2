/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.cont.order;

import com.tobacco.app.isale.domain.model.cont.order.UploadCont;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> wang<PERSON><PERSON><PERSON>
 * @email : <EMAIL>
 * @create_time : 2025/07/21 09:58
 * @description : 合同管理Mapper
 */
@Mapper
public interface ManageOrderManageMapper {


    /**
     * 获取要上传第三方系统的合同信息
     *
     * @param contIdList 主键列表
     * @param icomCode   工业编码
     * @return List<UploadCont>
     * <AUTHOR> wanglu<PERSON>01
     * @create_time : 2025-08-04 14:29:32
     * @description : 获取要上传第三方系统的合同信息
     */
    List<UploadCont> getThirdUploadContList(List<String> contIdList, String icomCode);

    /**
     * 更新合同协议编号
     *
     * @param contOrderIdList 主键列表
     * <AUTHOR> wang<PERSON><PERSON><PERSON>
     * @create_time : 2025-08-04 14:29:32
     * @description : 更新合同协议编号
     */
    void updateAgreeNo(List<String> contOrderIdList);
}
