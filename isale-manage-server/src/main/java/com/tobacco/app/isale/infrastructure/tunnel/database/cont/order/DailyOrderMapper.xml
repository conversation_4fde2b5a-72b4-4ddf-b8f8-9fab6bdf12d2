<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.cont.order.DailyOrderMapper">
    <select id="queryXyOrgSaleData"
            resultType="com.tobacco.app.isale.domain.model.order.preshipment.XyOrgSaleDay">
        select mc04_mg_cgt_10th_eod_stk_qty / mc04_cgt_roll_7_days_avg_sale_qty as stock_sale_ratio_roll7_days,
        mc04_mg_cgt_10th_eod_stk_qty / mc04_cgt_roll_28_days_avg_sale_qty as stock_sale_ratio_roll28_days,
        mc04_mg_cgt_10th_eod_stk_qty,
        md03_cgt_10th_com_coe_sale_qty,
        ma02_cgt_com_from_ind_buy_qty,
        mc04_purchase_sale_stk_date
        from MC04_IND_XY_ORG_CGT_PURCH_SALE_STK_DAY
        where mc04_xy_org_code = #{baComOrgCode}
        and mc04_purchase_sale_stk_date =
        (
        select max(mc04_purchase_sale_stk_date) from MC04_IND_XY_ORG_CGT_PURCH_SALE_STK_DAY
        where mc04_xy_org_code = #{baComOrgCode}
        <if test="acCgtCartonCodes != null and acCgtCartonCodes.size() > 0">
            and ac_cgt_carton_code in
            <foreach item="item" index="index" collection="acCgtCartonCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and mc04_purchase_sale_stk_date &lt; #{mc04PurchaseSaleStkDate}
        )
        <if test="acCgtCartonCodes != null and acCgtCartonCodes.size() > 0">
            and ac_cgt_carton_code in
            <foreach item="item" index="index" collection="acCgtCartonCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryPreshipmentPlanData"
            resultType="com.tobacco.app.isale.domain.model.order.preshipment.PreshipmentPlan">
        select i.mc04_nc_stock_move_demand_qty,
        i.mc04_nc_stock_move_demand_qty - coalesce(p.qty, 0) as moveRemainQty,
        i.ac_cgt_carton_code,
        d.ba_com_org_code
        from mc04_islmc_nc_stock_move_demand d
        inner join mc04_islmc_nc_stock_move_demand_item i on d.mc04_nc_stock_move_demand_id =
        i.mc04_nc_stock_move_demand_id
        left join (
        select ba_com_org_code,
        ac_cgt_carton_code,
        sum(ma02_cgt_pl_adjusted_qty) qty
        from mc04_islmc_cgt_day_plan
        where ma02_plan_month = #{ma02PlanMonth}
        and ba_com_org_code = #{baComOrgCode}
        and mc04_cont_zero_clock_type = '1'
        and ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
        group by ba_com_org_code,ac_cgt_carton_code
        ) p on d.ba_com_org_code = p.ba_com_org_code and i.ac_cgt_carton_code = p.ac_cgt_carton_code
        where d.icom_code = #{icomCode}
        and d.ba_com_org_code = #{baComOrgCode}
        and d.ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
        and d.za_occurrence_year = #{zaOccurrenceYear}
        and d.mc04_nc_stock_move_demand_status = '30'
        <if test="acTwoLevelCigCodes != null and acTwoLevelCigCodes.size()>0">
            and i.ac_two_level_cig_code in
            <foreach item="item" index="index" collection="acTwoLevelCigCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="acCgtCartonCodes != null and acCgtCartonCodes.size() > 0">
            and i.ac_cgt_carton_code in
            <foreach item="item" index="index" collection="acCgtCartonCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getDayPlan" resultType="com.tobacco.app.isale.domain.model.order.preshipment.MonthData">
        select ac_two_level_cig_code,
               ac_cgt_carton_code,
               ba_com_org_code,
               sum(ma02_cgt_pl_adjusted_qty) as regularZeroQty
        from mc04_islmc_cgt_day_plan
        where ba_com_org_code = #{baComOrgCode}
          and ma02_plan_month = #{ma02PlanMonth}
          and mc04_month_sale_plan_type in ('10', '30')
          and icom_code = #{icomCode}
          and ma02_toba_prod_trade_type_code = #{ma02TobaProdTradeTypeCode}
        group by ac_two_level_cig_code,
                 ac_cgt_carton_code,
                 ba_com_org_code
    </select>
</mapper>