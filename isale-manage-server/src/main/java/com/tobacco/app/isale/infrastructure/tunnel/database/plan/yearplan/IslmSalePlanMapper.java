/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.PlanQty;

import java.util.List;
import java.util.Map;


/**
 * @Description: Mapper 接口
 *
 * @Author: qintian
 * @Since: 2025-07-18
 * @Email: <EMAIL>
 * @Create: 2025-07-18
 */
public interface IslmSalePlanMapper {

    /**
     * 获取所有规格在指定组织下的年销售和调拨统计数据
     * @param year 业务年份
     */
    List<PlanQty> getAllProductSaleAndAllotQtyListForYearSnSw(String year);

    List<PlanQty> getAllProductEndStkSaleAndAllotQtyListForYearProvince(String year);
}