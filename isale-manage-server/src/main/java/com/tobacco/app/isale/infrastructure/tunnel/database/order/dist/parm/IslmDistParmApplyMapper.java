/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.parm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.domain.model.order.dist.parm.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR> liuwan<PERSON>
 * @create_time : 2025/06/03 19:58
 * @description : 配货参数申请持久层
 */
@Mapper
public interface IslmDistParmApplyMapper {

    /**
     * 分页查询配货参数申请列表
     *
     * @param page              分页信息
     * @param distParmApplyPage 分页查询请求参数
     * @return 分页数据响应对象
     */
    Page<DistParmApply> queryDistParmApplyPage(
            Page<DistParmApply> page, @Param("queryParam") DistParmApplyPage distParmApplyPage);

    /**
     * 查询本工业所有协议单位的配货参数申请的状态
     *
     * @param req 查询请求参数
     * @return 配货参数申请状态数据
     */
    List<DistParmApplyStatus> queryDistParmApplyStatus(DistParmApply req);

    /**
     * 查询本工业所有协议单位的行业配货参数的状态
     *
     * @param req 查询请求参数
     * @return 配货参数申请状态数据
     */
    List<DistParmStatus> queryNationParmStatus(DistParm req);



}
