<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic.AgreementMapper">

    <resultMap id="remainAgreementContTypeMap"
               type="com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcXy">
        <id property="mc04CgtXyId" column="mc04_cgt_xy_id"/>
        <result property="md02CgtXyNo" column="md02_cgt_xy_no"/>
        <result property="baComOrgCode" column="ba_com_org_code"/>
        <result property="mc04CgtXyPeriodCode" column="mc04_cgt_xy_period_code"/>
        <result property="ma02TobaProdTradeTypeCode" column="ma02_toba_prod_trade_type_code"/>
        <collection property="mc04IslmcXyItemList"
                    ofType="com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcXyItem">
            <result property="acCgtCartonCode" column="ac_cgt_carton_code"/>
            <result property="md02CgtXyOriginalQty" column="md02_cgt_xy_original_qty"/>
            <result property="md02CgtXyAdjustedQty" column="md02_cgt_xy_adjusted_qty"/>
            <result property="exeQty" column="exe_qty"/>
            <result property="remainQty" column="remain_qty"/>
        </collection>
    </resultMap>

    <select id="getRemainAgreementContType" resultMap="remainAgreementContTypeMap">
        select xy_table.mc04_cgt_xy_id,
               xy_table.md02_cgt_xy_no,
               xy_table.ba_com_org_code,
               xy_table.mc04_cgt_xy_period_code,
               xy_table.ma02_toba_prod_trade_type_code,
               xy_table.ac_cgt_carton_code,
               xy_table.md02_cgt_xy_original_qty,
               xy_table.md02_cgt_xy_adjusted_qty,
               coalesce(cont_order_table.exe_qty, 0) as exe_qty,
               (xy_table.md02_cgt_xy_adjusted_qty - coalesce(cont_order_table.exe_qty, 0)) as remain_qty
        from (select xy.mc04_cgt_xy_id,
                     xy.md02_cgt_xy_no,
                     xy.ba_com_org_code,
                     xy.mc04_cgt_xy_period_code,
                     xy.ma02_toba_prod_trade_type_code,
                     xy_item.ac_cgt_carton_code,
                     xy_item.md02_cgt_xy_original_qty,
                     xy_item.md02_cgt_xy_adjusted_qty
              from mc04_islmc_xy as xy,
                   mc04_islmc_xy_item as xy_item
              where xy.mc04_cgt_xy_id = xy_item.mc04_cgt_xy_id
                <foreach collection="xyNoList" open="and xy.md02_cgt_xy_no in(" close=")" separator="," item="xyNo">
                    #{xyNo}
                </foreach>
                and xy.mc04_cgt_xy_status = '1') as xy_table
                 left join (select cont_order.md02_cgt_xy_no,
                                   cont_order.ba_com_org_code,
                                   cont_order_item.ac_cgt_carton_code,
                                   sum(cont_order_item.md02_cgt_trade_cont_qty) exe_qty
                            from mc04_islmc_cont_order as cont_order,
                                 mc04_islmc_cont_order_item as cont_order_item
                            where cont_order.mc04_cont_order_id = cont_order_item.mc04_cont_order_id
                                <foreach collection="xyNoList" open="and cont_order.md02_cgt_xy_no in(" close=")" separator="," item="xyNo">
                                    #{xyNo}
                                </foreach>
                              and cont_order.mc04_cgt_trade_cont_status >= #{status}
                              and cont_order.mc04_cgt_trade_cont_status &lt;= '60'
                            group by cont_order.md02_cgt_xy_no,
                                     cont_order.ba_com_org_code,
                                     cont_order_item.ac_cgt_carton_code) cont_order_table
                           on xy_table.md02_cgt_xy_no = cont_order_table.md02_cgt_xy_no
                               and xy_table.ba_com_org_code = cont_order_table.ba_com_org_code and
                              xy_table.ac_cgt_carton_code =
                              cont_order_table.ac_cgt_carton_code
    </select>
</mapper>