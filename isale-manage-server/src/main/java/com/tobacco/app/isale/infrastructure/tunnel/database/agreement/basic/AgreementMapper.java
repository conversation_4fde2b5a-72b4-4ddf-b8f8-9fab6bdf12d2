/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic;


import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcXy;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Email : @inspur.com
 * @Create : 2025/05/07 14:09
 * @Description : 协议数据数据持久化接口
 */
@Mapper
public interface AgreementMapper {
    /**
     * @param xyNoList  协议编号
     * @param status    合同状态
     * @return List<Mc04IslmcXy>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 15:15:01
     * @description : 获取剩余协议
     */

    List<Mc04IslmcXy> getRemainAgreementContType(List<String> xyNoList, String status);
}
