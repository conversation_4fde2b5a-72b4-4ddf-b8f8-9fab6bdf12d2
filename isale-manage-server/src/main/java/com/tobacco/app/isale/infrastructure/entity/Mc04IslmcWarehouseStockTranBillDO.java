/*
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 *  版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DomainObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 前置库移库单
 * @Author: wuhaoran01
 * @Since: 2025-05-17
 * @Email: <EMAIL>
 * @Create: 2025-05-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_warehouse_stock_tran_bill")
@DomainObject(name = "前置库移库单", desc = "前置库移库单")
public class Mc04IslmcWarehouseStockTranBillDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
    /**
     * 卷烟移库单编码
     */

    @TableId(value = "mc04_warehouse_stock_tran_bill_id", type = IdType.NONE)
    @Field(value = "卷烟移库单编码", name = "卷烟移库单编码")
    private String mc04WarehouseStockTranBillId;
    /**
     * 烟草制品交易业务类型代码
     */

    @Field(value = "烟草制品交易业务类型代码", name = "烟草制品交易业务类型代码")
    private String ma02TobaProdTradeTypeCode;
    /**
     * 商业公司编码
     */

    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;
    /**
     * 计划月份
     */

    @Field(value = "计划月份", name = "计划月份")
    private String ma02PlanMonth;


    /**
     * 时间周期代码
     */

    @Field(value = "时间周期代码", name = "时间周期代码")
    private String mc04DatePeriodCode;
    /**
     * 卷烟交易合同要求到货日期
     */

    @Field(value = "卷烟交易合同要求到货日期", name = "卷烟交易合同要求到货日期")
    private String mc04AskComeDate;

    /**
     * 卷烟交易合同要求到货日期
     */

    @Field(value = "卷烟交易合同要求移库日期", name = "卷烟交易合同要求移库日期")
    private String mc05PreposMovePlMoveDate;
    /**
     * 卷烟发货仓库代码
     */

    @Field(value = "卷烟发货仓库代码", name = "卷烟发货仓库代码")
    private String md02CgtOutStorehouseCode;
    /**
     * 卷烟收货仓库代码
     */

    @Field(value = "卷烟收货仓库代码", name = "卷烟收货仓库代码")
    private String md02CgtInStorehouseCode;
    /**
     * 状态
     */

    @Field(value = "状态", name = "状态")
    private String mc04WarehouseStockTranBillStatus;

    /**
     * 工业公司Code
     */
    @Field(value = "工业公司Code", name = "工业公司Code")
    private String icomCode;


    /**
     * 备注
     */

    @Field(value = "备注", name = "备注")
    private String zaRemark;

    /**
     * 是否删除
     */

    @Field(value = "删除标识", name = "删除标识")
    private String isDelete;


}
