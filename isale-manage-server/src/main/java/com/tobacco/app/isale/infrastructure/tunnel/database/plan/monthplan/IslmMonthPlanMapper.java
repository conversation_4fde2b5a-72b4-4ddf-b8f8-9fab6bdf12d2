/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: jinfuli
 * @Date: 2025/6/14
 * @Description:
 */
@Mapper
public interface IslmMonthPlanMapper {


    /**
     * 根据日期和商业公司查询执行量
     *
     * @param param
     * @return
     */
    @MapKey("ac_cgt_carton_code")
    Map<String,Object> getExeQyt1(Map param);

    /**
     * 根据日期和商业公司查询执行量
     *
     * @param param
     * @return
     */
    @MapKey("ac_cgt_carton_code")
    Map<String,Object> getExeQyt2(Map param);

    /**
     * 根据条件查询协议执行情况
     *
     * @param param
     * @return
     */
    @DS("dws")
    List<Mc04IslmMonthSalePlan> getAgreementExecutionByCriteria(Map param);

    /**
     * 全年销售计划
     *
     * @param param
     * @return
     */
    @DS("dws")
    List<Mc04IslmMonthSalePlan> getYearSalesPlan(Map param);


    /**
     * 全年销售执行量
     *
     * @param param
     * @return
     */
    @DS("dws")
    List<Mc04IslmMonthSalePlan> getAnnualSalesExecution(Map param);

    /**
     * 全季销售计划
     *
     * @param param
     * @return
     */
    @DS("dws")
    List<Mc04IslmMonthSalePlan> getQuarterSalesPlan(Map param);

    /**
     * 全季销售执行量
     *
     * @param param
     * @return
     */
    @DS("dws")
    List<Mc04IslmMonthSalePlan> getQuarterSalesExecution(Map param);

    /**
     * 月实际销量（联合库获取
     *
     * @return
     */
    @DS("dws")
    List<Mc04IslmMonthSalePlan> getMonthActualSale(Map param);

    /**
     * 月实际调拨量
     *
     * @param param
     * @return
     */
    List<Mc04IslmMonthSalePlan> getMonthActualAllocate(Map param);

    /**
     * 月调拨计划量
     *
     * @param param
     * @return
     */
    List<Mc04IslmMonthSalePlan> getMonthAllocatePlan(Map param);

    /**
     * 月销售计划量
     *
     * @param param
     * @return
     */
    List<Mc04IslmMonthSalePlan> getMonthReasonableSaleRatio(Map param);

    /**
     * 月计划量
     **/
    List<Mc04IslmMonthSalePlan> getMonthPlan(String comIds, String monthCode, String cgtType);
}
