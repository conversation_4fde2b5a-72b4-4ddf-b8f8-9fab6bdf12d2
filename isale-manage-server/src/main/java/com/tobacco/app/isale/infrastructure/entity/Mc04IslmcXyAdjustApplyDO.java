/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 卷烟半年协议变更申请
 *
 * @Author: jinfuli
 * @Since: 2025-05-12
 * @Email: <EMAIL>
 * @Create: 2025-05-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_xy_adjust_apply")
@DataObject(name = "卷烟半年协议变更申请", desc = "卷烟半年协议变更申请")
public class Mc04IslmcXyAdjustApplyDO {

    private static final long serialVersionUID = 1L;
                /**
             * 卷烟半年协议调整单编号
             */

            @TableId(value = "mc04_xy_adjust_id", type = IdType.NONE)
    @Field(value = "卷烟半年协议调整单编号", name = "卷烟半年协议调整单编号")
    private String mc04XyAdjustId;
                                /**
             * 卷烟协议编号
             */

    @Field(value = "卷烟协议编号", name = "卷烟协议编号")
    private String md02CgtXyNo;
                                /**
             * 商业公司编码
             */

    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;
                                /**
             * 卷烟协议周期
             */
    @Field(value = "卷烟协议周期", name = "卷烟协议周期")
    private String mc04CgtXyPeriodCode;
                                /**
             * 烟草制品交易业务类型代码
             */
    @Field(value = "烟草制品交易业务类型代码", name = "烟草制品交易业务类型代码")
    private String ma02TobaProdTradeTypeCode;
                                /**
             * 协议变更申请单状态
             */
    @Field(value = "协议变更申请单状态", name = "协议变更申请单状态")
    private String mc04XyAdjustStatus;
                                /**
             * 超限调整标识
             */
    @Field(value = "超限调整标识", name = "超限调整标识")
    private String mc04IsExcessLimitAdjust;
                                /**
             * 最近一次调整标识
             */
    @Field(value = "最近一次调整标识", name = "最近一次调整标识")
    private String mc04IsLatestAdjust;

    @Field(value = "协议变更类型", name = "协议变更类型")
    private String mc04XyAdjustType;
                

}
