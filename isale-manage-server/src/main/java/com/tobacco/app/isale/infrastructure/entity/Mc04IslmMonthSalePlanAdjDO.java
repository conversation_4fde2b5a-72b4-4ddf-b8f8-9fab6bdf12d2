package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * 月计划调整上报 视图层对象/值对象
 * </p>
 *
 * @Author: jinfuli
 * @Since: 2025-08-11
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("mc04_islm_month_sale_plan_adj")
public class Mc04IslmMonthSalePlanAdjDO extends BaseFactor {

    private static final long serialVersionUID = 1L;

    /**
     * 月计划调整上报明细编号
     */
    @TableId(value = "mc04_month_sale_plan_adj_item_id", type = IdType.NONE)
    @Field(value = "月计划调整上报明细编号", name = "月计划调整上报明细编号")
    private String mc04MonthSalePlanAdjItemId;

    /**
     * 月计划调整上报数据编号
     */
    @Field(value = "月计划调整上报数据编号", name = "月计划调整上报数据编号")
    private String mc04MonthSalePlanAdjId;

    /**
     * 最近一次调整标识
     */
    @Field(value = "最近一次调整标识", name = "最近一次调整标识")
    private String mc04IsLatestAdjust;

    /**
     * 烟草制品交易业务类型代码
     */
    @Field(value = "烟草制品交易业务类型代码", name = "烟草制品交易业务类型代码")
    private String ma02TobaProdTradeTypeCode;

    /**
     * 卷烟业务月份
     */
    @Field(value = "卷烟业务月份", name = "卷烟业务月份")
    private String zaOccurrenceMonth;

    /**
     * 计划月份
     */
    @Field(value = "计划月份", name = "计划月份")
    private String ma02PlanMonth;

    /**
     * 调拨计划类型
     */
    @Field(value = "调拨计划类型", name = "调拨计划类型")
    private String mc04MonthSalePlanType;

    /**
     * 省级公司代码
     */
    @Field(value = "省级公司代码", name = "省级公司代码")
    private String baProvOrgCode;

    /**
     * 商业公司编码
     */
    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;

    /**
     * 卷烟协议周期
     */
    @Field(value = "卷烟协议周期", name = "卷烟协议周期")
    private String mc04CgtXyPeriodCode;

    /**
     * 上报数据的状态
     */
    @Field(value = "上报数据的状态", name = "上报数据的状态")
    private String mc04MonthSalePlanStatus;

    /**
     * 卷烟代码（条）
     */
    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;

    /**
     * 卷烟名称
     */
    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;

    /**
     * 卷烟二级牌号编码
     */
    @Field(value = "卷烟二级牌号编码", name = "卷烟二级牌号编码")
    private String acTwoLevelCigCode;

    /**
     * 卷烟二级牌号名称
     */
    @Field(value = "卷烟二级牌号名称", name = "卷烟二级牌号名称")
    private String acTwoLevelCigName;

    /**
     * 卷烟价位段代码
     */
    @Field(value = "卷烟价位段代码", name = "卷烟价位段代码")
    private String acCgtPriceSegmentCode;

    /**
     * 卷烟价位段名称
     */
    @Field(value = "卷烟价位段名称", name = "卷烟价位段名称")
    private String acCgtPriceSegmentName;

    /**
     * 卷烟含税调拨价格
     */
    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;

    /**
     * 卷烟统一批发价
     */
    @Field(value = "卷烟统一批发价", name = "卷烟统一批发价")
    private BigDecimal acCgtTradePrice;

    /**
     * 卷烟商业期初库存量（万支）
     */
    @Field(value = "卷烟商业期初库存量（万支）", name = "卷烟商业期初库存量（万支）")
    @TableField(value = "md03_cgt_10th_com_init_stk_qty")
    private BigDecimal md03Cgt10thComInitStkQty;

    /**
     * 卷烟销售计划原计划量
     */
    @Field(value = "卷烟销售计划原计划量", name = "卷烟销售计划原计划量")
    private BigDecimal mc04CgtSalePlanOriginQty;

    /**
     * 卷烟销售计划剩余量
     */
    @Field(value = "卷烟销售计划剩余量", name = "卷烟销售计划剩余量")
    private BigDecimal mc04CgtSalePlanSurplQty;

    /**
     * 卷烟销售计划销区上报调整量
     */
    @Field(value = "卷烟销售计划销区上报调整量", name = "卷烟销售计划销区上报调整量")
    private BigDecimal mc04CgtSalePlanReportAdjQty;

    /**
     * 卷烟销售计划区域确认调整量
     */
    @Field(value = "卷烟销售计划区域确认调整量", name = "卷烟销售计划区域确认调整量")
    private BigDecimal mc04CgtSalePlanRegionConfirmAdjQty;

    /**
     * 卷烟销售计划业务科确认调整量
     */
    @Field(value = "卷烟销售计划业务科确认调整量", name = "卷烟销售计划业务科确认调整量")
    private BigDecimal mc04CgtSalePlanBdConfirmAdjQty;

    /**
     * 卷烟销售计划计划科确认调整量
     */
    @Field(value = "卷烟销售计划计划科确认调整量", name = "卷烟销售计划计划科确认调整量")
    private BigDecimal mc04CgtSalePlanPdConfirmAdjQty;

    /**
     * 卷烟销售计划调整后计划量
     */
    @Field(value = "卷烟销售计划调整后计划量", name = "卷烟销售计划调整后计划量")
    private BigDecimal mc04CgtSalePlanAdjustedQty;

    /**
     * 卷烟调拨计划系统推荐量
     */
    @Field(value = "卷烟调拨计划系统推荐量", name = "卷烟调拨计划系统推荐量")
    private BigDecimal mc04CgtAllotPlanRecomQty;

    /**
     * 卷烟调拨计划原计划量
     */
    @Field(value = "卷烟调拨计划原计划量", name = "卷烟调拨计划原计划量")
    private BigDecimal ma02CgtPlResolOrigplanQty;

    /**
     * 烟草制品调拨计划剩余计划量
     */
    @Field(value = "烟草制品调拨计划剩余计划量", name = "烟草制品调拨计划剩余计划量")
    private BigDecimal md02TobaTranPlRestQty;

    /**
     * 卷烟调拨计划销区上报调整量
     */
    @Field(value = "卷烟调拨计划销区上报调整量", name = "卷烟调拨计划销区上报调整量")
    private BigDecimal mc04CgtAllotPlanReportAdjQty;

    /**
     * 卷烟调拨计划区域确认调整量
     */
    @Field(value = "卷烟调拨计划区域确认调整量", name = "卷烟调拨计划区域确认调整量")
    private BigDecimal mc04CgtAllotPlanRegionConfirmAdjQty;

    /**
     * 卷烟调拨计划业务科确认调整量
     */
    @Field(value = "卷烟调拨计划业务科确认调整量", name = "卷烟调拨计划业务科确认调整量")
    private BigDecimal mc04CgtAllotPlanBdConfirmAdjQty;

    /**
     * 卷烟调拨计划计划科确认调整量
     */
    @Field(value = "卷烟调拨计划计划科确认调整量", name = "卷烟调拨计划计划科确认调整量")
    private BigDecimal mc04CgtAllotPlanPdConfirmAdjQty;

    /**
     * 卷烟调拨计划调整后计划量
     */
    @Field(value = "卷烟调拨计划调整后计划量", name = "卷烟调拨计划调整后计划量")
    private BigDecimal ma02CgtPlAdjustedQty;

    /**
     * 卷烟商业期末库存量（万支）
     */
    @TableField(value = "md03_cgt_10th_com_end_stk_qty")
    @Field(value = "卷烟商业期末库存量（万支）", name = "卷烟商业期末库存量（万支）")
    private BigDecimal md03Cgt10thComEndStkQty;

    /**
     * 备注
     */
    @Field(value = "备注", name = "备注")
    private String zaRemark;

    /**
     * 备注
     */
    @Field(value = "备注", name = "备注")
    private String mf04ApprovalInfo;

}
