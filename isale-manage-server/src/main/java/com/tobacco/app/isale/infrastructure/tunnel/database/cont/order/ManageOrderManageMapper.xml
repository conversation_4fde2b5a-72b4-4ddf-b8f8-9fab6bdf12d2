<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.cont.order.ManageOrderManageMapper">


    <resultMap id="ContToThirdMap"
               type="com.tobacco.app.isale.domain.model.cont.order.UploadCont">
        <id property="itfpk" column="itfpk" jdbcType="VARCHAR"/>
        <result property="protocolno" column="protocolno" jdbcType="VARCHAR"/>
        <result property="supmemberCode" column="supmember_code" jdbcType="VARCHAR"/>
        <result property="supmemberName" column="supmember_name" jdbcType="VARCHAR"/>
        <result property="reqmemberCode" column="reqmember_code" jdbcType="VARCHAR"/>
        <result property="reqmemberName" column="reqmember_name" jdbcType="VARCHAR"/>
        <result property="reqregion" column="region_name" jdbcType="VARCHAR"/>
        <result property="supdeputyName" column="supdeputy_name" jdbcType="VARCHAR"/>
        <result property="cycleType" column="cycle_type" jdbcType="VARCHAR"/>
        <result property="busiType" column="busi_type" jdbcType="VARCHAR"/>
        <result property="recwarehouseCode" column="recwarehouse_code" jdbcType="VARCHAR"/>
        <result property="deliwarehouseCode" column="deliwarehouse_code" jdbcType="VARCHAR"/>
        <result property="deliaddress" column="deliaddress" jdbcType="VARCHAR"/>
        <result property="recaddress" column="recaddress" jdbcType="VARCHAR"/>
        <result property="reqbankacct" column="reqbankacct" jdbcType="VARCHAR"/>
        <result property="reqtaxno" column="reqtaxno" jdbcType="VARCHAR"/>
        <result property="reqbankdocname" column="reqbankdocname" jdbcType="VARCHAR"/>
        <result property="supbankacct" column="supbankacct" jdbcType="VARCHAR"/>
        <result property="supbankdocname" column="supbankdocname" jdbcType="VARCHAR"/>
        <result property="suptaxno" column="suptaxno" jdbcType="VARCHAR"/>
        <result property="settltType" column="settlt_type" jdbcType="VARCHAR"/>
        <result property="transType" column="trans_type" jdbcType="VARCHAR"/>
        <result property="sourceCode" column="source_code" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <collection property="detail"
                    ofType="com.tobacco.app.isale.domain.model.cont.order.UploadContItem">
            <result property="itfrowPk" column="itfrow_pk" jdbcType="VARCHAR"/>
            <result property="itfpk" column="detail_itfpk" jdbcType="VARCHAR"/>
            <result property="pkTradecigarette" column="pk_tradecigarette" jdbcType="VARCHAR"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="qty" column="qty" jdbcType="DECIMAL"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="supmemberCode" column="detail_supmember_code" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <select id="getThirdUploadContList" resultMap="ContToThirdMap">
        select a.mc04_cont_order_code                               as itfpk,
               a.md02_cgt_xy_no                                     as protocolno,
               a.icom_code                                          as supmember_code,
               icom.member_name                                     as supmember_name,
               a.ba_com_org_code                                    as reqmember_code,
               com.member_name                                      as reqmember_name,
               com.region_name                                      as reqmember_name,
               k.employeename                                       as supdeputy_name,
               a.mc04_cgt_xy_period_code                            as cycle_type,
               a.ma02_toba_prod_trade_type_code                     as busi_type,
               h.mc05_logt_transport_del_ind_warehouse_code         as recwarehouse_code,
               i.mc05_logt_transport_del_ind_warehouse_code         as deliwarehouse_code,
               i.md02_cgt_out_storehouse_adress                     as deliaddress,
               h.md02_cgt_in_storehouse_adress                      as recaddress,
               g.reqmember_bank_no                                  as reqbankacct,
               j.reqmember_tax_no                                   as reqtaxno,
               g.com_bank_name                                      as reqbankdocname,
               icom_bank.bank_account                               as supbankacct,
               icom_bank.bankdoc_name                               as supbankdocname,
               icom_tax.taxpayer_no                                 as suptaxno,
               a.zb_settlement_mode_code                            as settlt_type,
               a.md02_trade_trans_type_code                         as trans_type,
               dist.pk                                              as source_code,
               a.md02_cgt_trade_cont_remark                         as remark,
               concat(a.mc04_cont_order_code, d.ac_cgt_carton_code) as itfrow_pk,
               a.mc04_cont_order_code                               as detail_itfpk,
               e.pk_tradecigarette,
               d.ac_cgt_carton_code                                 as product_code,
               e.cigarette_name                                     as product_name,
               d.md02_cgt_trade_cont_qty                            as qty,
               d.ac_cgt_no_tax_allot_price                          as price,
               a.icom_code                                          as detail_supmember_code
        from mc04_islm_cont_order as a
                 left join mc04_islm_cont_order_item as d on a.mc04_cont_order_id = d.mc04_cont_order_id
                 left join nation_dist as dist on a.mc04_cgt_dist_order_code = dist.itfpk
                 left join nation_product_control as e on d.ac_cgt_carton_code = e.cigarette_code
            and e.org_code = #{icomCode} and left(a.mc04_cgt_xy_period_code, 4) = e.year
                 left join (select member_code,
                                   max(member_name) as member_name
                            from nation_member
                            group by member_code) as icom on a.icom_code = icom.member_code
                 left join (select member_code,
                                   max(member_name) as member_name,
                                   max(region_name) as region_name
                            from nation_member
                            group by member_code) as com on a.ba_com_org_code = com.member_code
                 left join mc04_islmc_cont_reach_whse as h
                           on a.md02_cgt_in_storehouse_code = h.md02_cgt_in_storehouse_code
                               and h.is_use = '1'
                 left join mc04_islmc_cont_deliv_whse as i
                           on a.md02_cgt_out_storehouse_code = i.md02_cgt_out_storehouse_code
                               and i.is_use = '1'

                 left join (select member_code,
                                   max(bank_account) as reqmember_bank_no,
                                   max(bankdoc_name) as com_bank_name
                            from nation_bank
                            where is_default = 'Y'
                            group by member_code) as g on g.member_code = a.ba_com_org_code
                 left join (select member_code,
                                   max(taxpayer_no) as reqmember_tax_no
                            from nation_tax
                            where is_default = 'Y'
                            group by member_code) as j on j.member_code = a.ba_com_org_code

                 left join (select member_code,
                                   max(bank_account) as bank_account,
                                   max(bankdoc_name) as bankdoc_name
                            from nation_bank
                            where is_default = 'Y'
                            group by member_code) as icom_bank on icom_bank.member_code = a.icom_code
                 left join (select member_code,
                                   max(taxpayer_no) as taxpayer_no
                            from nation_tax
                            where is_default = 'Y'
                            group by member_code) as icom_tax on icom_tax.member_code = a.icom_code
                 left join (select member_code,
                                   max(employeename) as employeename
                            from nation_employee
                            where is_default = 'Y'
                            group by member_code) as k on k.member_code = a.icom_code
        where a.mc04_cont_order_id in (
        <foreach collection="contIdList" separator="," item="contId">
            #{contId}
        </foreach>
        )
        order by a.mc04_cont_order_code
    </select>

    <update id="updateAgreeNo">
        update mc04_islm_cont_order as cont, nation_agreement as agreement
        set cont.mc04_cgt_agree_no = agreement.protocolno
        where cont.mc04_date_period_code = agreement.cycle_code
            and cont.ba_com_org_code = agreement.reqmember_code
            and agreement.audit_status = '2'
            and cont.mc04_cont_order_id in (
        <foreach collection="contOrderIdList" separator="," item="contOrderId">
            #{contOrderId}
        </foreach>
        )
    </update>
</mapper>