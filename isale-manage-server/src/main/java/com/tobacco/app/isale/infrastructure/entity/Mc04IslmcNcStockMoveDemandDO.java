/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 备货移库需求
 *
 * @Author: hujiarong
 * @Since: 2025-08-27
 * @Email: <EMAIL>
 * @Create: 2025-08-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_nc_stock_move_demand")
public class Mc04IslmcNcStockMoveDemandDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 备货移库需求编码
             */

            @TableId(value = "mc04_nc_stock_move_demand_id", type = IdType.NONE)
    @Field(value = "备货移库需求编码", name = "备货移库需求编码")
    private String mc04NcStockMoveDemandId;
                                /**
             * 烟草制品交易业务类型代码
             */

    @Field(value = "烟草制品交易业务类型代码", name = "烟草制品交易业务类型代码")
    private String ma02TobaProdTradeTypeCode;
                                /**
             * 批次编号
             */

    @Field(value = "批次编号", name = "批次编号")
    private String mc04BatchNo;
                                /**
             * 业务年份
             */

    @Field(value = "业务年份", name = "业务年份")
    private String zaOccurrenceYear;
                                /**
             * 商业公司编码
             */

    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;
                                /**
             * 备货移库需求状态
             */

    @Field(value = "备货移库需求状态", name = "备货移库需求状态")
    private String mc04NcStockMoveDemandStatus;
                                /**
             * 备注
             */

    @Field(value = "备注", name = "备注")
    private String remark;
                                                                                                    

}
