<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmDemandFoMapper">
    <select id="getDemandFoConfirmQty" resultType="com.tobacco.app.isale.domain.model.plan.PlanQty">
        SELECT
            ac_cgt_carton_code productCode,
            sum( mc04_demand_fo_confirm_qty ) demandFoConfirmQty
        FROM
            mc04_islm_demand_fo_item
        WHERE
            mc04_demand_fo_id IN ( SELECT mc04_demand_fo_id FROM mc04_islm_demand_fo WHERE mc04_com_org_level = '01'
                                AND mc04_com_org_is_imported = #{orgIsImported} AND za_occurrence_year = #{year} )
          AND mc04_cgt_sale_fo_period_type = 'T04'
          AND mc04_cgt_sale_fo_period_code = #{month}
        GROUP BY
            ac_cgt_carton_code
    </select>
    <select id="allSalesByMonthAndBamCode" resultType="java.util.Map">
        SELECT
            ac_cgt_carton_code as cartonCode,
            md03_cgt_10th_com_coe_sale_qty_m_a as sale
        FROM
            induniondb.mc04_ind_xy_org_cgt_purch_sale_stk_month
        WHERE
          mc04_purchase_sale_stk_month = #{month}
          AND mc04_xy_org_code = #{bamCode}
        GROUP BY
            ac_cgt_carton_code
    </select>
    <select id="selectCartonCodeAndAdjustedQtyByYearAndSubjectType" resultType="java.util.Map">
        SELECT item.ac_cgt_carton_code as cartonCode,
               item.mc04_cgt_sale_plan_adjusted_qty as adjustedQty
        FROM mc04_islm_sale_plan plan
                 JOIN mc04_islm_sale_plan_item item
                      ON plan.mc04_sale_plan_id = item.mc04_sale_plan_id
        WHERE plan.za_occurrence_year = #{zaOccurrenceYear}
          AND plan.mc04_org_type_kind = '02'
          AND plan.mc04_cgt_sale_fo_period_type = 'T01'
          AND plan.mc04_plan_subject_type = #{planSubjectType}
        ORDER BY plan.create_time DESC
    </select>


</mapper>