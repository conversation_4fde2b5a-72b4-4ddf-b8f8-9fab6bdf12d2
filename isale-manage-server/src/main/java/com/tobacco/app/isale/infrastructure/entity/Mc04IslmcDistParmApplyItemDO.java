/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 配货参数申请明细表
 * @Author: liuwancheng
 * @Since: 2025-05-28
 * @Email: <EMAIL>
 * @Create: 2025-05-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_dist_parm_apply_item")
@DataObject(name = "配货参数申请明细表", desc = "配货参数申请明细表")
public class Mc04IslmcDistParmApplyItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
    /**
     * 配货参数申请明细编号
     */

    @TableId(value = "mc04_cgt_dist_parm_apply_cgt_id", type = IdType.NONE)
    @Field(value = "配货参数申请明细编号", name = "配货参数申请明细编号")
    private String mc04CgtDistParmApplyCgtId;
    /**
     * 配货参数申请编号
     */

    @Field(value = "配货参数申请编号", name = "配货参数申请编号")
    private String mc04CgtDistParmApplyId;
    /**
     * 卷烟代码（条）
     */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
    /**
     * 卷烟名称
     */

    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;
    /**
     * 网配订单确认配货模式
     */

    @Field(value = "网配订单确认配货模式", name = "网配订单确认配货模式")
    private String md02CgtDistDistModel;
    /**
     * 网配订单计算方式代码
     */

    @Field(value = "网配订单计算方式代码", name = "网配订单计算方式代码")
    private String md02CgtDistCalcModelCode;
    /**
     * 网配订单日均销量滚动天数
     */

    @Field(value = "网配订单日均销量滚动天数", name = "网配订单日均销量滚动天数")
    private BigDecimal md02CgtDistDailySaleDays;
    /**
     * 网配订单日均销量计算方式代码
     */

    @Field(value = "网配订单日均销量计算方式代码", name = "网配订单日均销量计算方式代码")
    private String md02CgtDistDayilySaleModelCode;
    /**
     * 存销比上限(天)
     */

    @Field(value = "存销比上限(天)", name = "存销比上限(天)")
    private BigDecimal md02CgtUpperLimitStocksaleMax;
    /**
     * 存销比下限(天)
     */

    @Field(value = "存销比下限(天)", name = "存销比下限(天)")
    private BigDecimal md02CgtLowerLimitStocksaleMax;
    /**
     * 卷烟库存绝对值上限
     */

    @Field(value = "卷烟库存绝对值上限", name = "卷烟库存绝对值上限")
    private BigDecimal md05MgCgtUpperLimitStockQty;
    /**
     * 卷烟库存绝对值下限
     */

    @Field(value = "卷烟库存绝对值下限", name = "卷烟库存绝对值下限")
    private BigDecimal md05MgCgtLowerLimitStockQty;
    /**
     * 烟草制品投放策略配货-商业安全库存
     */

    @Field(value = "烟草制品投放策略配货-商业安全库存", name = "烟草制品投放策略配货-商业安全库存")
    private BigDecimal mc04CgtDistStrategyComSafeStkQty;
    /**
     * 烟草制品实时订单配货-商业安全库存
     */

    @Field(value = "烟草制品实时订单配货-商业安全库存", name = "烟草制品实时订单配货-商业安全库存")
    private BigDecimal md02CgtDistOrderDistSafeStkQty;
    /**
     * 网配订单上周订足率权重
     */

    @Field(value = "网配订单上周订足率权重", name = "网配订单上周订足率权重")
    private BigDecimal md02CgtDistFullOrderRate;
    /**
     * 网配订单上两周订足率权重
     */

    @Field(value = "网配订单上两周订足率权重", name = "网配订单上两周订足率权重")
    private BigDecimal md02CgtDistFullOrderRate2;
    /**
     * 网配订单上三周订足率权重
     */

    @Field(value = "网配订单上三周订足率权重", name = "网配订单上三周订足率权重")
    private BigDecimal md02CgtDistFullOrderRate3;
    /**
     * 网配订单上四周订足率权重
     */

    @Field(value = "网配订单上四周订足率权重", name = "网配订单上四周订足率权重")
    private BigDecimal md02CgtDistFullOrderRate4;
    /**
     * 备注
     */

    @Field(value = "备注", name = "备注")
    private String zaRemark;


}
