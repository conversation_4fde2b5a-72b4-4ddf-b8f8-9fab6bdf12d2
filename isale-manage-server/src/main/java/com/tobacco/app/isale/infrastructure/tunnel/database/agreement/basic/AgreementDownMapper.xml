<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic.AgreementDownMapper">

    <select id="queryAgreementPage" resultType="com.tobacco.app.isale.domain.model.agreement.basic.IslmcXy">
        SELECT * FROM (
        <choose>
            <when test="map.dataTypes == null or map.dataTypes.size() == 0">
               <include refid="queryAgree"></include>
                UNION ALL
               <include refid="queryAgreeAdjust"></include>
            </when>
            <when test="map.dataTypes.contains('10') and map.dataTypes.contains('20')">
                <include refid="queryAgree"></include>
                UNION ALL
                <include refid="queryAgreeAdjust"></include>
            </when>
            <when test="map.dataTypes.contains('10')">
                <include refid="queryAgree"></include>
            </when>
            <when test="map.dataTypes.contains('20')">
                <include refid="queryAgreeAdjust"></include>
            </when>
        </choose>
        ) AS res
        order by res.mc04CgtXyPeriodCode desc
    </select>

    <sql id="queryAgree">
        select
        '协议'  as mc04CgtXyType,
        xy.md02_cgt_xy_no as md02CgtXyNo,
        xy.mc04_cgt_xy_period_code as mc04CgtXyPeriodCode,
        xy.mc04_cgt_xy_status as mc04CgtXyStatus,
        xy.mc04_cgt_xy_download_date as mc04CgtXyDownloadDate,
        n.supmemberCode,
        n.reqmemberName,
        n.reqmemberCode,
        n.supmemberName,
        n.pk as pk,
        n.qty
        from mc04_islmc_xy xy
        left join
        ( select
        count(nai.qty) as qty,
        na.supmember_name as supmemberName,
        na.supmember_code as supmemberCode,
        na.protocolno as protocolno,
        na.reqmember_code as reqmemberCode,
        na.reqmember_name as reqmemberName,
        na.pk as pk
        from nation_agreement na, nation_agreement_item nai
        where na.pk = nai.pk
        <if test="map.comIds != null and map.comIds.size() > 0">
            and na.reqmember_code in
            <foreach item="item" index="index" collection="map.comIds" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by na.protocolno
        ) as n
        on xy.md02_cgt_xy_no = n.protocolno
        where xy.ma02_toba_prod_trade_type_code = #{map.cgtType}
        and xy.mc04_cgt_xy_download_date between #{map.downBeginDate} and #{map.downEndDate}
        <if test="map.dateCode != null">
            and xy.mc04_cgt_xy_period_code = #{map.dateCode}
        </if>

    </sql>
    <sql  id="queryAgreeAdjust">
        select
        '协议调整'  as mc04CgtXyType,
        xy.md02_cgt_xy_no as md02CgtXyNo,
        xy.mc04_cgt_xy_period_code as mc04CgtXyPeriodCode,
        '1' as mc04CgtXyStatus,
        xy.mc04_cgt_xy_download_date as mc04CgtXyDownloadDate,
        n.supmemberCode,
        n.reqmemberName,
        n.reqmemberCode,
        n.supmemberName,
        n.qty,
        n.pk as pk
        from mc04_islmc_xy_adjust xy
        left join
        ( select na.pk,
        count(nai.qty) as qty,
        na.supmember_code as supmemberCode,
        na.supmember_name as supmemberName,
        na.protocolno as protocolno,
        na.reqmember_code as reqmemberCode,
        na.reqmember_name as reqmemberName
        from nation_agreement_adjust na, nation_agreement_adjust_item nai
        where na.pk = nai.pk
        <if test="map.comIds != null and map.comIds.size() > 0">
            and na.reqmember_code in
            <foreach item="item" index="index" collection="map.comIds" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by na.protocolno
        ) as n
        on xy.md02_cgt_xy_no = n.protocolno
        where xy.ma02_toba_prod_trade_type_code = #{map.cgtType}
        and xy.mc04_cgt_xy_download_date between #{map.downBeginDate} and #{map.downEndDate}
        <if test="map.dateCode != null">
            and xy.mc04_cgt_xy_period_code = #{map.dateCode}
        </if>
    </sql>
</mapper>