/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 商业需求预测明细
 *
 * @Author: hujiarong
 * @Since: 2025-09-05
 * @Email: <EMAIL>
 * @Create: 2025-09-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_com_demand_fo_item")
public class Mc04IslmcComDemandFoItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 市场需求预测明细编号
             */

            @TableId(value = "mc04_demand_fo_item_id", type = IdType.NONE)
    @Field(value = "市场需求预测明细编号", name = "市场需求预测明细编号")
    private String mc04DemandFoItemId;
                                /**
             * 市场需求预测数据编号
             */

    @Field(value = "市场需求预测数据编号", name = "市场需求预测数据编号")
    private String mc04DemandFoId;
                                /**
             * 卷烟代码（条）
             */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
                                /**
             * 卷烟名称
             */

    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;
                                /**
             * 指标编码
             */

    @Field(value = "指标编码", name = "指标编码")
    private String mz10IndexCode;
                                /**
             * 需求预测最终量
             */

    @Field(value = "需求预测最终量", name = "需求预测最终量")
    private BigDecimal mc04DemandFoFinalQty;
                                                                                                    

}
