/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @description : 工业需求预测明细
 *
 * <AUTHOR> wanglu<PERSON>
 * @since : 2025-04-22
 * @email : <EMAIL>
 * @create_time : 2025-04-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_ind_demand_fo_item")
@DataObject(name = "工业需求预测明细", desc = "工业需求预测明细")
public class Mc04IslmcIndDemandFoItemDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 市场需求预测明细编号
             */

            @TableId(value = "mc04_demand_fo_item_id", type = IdType.NONE)
    @Field(value = "市场需求预测明细编号", name = "市场需求预测明细编号")
    private String mc04DemandFoItemId;
                                /**
             * 市场需求预测数据编号
             */

    @Field(value = "市场需求预测数据编号", name = "市场需求预测数据编号")
    private String mc04DemandFoId;
                                /**
             * 卷烟代码（条）
             */

    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
                                /**
             * 卷烟名称
             */

    @Field(value = "卷烟名称", name = "卷烟名称")
    private String acCgtName;
                                /**
             * 卷烟二级牌号编码
             */

    @Field(value = "卷烟二级牌号编码", name = "卷烟二级牌号编码")
    private String acTwoLevelCigCode;
                                /**
             * 卷烟二级牌号名称
             */

    @Field(value = "卷烟二级牌号名称", name = "卷烟二级牌号名称")
    private String acTwoLevelCigName;
                                /**
             * 卷烟含税调拨价格
             */

    @Field(value = "卷烟含税调拨价格", name = "卷烟含税调拨价格")
    private BigDecimal acCgtTaxAllotPrice;
                                 /**
             * 卷烟统一批发价
             */

    @Field(value = "卷烟统一批发价", name = "卷烟统一批发价")
    private BigDecimal acCgtTradePrice;
                                /**
             * 指标编码
             */

    @Field(value = "指标编码", name = "指标编码")
    private String mz10IndexCode;
                                /**
             * 销区上报需求预测量
             */

    @Field(value = "销区上报需求预测量", name = "销区上报需求预测量")
    private BigDecimal mc04DemandFoReportQty;
                                /**
             * 区域确认需求预测量
             */

    @Field(value = "区域确认需求预测量", name = "区域确认需求预测量")
    private BigDecimal mc04DemandFoRegionConfirmQty;
                                /**
             * 意向上报总监审核量
             */

    @Field(value = "意向上报总监审核量", name = "意向上报总监审核量")
    private BigDecimal mc04DemandFoDirectorAuditQty;
                                /**
             * 需求预测最终量
             */

    @Field(value = "需求预测最终量", name = "需求预测最终量")
    private BigDecimal mc04DemandFoFinalQty;
                                                                                                    

}
