<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.nation.DistDownloadMapper">

	<delete id="deleteNationDistRegion">
		delete from nation_dist_region where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDistRegion">
		insert into nation_dist_region(
		pk,
		member_code,
		member_name,
		region_code ,
		region_name,
		isseal,
		remark
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.MEMBER_CODE,jdbcType=VARCHAR}
			,#{d.MEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REGION_CODE,jdbcType=VARCHAR}
			,#{d.REGION_NAME,jdbcType=VARCHAR}
			,#{d.ISSEAL,jdbcType=VARCHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationDistReceiveRegion">
		delete from nation_dist_receive_region where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDistReceiveRegion">
		insert into nation_dist_receive_region
		(
			pk,
			ma02_cgt_trade_req_memb_code,
			md02_cgt_trade_req_memb_name,
			md02_dist_receiveregion_code,
			md02_dist_receiveregion_name,
			isseal,
			remark
		)
		values
		<foreach collection="datas" item="d" separator=",">
			(
			#{d.PK}
			,#{d.MA02_CGT_TRADE_REQ_MEMB_CODE,jdbcType=VARCHAR}
			,#{d.MD02_CGT_TRADE_REQ_MEMB_NAME,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_CODE,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_NAME,jdbcType=VARCHAR}
			,#{d.ISSEAL,jdbcType=VARCHAR}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationDistPreview">
		delete from nation_dist_preview where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDistPreview">
		insert into nation_dist_preview(
		pk
		,bill_date
		,supmember_code
		,supmember_name
		,reqmember_code
		,reqmember_name
		,distregion_code
		,distregion_name
		,md02_dist_receiveregion_code
		,md02_dist_receiveregion_name
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.PK}
			,#{d.BILL_DATE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.DISTREGION_CODE,jdbcType=VARCHAR}
			,#{d.DISTREGION_NAME,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_CODE,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_NAME,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationDistPreviewItem">
		delete from nation_dist_preview_item where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDistPreviewItem">
		insert into nation_dist_preview_item(
		pk
		,pk_tradeproduct
		,product_code
		,product_name
		,prep_days
		,onway_days
		,dailysale_days
		,dayilysale_model
		,dist_model
		,calc_model
		,maxstocksale_days
		,minstocksale_days
		,maxstocknum
		,minstocknum
		,suplsafestocknum
		,fullorderrate1
		,fullorderrate2
		,fullorderrate3
		,fullorderrate4
		,trans_w_price
		,trans_price
		,stocksale_days
		,stocknum
		,onwaynum
		,predordernum
		,dailysalenum
		,ordersafestocknum
		,calcdistnum
		,md02_cgt_dist_season_factor
		,md01_cgt_market_status
		,md02_cgt_dist_qty_rate_max
		,md02_cgt_dist_qty_rate_min
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.PK,jdbcType=VARCHAR}
			,#{d.PK_TRADEPRODUCT,jdbcType=VARCHAR}
			,#{d.PRODUCT_CODE,jdbcType=VARCHAR}
			,#{d.PRODUCT_NAME,jdbcType=VARCHAR}
			,#{d.PREP_DAYS,jdbcType=DECIMAL}
			,#{d.ONWAY_DAYS,jdbcType=DECIMAL}
			,#{d.DAILYSALE_DAYS,jdbcType=DECIMAL}
			,#{d.DAYILYSALE_MODEL,jdbcType=INTEGER}
			,#{d.DIST_MODEL,jdbcType=INTEGER}
			,#{d.CALC_MODEL,jdbcType=INTEGER}
			,#{d.MAXSTOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.MINSTOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.MAXSTOCKNUM,jdbcType=DECIMAL}
			,#{d.MINSTOCKNUM,jdbcType=DECIMAL}
			,#{d.SUPLSAFESTOCKNUM,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE1,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE2,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE3,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE4,jdbcType=DECIMAL}
			,#{d.TRANS_W_PRICE,jdbcType=DECIMAL}
			,#{d.TRANS_PRICE,jdbcType=DECIMAL}
			,#{d.STOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.STOCKNUM,jdbcType=DECIMAL}
			,#{d.ONWAYNUM,jdbcType=DECIMAL}
			,#{d.PREDORDERNUM,jdbcType=DECIMAL}
			,#{d.DAILYSALENUM,jdbcType=DECIMAL}
			,#{d.ORDERSAFESTOCKNUM,jdbcType=DECIMAL}
			,#{d.CALCDISTNUM,jdbcType=DECIMAL}
			,#{d.MD02_CGT_DIST_SEASON_FACTOR,jdbcType=DECIMAL}
			,#{d.MD01_CGT_MARKET_STATUS,jdbcType=VARCHAR}
			,#{d.MD02_CGT_DIST_QTY_RATE_MAX,jdbcType=DECIMAL}
			,#{d.MD02_CGT_DIST_QTY_RATE_MIN,jdbcType=DECIMAL}
			)
		</foreach>
	</insert>

	<delete id="deleteNationParm" >
		delete from nation_parm where
		supmember_code = #{supmemberCode}
		<if test="reqmemberCode !=null and reqmemberCode !=''">
            and reqmember_code = #{reqmemberCode}
        </if>
		<if test="pkList !=null and pkList.size() > 0">
			and pk in
			<foreach item="item" collection="pkList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</delete>

	<delete id="deleteNationParmItem" >
		delete from nation_parm_item where pk in (
		select pk from nation_parm where
		supmember_code = #{supmemberCode}
		<if test="reqmemberCode !=null and reqmemberCode !=''">
                and reqmember_code = #{reqmemberCode}
            </if>
		<if test="pkList !=null and pkList.size() > 0">
			and pk in
			<foreach item="item" collection="pkList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		)
	</delete>

	<insert id="insertNationParm" >
		insert into nation_parm(
		pk
		,itfpk
		,version
		,supmember_code
		,supmember_name
		,reqmember_code
		,reqmember_name
		,distregion_code
		,distregion_name
		,md02_dist_receiveregion_code
		,md02_dist_receiveregion_name
		,prep_days
		,onway_days
		,dailysale_days
		,dayilysale_model
		,req_confirm_status
		,req_confirm_time
		,sup_confirm_status
		,sup_confirm_time
		,effect_status
		,remark
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.PK}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.VERSION,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.DISTREGION_CODE,jdbcType=VARCHAR}
			,#{d.DISTREGION_NAME,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_CODE,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_NAME,jdbcType=VARCHAR}
			,#{d.PREP_DAYS,jdbcType=DECIMAL}
			,#{d.ONWAY_DAYS,jdbcType=DECIMAL}
			,#{d.DAILYSALE_DAYS,jdbcType=DECIMAL}
			,#{d.DAYILYSALE_MODEL,jdbcType=INTEGER}
			,#{d.REQ_CONFIRM_STATUS,jdbcType=INTEGER}
			,#{d.REQ_CONFIRM_TIME,jdbcType=CHAR}
			,#{d.SUP_CONFIRM_STATUS,jdbcType=INTEGER}
			,#{d.SUP_CONFIRM_TIME,jdbcType=CHAR}
			,#{d.EFFECT_STATUS,jdbcType=INTEGER}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<insert id="insertNationParmItem" >
		insert into nation_parm_item(
		row_pk
		,pk
		,itfpk
		,itfrow_pk
		,pk_tradeproduct
		,product_code
		,product_name
		,dist_model
		,calc_model
		,dailysale_days
		,dayilysale_model
		,maxstocksale_days
		,minstocksale_days
		,maxstocknum
		,minstocknum
		,suplsafestocknum
		,fullorderrate1
		,fullorderrate2
		,fullorderrate3
		,fullorderrate4
		,ordersafestocknum
		,remark
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.ROW_PK}
			,#{d.PK,jdbcType=VARCHAR}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.ITFROW_PK,jdbcType=VARCHAR}
			,#{d.PK_TRADEPRODUCT,jdbcType=VARCHAR}
			,#{d.PRODUCT_CODE,jdbcType=VARCHAR}
			,#{d.PRODUCT_NAME,jdbcType=VARCHAR}
			,#{d.DIST_MODEL,jdbcType=INTEGER}
			,#{d.CALC_MODEL,jdbcType=INTEGER}
			,#{d.DAILYSALE_DAYS,jdbcType=DECIMAL}
			,#{d.DAYILYSALE_MODEL,jdbcType=INTEGER}
			,#{d.MAXSTOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.MINSTOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.MAXSTOCKNUM,jdbcType=DECIMAL}
			,#{d.MINSTOCKNUM,jdbcType=DECIMAL}
			,#{d.SUPLSAFESTOCKNUM,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE1,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE2,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE3,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE4,jdbcType=DECIMAL}
			,#{d.ORDERSAFESTOCKNUM,jdbcType=DECIMAL}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationParmSeason" >
		delete from nation_parm_season where row_pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.ROW_PK}
		</foreach>
	</delete>

	<insert id="insertNationParmSeason" >
		insert into nation_parm_season(
		row_pk
		,pk
		,itfpk
		,itfrow_pk
		,md02_ic_trade_busi_start_date
		,md02_ic_trade_busi_end_date
		,md02_cgt_dist_season_factor
		,remark
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.ROW_PK}
			,#{d.PK,jdbcType=VARCHAR}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.ITFROW_PK,jdbcType=VARCHAR}
			,#{d.MD02_IC_TRADE_BUSI_START_DATE,jdbcType=VARCHAR}
			,#{d.MD02_IC_TRADE_BUSI_END_DATE,jdbcType=VARCHAR}
			,#{d.MD02_CGT_DIST_SEASON_FACTOR,jdbcType=DECIMAL}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationDist">
		delete from nation_dist where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDist">
		insert into nation_dist(
		pk
		,itfpk
		,vbillno
		,version
		,supmember_code
		,supmember_name
		,reqmember_code
		,reqmember_name
		,distregion_code
		,distregion_name
		,md02_dist_receiveregion_code
		,md02_dist_receiveregion_name
		,sup_confirm_status
		,sup_confirm_time
		,req_confirm_status
		,req_confirm_time
		,effect_status
		,remark
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.PK}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.VBILLNO,jdbcType=VARCHAR}
			,#{d.VERSION,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.SUPMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.REQMEMBER_CODE,jdbcType=VARCHAR}
			,#{d.REQMEMBER_NAME,jdbcType=VARCHAR}
			,#{d.DISTREGION_CODE,jdbcType=VARCHAR}
			,#{d.DISTREGION_NAME,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_CODE,jdbcType=VARCHAR}
			,#{d.MD02_DIST_RECEIVEREGION_NAME,jdbcType=VARCHAR}
			,#{d.SUP_CONFIRM_STATUS,jdbcType=INTEGER}
			,#{d.SUP_CONFIRM_TIME,jdbcType=CHAR}
			,#{d.REQ_CONFIRM_STATUS,jdbcType=INTEGER}
			,#{d.REQ_CONFIRM_TIME,jdbcType=CHAR}
			,#{d.EFFECT_STATUS,jdbcType=INTEGER}
			,#{d.REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<delete id="deleteNationDistItem">
		delete from nation_dist_item where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</delete>

	<insert id="insertNationDistItem">
		insert into nation_dist_item(
		row_pk
		,pk
		,itfpk
		,itfrow_pk
		,pk_tradeproduct
		,product_code
		,product_name
		,prep_days
		,onway_days
		,dailysale_days
		,dayilysale_model
		,dist_model
		,calc_model
		,maxstocksale_days
		,minstocksale_days
		,maxstocknum
		,minstocknum
		,suplsafestocknum
		,fullorderrate1
		,fullorderrate2
		,fullorderrate3
		,fullorderrate4
		,trans_w_price
		,trans_price
		,stocksale_days
		,stocknum
		,onwaynum
		,predordernum
		,dailysalenum
		,ordersafestocknum
		,calcdistnum
		,supcomfirmnum
		,reqconfirmnum
		,confirmnum
		,remark
		,md02_cgt_dist_season_factor
		,md01_cgt_market_status
		,md02_cgt_dist_qty_rate_max
		,md02_cgt_dist_qty_rate_min
		,md02_cgt_dist_indu_abnormal_remark
		,md02_cgt_dist_busi_abnormal_remark
		)
		values
		<foreach collection="datas" item="d"  separator=",">
			(
			#{d.ROW_PK}
			,#{d.PK,jdbcType=VARCHAR}
			,#{d.ITFPK,jdbcType=VARCHAR}
			,#{d.ITFROW_PK,jdbcType=VARCHAR}
			,#{d.PK_TRADEPRODUCT,jdbcType=VARCHAR}
			,#{d.PRODUCT_CODE,jdbcType=VARCHAR}
			,#{d.PRODUCT_NAME,jdbcType=VARCHAR}
			,#{d.PREP_DAYS,jdbcType=DECIMAL}
			,#{d.ONWAY_DAYS,jdbcType=DECIMAL}
			,#{d.DAILYSALE_DAYS,jdbcType=DECIMAL}
			,#{d.DAYILYSALE_MODEL,jdbcType=INTEGER}
			,#{d.DIST_MODEL,jdbcType=INTEGER}
			,#{d.CALC_MODEL,jdbcType=INTEGER}
			,#{d.MAXSTOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.MINSTOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.MAXSTOCKNUM,jdbcType=DECIMAL}
			,#{d.MINSTOCKNUM,jdbcType=DECIMAL}
			,#{d.SUPLSAFESTOCKNUM,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE1,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE2,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE3,jdbcType=DECIMAL}
			,#{d.FULLORDERRATE4,jdbcType=DECIMAL}
			,#{d.TRANS_W_PRICE,jdbcType=DECIMAL}
			,#{d.TRANS_PRICE,jdbcType=DECIMAL}
			,#{d.STOCKSALE_DAYS,jdbcType=DECIMAL}
			,#{d.STOCKNUM,jdbcType=DECIMAL}
			,#{d.ONWAYNUM,jdbcType=DECIMAL}
			,#{d.PREDORDERNUM,jdbcType=DECIMAL}
			,#{d.DAILYSALENUM,jdbcType=DECIMAL}
			,#{d.ORDERSAFESTOCKNUM,jdbcType=DECIMAL}
			,#{d.CALCDISTNUM,jdbcType=DECIMAL}
			,#{d.SUPCOMFIRMNUM,jdbcType=DECIMAL}
			,#{d.REQCONFIRMNUM,jdbcType=DECIMAL}
			,#{d.CONFIRMNUM,jdbcType=DECIMAL}
			,#{d.REMARK,jdbcType=VARCHAR}
			,#{d.MD02_CGT_DIST_SEASON_FACTOR,jdbcType=DECIMAL}
			,#{d.MD01_CGT_MARKET_STATUS,jdbcType=VARCHAR}
			,#{d.MD02_CGT_DIST_QTY_RATE_MAX,jdbcType=DECIMAL}
			,#{d.MD02_CGT_DIST_QTY_RATE_MIN,jdbcType=DECIMAL}
			,#{d.MD02_CGT_DIST_INDU_ABNORMAL_REMARK,jdbcType=VARCHAR}
			,#{d.MD02_CGT_DIST_BUSI_ABNORMAL_REMARK,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<select id="getIsmDistParms" resultType="string">
		select mc04_cgt_dist_parm_id from mc04_islmc_dist_parm where (ba_com_org_code,coalesce(md02_cgt_dist_region_code,''),coalesce(md02_dist_receiveregion_code,'')) in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			(#{d.REQMEMBER_CODE}, coalesce(#{d.DISTREGION_CODE},''), coalesce(#{d.MD02_DIST_RECEIVEREGION_CODE},''))
		</foreach>
	</select>

	<delete id="deleteIsmDistParm" >
		delete from mc04_islmc_dist_parm where mc04_cgt_dist_parm_id in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</delete>

	<insert id="insertIsmDistParmFromNation" >
		insert into mc04_islmc_dist_parm
		(
		mc04_cgt_dist_parm_id,
		ba_com_org_code,
		ma02_cgt_trade_supp_memb_code,
		md02_cgt_trade_supp_memb_name,
		ma02_cgt_trade_req_memb_code,
		md02_cgt_trade_req_memb_name,
		md02_cgt_dist_region_code,
		md02_cgt_dist_region_name,
		md02_dist_receiveregion_code,
		md02_dist_receiveregion_name,
		md02_cgt_dist_prep_days,
		md02_cgt_dist_on_way_days,
		md02_cgt_dist_daily_sale_days,
		md02_cgt_dist_dayily_sale_model_code,
		mc04_cgt_dist_parm_effect_status,
		za_remark
		)
		select
		pk mc04_cgt_dist_parm_id,
		reqmember_code com_code,
		supmember_code,
		supmember_name,
		reqmember_code,
		reqmember_name,
		coalesce(distregion_code,'') as distregion_code,
		distregion_name,
		coalesce(md02_dist_receiveregion_code,'') as md02_dist_receiveregion_code,
		md02_dist_receiveregion_name,
		prep_days,
		onway_days,
		dailysale_days,
		dayilysale_model,
		effect_status,
		remark
		from
		nation_parm
		where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<select id="getIsmDistParmFromNation" resultType="com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ">
		select
		pk mc04_cgt_dist_parm_id,
		reqmember_code ba_com_org_code,
		supmember_code ma02_cgt_trade_supp_memb_code,
		supmember_name md02_cgt_trade_supp_memb_name,
		reqmember_code ma02_cgt_trade_req_memb_code,
		reqmember_name md02_cgt_trade_req_memb_name,
		coalesce(distregion_code,'') as md02_cgt_dist_region_code,
		distregion_name md02_cgt_dist_region_name,
		coalesce(md02_dist_receiveregion_code,'') as md02_dist_receiveregion_code,
		md02_dist_receiveregion_name,
		prep_days md02_cgt_dist_prep_days,
		onway_days md02_cgt_dist_on_way_days,
		dailysale_days md02_cgt_dist_daily_sale_days,
		dayilysale_model md02_cgt_dist_dayily_sale_model_code,
		effect_status mc04_cgt_dist_parm_effect_status,
		remark za_remark
		from
		nation_parm
		where pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<delete id="deleteIsmDistParmItem" >
		delete from mc04_islmc_dist_parm_item where mc04_cgt_dist_parm_id in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</delete>

	<insert id="insertIsmDistParmItemFromNation" >
		insert into mc04_islmc_dist_parm_item (
		mc04_cgt_dist_parm_item_id,
		mc04_cgt_dist_parm_id,
		ac_cgt_carton_code,
		ac_cgt_name,
		md02_cgt_dist_dist_model,
		md02_cgt_dist_calc_model_code,
		md02_cgt_dist_daily_sale_days,
		md02_cgt_dist_dayily_sale_model_code,
		md02_cgt_upper_limit_stocksale_max,
		md02_cgt_lower_limit_stocksale_max,
		md05_mg_cgt_upper_limit_stock_qty,
		md05_mg_cgt_lower_limit_stock_qty,
		mc04_cgt_dist_strategy_com_safe_stk_qty ,
		md02_cgt_dist_full_order_rate,
		md02_cgt_dist_full_order_rate2,
		md02_cgt_dist_full_order_rate3,
		md02_cgt_dist_full_order_rate4,
		md02_cgt_dist_order_dist_safe_stk_qty,
		za_remark
		)
		select
		b.row_pk mc04_cgt_dist_parm_item_id,
		b.pk mc04_cgt_dist_parm_id,
		b.product_code,
		b.product_name,
		b.dist_model,
		b.calc_model,
		b.dailysale_days,
		b.dayilysale_model,
		b.maxstocksale_days,
		b.minstocksale_days,
		b.maxstocknum,
		b.minstocknum,
		b.suplsafestocknum,
		b.fullorderrate1,
		b.fullorderrate2,
		b.fullorderrate3,
		b.fullorderrate4,
		b.ordersafestocknum,
		b.remark
		from
		nation_parm_item b
		where
		b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<select id="getIsmDistParmItemFromNation" resultType="com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmItemREQ">
		select
		b.row_pk mc04_cgt_dist_parm_item_id,
		b.pk mc04_cgt_dist_parm_id,
		b.product_code ac_cgt_carton_code,
		b.product_name ac_cgt_name,
		b.dist_model md02_cgt_dist_dist_model,
		b.calc_model md02_cgt_dist_calc_model_code,
		b.dailysale_days md02_cgt_dist_daily_sale_days,
		b.dayilysale_model md02_cgt_dist_dayily_sale_model_code,
		b.maxstocksale_days md02_cgt_upper_limit_stocksale_max,
		b.minstocksale_days md02_cgt_lower_limit_stocksale_max,
		b.maxstocknum md05_mg_cgt_upper_limit_stock_qty,
		b.minstocknum md05_mg_cgt_lower_limit_stock_qty,
		b.suplsafestocknum mc04_cgt_dist_strategy_com_safe_stk_qty,
		b.fullorderrate1 md02_cgt_dist_full_order_rate,
		b.fullorderrate2 md02_cgt_dist_full_order_rate2,
		b.fullorderrate3 md02_cgt_dist_full_order_rate3,
		b.fullorderrate4 md02_cgt_dist_full_order_rate4,
		b.ordersafestocknum md02_cgt_dist_order_dist_safe_stk_qty,
		b.remark za_remark
		from
		nation_parm_item b
		where
		b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<delete id="deleteIsmDistParmSeason" >
		delete from mc04_islmc_dist_parm_season where mc04_cgt_dist_parm_id in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</delete>

	<insert id="insertIsmDistParmSeasonFromNation" >
		insert into mc04_islmc_dist_parm_season (
		mc04_cgt_dist_parm_season_id,
		mc04_cgt_dist_parm_id,
		md02_ic_trade_busi_start_date,
		md02_ic_trade_busi_end_date,
		md02_cgt_dist_season_factor,
		za_remark
		)
		select
		b.row_pk mc04_cgt_dist_parm_season_id,
		b.pk mc04_cgt_dist_parm_id,
		replace(b.md02_ic_trade_busi_start_date,'-','') as md02_ic_trade_busi_start_date,
		replace(b.md02_ic_trade_busi_end_date,'-','') as md02_ic_trade_busi_end_date,
		b.md02_cgt_dist_season_factor,
		b.remark
		from
		nation_parm_season b
		where
		b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</insert>

	<select id="getIsmDistParmSeasonFromNation" resultType="com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmSeasonREQ">
		select
		b.row_pk mc04_cgt_dist_parm_season_id,
		b.pk mc04_cgt_dist_parm_id,
		replace(b.md02_ic_trade_busi_start_date,'-','') as md02_ic_trade_busi_start_date,
		replace(b.md02_ic_trade_busi_end_date,'-','') as md02_ic_trade_busi_end_date,
		b.md02_cgt_dist_season_factor,
		b.remark za_remark
		from
		nation_parm_season b
		where
		b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<select id="getConfirmIsmDistParmApplys" resultType="string">
		select
		mc04_cgt_dist_parm_apply_code
		from
		mc04_islmc_dist_parm_apply a,
		nation_parm b
		where
		b.reqmember_code = a.ma02_cgt_trade_req_memb_code
		and coalesce(b.distregion_code,'') = coalesce(a.md02_cgt_dist_region_code,'')
		and coalesce(b.md02_dist_receiveregion_code,'') = coalesce(a.md02_dist_receiveregion_code,'')
		and a.mc04_cgt_dist_parm_apply_status = '50'
		and b.effect_status = '1'
		and b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<select id="getRejectIsmDistParmApplys" resultType="string">
		select
		mc04_cgt_dist_parm_apply_code
		from
		mc04_islmc_dist_parm_apply a,
		nation_parm b
		where
		b.reqmember_code = a.ma02_cgt_trade_req_memb_code
		and coalesce(b.distregion_code,'') = coalesce(a.md02_cgt_dist_region_code,'')
		and coalesce(b.md02_dist_receiveregion_code,'') = coalesce(a.md02_dist_receiveregion_code,'')
		and a.mc04_cgt_dist_parm_apply_status = '50'
		and b.effect_status = '0'
		and b.req_confirm_status = '1'
		and b.sup_confirm_status = '0'
		and b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<update id="updateIsmDistParmApply">
		update
		mc04_islmc_dist_parm_apply
		set
		mc04_cgt_dist_parm_apply_status = #{status}
		where
		mc04_cgt_dist_parm_apply_status = '50'
		and mc04_cgt_dist_parm_apply_code in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</update>

	<update id="updateDistOrderQty" >
		update mc04_islm_cgt_dist_order a,
		(
		select
		d.itfpk,
		b.product_code,
		round(b.reqconfirmnum/5,6) qty_com,
		round(coalesce(b.confirmnum,b.reqconfirmnum)/5,6) qty_cfm
		from
		nation_dist d,
		nation_dist_item b
		where
		coalesce(d.req_confirm_status,d.sup_confirm_status,'0') = '1'
		and b.pk = d.pk
		and d.itfpk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
		) b
		set
		a.md02_cgt_dist_reqconfirm_qty = b.qty_com,
		a.md02_cgt_dist_confirm_qty = b.qty_cfm
		where
		a.mc04_cgt_dist_order_code = b.itfpk
		and a.ac_cgt_carton_code = b.product_code
		and a.mc04_cgt_dist_order_status = '50'
	</update>

	<select id="getConfirmDistOrders" resultType="string">
		select
		distinct mc04_cgt_dist_order_code
		from
		mc04_islm_cgt_dist_order a,
		nation_dist b
		where
		a.mc04_cgt_dist_order_code = b.itfpk
		and a.mc04_cgt_dist_order_status = '50'
		and b.effect_status = '1'
		and b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<select id="getRejectDistOrders" resultType="string">
		select
		distinct mc04_cgt_dist_order_code
		from
		mc04_islm_cgt_dist_order a,
		nation_dist b
		where
		a.mc04_cgt_dist_order_code = b.itfpk
		and a.mc04_cgt_dist_order_status = '50'
		and b.effect_status = '0'
		and b.req_confirm_status = '1'
		and b.sup_confirm_status = '0'
		and b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<select id="getCancelDistOrders" resultType="string">
		select
		distinct mc04_cgt_dist_order_code
		from
		mc04_islm_cgt_dist_order a,
		nation_dist b
		where
		a.mc04_cgt_dist_order_code = b.itfpk
		and a.mc04_cgt_dist_order_status = '50'
		and b.effect_status > '1'
		and b.pk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d.PK}
		</foreach>
	</select>

	<update id="updateDistOrderStatus" >
		update mc04_islm_cgt_dist_order
		set mc04_cgt_dist_order_status = #{status}
		where
		mc04_cgt_dist_order_status = '50'
		and mc04_cgt_dist_order_code in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</update>

	<update id="updateIsmItemDayPlanEffect" >
		update mc04_islmc_cgt_day_plan
		set md02_toba_prod_trade_order_effect_status_code = '1'
		where
		mc04_cgt_dist_order_code in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
		and md02_toba_prod_trade_order_effect_status_code = '0'
	</update>

	<update id="removeIsmItemDayPlans" >
		delete from  mc04_islmc_cgt_day_plan
		where
		mc04_cgt_day_plan_status = '0'
		and mc04_cgt_dist_order_code in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</update>

	<insert id="insertIsmDistLog" >
		insert into mc04_islmc_cgt_dist_order_log
		(
		mc04_cgt_dist_order_log_id,
		icom_code,
		mc04_cgt_dist_order_code,
		mc04_cgt_dist_order_current_status,
		mc04_cgt_dist_order_next_status,
		create_id,
		create_name,
		create_time,
		mc04_cgt_dist_order_client
		)
		select
		uuid() mc04_cgt_dist_order_log_id,
		b.supmember_code,
		b.itfpk mc04_cgt_dist_order_code,
		'50' mc04_cgt_dist_order_current_status,
		'${status}' mc04_cgt_dist_order_next_status,
		'nation' create_id,
		'定时器' create_name,
		date_format(now(), '%Y%m%d%H%i%s') as create_time,
		'pc' mc04_cgt_dist_order_client
		from
		nation_dist b
		where b.itfpk in
		<foreach collection="datas" item="d" open="(" close=")" separator=",">
			#{d}
		</foreach>
	</insert>

	<select id="getParm" resultType="map">
		select
			aa.pk as PK,
		    coalesce(aa.itfpk,concat(a.ma02_cgt_trade_supp_memb_code,'_',a.ma02_cgt_trade_req_memb_code,'_',coalesce(a.md02_cgt_dist_region_code,''),'_',coalesce(a.md02_dist_receiveregion_code,''))) as ITFPK,
			'INSERT' as ACTION,
			aa.version as VERSION,
		    a.ma02_cgt_trade_supp_memb_code as SUPMEMBER_CODE,
		    a.md02_cgt_trade_supp_memb_name as SUPMEMBER_NAME,
		    a.ma02_cgt_trade_req_memb_code as REQMEMBER_CODE,
		    a.md02_cgt_trade_req_memb_name as REQMEMBER_NAME,
		    coalesce(a.md02_cgt_dist_region_code ,'') as DISTREGION_CODE,
		    a.md02_cgt_dist_region_name as DISTREGION_NAME,
		    coalesce(a.md02_dist_receiveregion_code ,'') as MD02_DIST_RECEIVEREGION_CODE,
		    a.md02_dist_receiveregion_name as MD02_DIST_RECEIVEREGION_NAME,
		    a.md02_cgt_dist_prep_days as PREP_DAYS,
		    a.md02_cgt_dist_on_way_days as ONWAY_DAYS,
		    a.md02_cgt_dist_daily_sale_days as DAILYSALE_DAYS,
			a.md02_cgt_dist_dayily_sale_model_code as DAYILYSALE_MODEL,
		    a.create_name as SUP_CONFIRMER_NAME,
		    date_format(a.create_time,'%Y-%m-%d %H:%i:%S') as SUP_CONFIRM_TIME,
		    a.za_remark as REMARK
		from mc04_islmc_dist_parm_apply a
		left join nation_parm aa on
            a.ma02_cgt_trade_supp_memb_code = aa.supmember_code
            and a.ma02_cgt_trade_req_memb_code = aa.reqmember_code
            and coalesce(a.md02_cgt_dist_region_code ,'') = coalesce(aa.distregion_code ,'')
            and coalesce(a.md02_dist_receiveregion_code ,'') = coalesce(aa.md02_dist_receiveregion_code ,'')
		where a.mc04_cgt_dist_parm_apply_code = #{mc04CgtDistParmApplyCode}
	</select>

	<select id="getParmItems" resultType="map">
		select
			coalesce(aa.itfrow_pk,coalesce(aa.itfpk,concat(a.itfpk,'_',a.product_code))) as ITFROW_PK,
			coalesce(aa.itfpk,a.itfpk) as ITFPK,
			aa.ROW_PK as ROW_PK,
		    aa.PK as PK,
			e.PK_TRADEPRODUCT,
			a.product_code as PRODUCT_CODE,
		    a.product_name as PRODUCT_NAME,
			a.dist_model as DIST_MODEL,
		    a.calc_model as CALC_MODEL,
		    a.dailysale_days as DAILYSALE_DAYS,
			a.dayilysale_model as DAYILYSALE_MODEL,
		    a.maxstocksale_days as MAXSTOCKSALE_DAYS,
		    a.minstocksale_days as MINSTOCKSALE_DAYS,
		    a.maxstocknum as MAXSTOCKNUM,
		    a.minstocknum as MINSTOCKNUM,
			a.fullorderrate1 as FULLORDERRATE1,
			a.fullorderrate2 as FULLORDERRATE2,
			a.fullorderrate3 as FULLORDERRATE3,
			a.fullorderrate4 as FULLORDERRATE4,
			a.suplsafestocknum as SUPLSAFESTOCKNUM,
			a.ordersafestocknum as ORDERSAFESTOCKNUM,
			a.remark as REMARK
		from (
			select
				a.ma02_cgt_trade_supp_memb_code supmember_code,
				a.ma02_cgt_trade_req_memb_code reqmember_code,
				a.md02_cgt_dist_region_code distregion_code,
				a.md02_dist_receiveregion_code,
				concat(a.ma02_cgt_trade_supp_memb_code,'_',a.ma02_cgt_trade_req_memb_code,'_',coalesce(a.md02_cgt_dist_region_code,''),'_',coalesce(a.md02_dist_receiveregion_code,'')) as itfpk,
				b.ac_cgt_carton_code product_code ,
				b.ac_cgt_name product_name ,
				b.md02_cgt_dist_dist_model dist_model ,
				b.md02_cgt_dist_calc_model_code calc_model ,
				b.md02_cgt_dist_daily_sale_days dailysale_days ,
				b.md02_cgt_dist_dayily_sale_model_code dayilysale_model ,
				b.md02_cgt_upper_limit_stocksale_max maxstocksale_days,
				b.md02_cgt_lower_limit_stocksale_max minstocksale_days,
				b.md05_mg_cgt_upper_limit_stock_qty maxstocknum ,
				b.md05_mg_cgt_lower_limit_stock_qty minstocknum ,
				b.md02_cgt_dist_full_order_rate fullorderrate1,
                b.md02_cgt_dist_full_order_rate2 fullorderrate2,
                b.md02_cgt_dist_full_order_rate3 fullorderrate3,
                b.md02_cgt_dist_full_order_rate4 fullorderrate4,
				b.mc04_cgt_dist_strategy_com_safe_stk_qty suplsafestocknum ,
				b.md02_cgt_dist_order_dist_safe_stk_qty ordersafestocknum ,
				b.za_remark remark
			from
				mc04_islmc_dist_parm_apply a,
				mc04_islmc_dist_parm_apply_item b
			where
				a.mc04_cgt_dist_parm_apply_id= b.mc04_cgt_dist_parm_apply_id
				and a.mc04_cgt_dist_parm_apply_code = #{mc04CgtDistParmApplyCode}
			) a
		left join (
			select b.pk,b.itfpk,a.row_pk,a.itfrow_pk,a.product_code,
			    b.version,b.supmember_code,b.reqmember_code,b.distregion_code,b.md02_dist_receiveregion_code
			from nation_parm b left join nation_parm_item a on a.pk = b.pk
			where
			    b.supmember_code = #{supmemberCode}
			    and b.reqmember_code = #{reqmemberCode}
			    and coalesce(b.distregion_code ,'') = coalesce(#{distregionCode} ,'')
			    and coalesce(b.md02_dist_receiveregion_code ,'') = coalesce(#{md02DistReceiveregionCode} ,'')
		) aa on a.supmember_code = aa.supmember_code
            and a.reqmember_code = aa.reqmember_code
            and coalesce(a.distregion_code ,'') = coalesce(aa.distregion_code ,'')
            and coalesce(a.md02_dist_receiveregion_code ,'') = coalesce(aa.md02_dist_receiveregion_code ,'')
            and a.product_code = aa.product_code
		left join nation_product e on a.product_code = e.cigarette_code
		    and e.org_code = #{supmemberCode}
	</select>

	<select id="getParmSeasons" resultType="map">
		select
		    coalesce(aa.itfrow_pk,concat(a.itfpk,'_',a.md02_ic_trade_busi_start_date)) as ITFROW_PK,
			coalesce(aa.itfpk,a.itfpk) as ITFPK,
			aa.ROW_PK as ROW_PK,
		    aa.PK as PK,
			a.md02_ic_trade_busi_start_date as MD02_IC_TRADE_BUSI_START_DATE,
			a.md02_ic_trade_busi_end_date as MD02_IC_TRADE_BUSI_END_DATE,
			a.md02_cgt_dist_season_factor as MD02_CGT_DIST_SEASON_FACTOR,
			a.remark as REMARK
		from (
			select
				a.ma02_cgt_trade_supp_memb_code supmember_code,
				a.ma02_cgt_trade_req_memb_code reqmember_code,
				a.md02_cgt_dist_region_code distregion_code,
				a.md02_dist_receiveregion_code,
				concat(a.ma02_cgt_trade_supp_memb_code,'_',a.ma02_cgt_trade_req_memb_code,'_',coalesce(a.md02_cgt_dist_region_code,''),'_',coalesce(a.md02_dist_receiveregion_code,'')) as itfpk,
				b.md02_ic_trade_busi_start_date,
				b.md02_ic_trade_busi_end_date,
				b.md02_cgt_dist_season_factor,
				b.za_remark remark
			from
				mc04_islmc_dist_parm_apply a,
				mc04_islmc_dist_parm_apply_season b
			where
				a.mc04_cgt_dist_parm_apply_id= b.mc04_cgt_dist_parm_apply_id
				and a.mc04_cgt_dist_parm_apply_code = #{mc04CgtDistParmApplyCode}
			) a
		left join (
			select b.pk,b.itfpk,a.row_pk,a.itfrow_pk,a.md02_ic_trade_busi_start_date,
			    b.version,b.supmember_code,b.reqmember_code,b.distregion_code,b.md02_dist_receiveregion_code
			from nation_parm b left join nation_parm_season a on a.pk = b.pk
			where
			    b.supmember_code = #{supmemberCode}
			    and b.reqmember_code = #{reqmemberCode}
			    and coalesce(b.distregion_code ,'') = coalesce(#{distregionCode} ,'')
			    and coalesce(b.md02_dist_receiveregion_code ,'') = coalesce(#{md02DistReceiveregionCode} ,'')
		) aa on a.supmember_code = aa.supmember_code
            and a.reqmember_code = aa.reqmember_code
            and coalesce(a.distregion_code ,'') = coalesce(aa.distregion_code ,'')
            and coalesce(a.md02_dist_receiveregion_code ,'') = coalesce(aa.md02_dist_receiveregion_code ,'')
            and a.md02_ic_trade_busi_start_date = aa.md02_ic_trade_busi_start_date
	</select>

	<select id="getDeleteDist" resultType="map">
		select
            itfpk as ITFPK,
            pk  as PK
		from nation_dist
		where itfpk = #{mc04CgtDistOrderCode}
		and effect_status &lt;= '1'
	</select>

	<select id="getAddDist" resultType="map">
		select
			mc04_cgt_dist_order_code as ITFPK,
			'INSERT' as ACTION,
			a.icom_code as SUPMEMBER_CODE,
			c.member_name as SUPMEMBER_NAME,
			a.ba_com_org_code as REQMEMBER_CODE,
			b.member_name as REQMEMBER_NAME,
			a.md02_cgt_dist_region_code as DISTREGION_CODE,
			d.region_name as DISTREGION_NAME,
			a.md02_dist_receiveregion_code as MD02_DIST_RECEIVEREGION_CODE,
		    e.md02_dist_receiveregion_name  as MD02_DIST_RECEIVEREGION_NAME,
			date_format(date(mc04_cgt_dist_order_dist_date), '%Y-%m-%d') as BILL_DATE,
			a.za_remark as REMARK
		from (
            select
            distinct mc04_cgt_dist_order_code,icom_code,ba_com_org_code,md02_cgt_dist_region_code,md02_dist_receiveregion_code,za_remark, mc04_cgt_dist_order_dist_date
            from mc04_islm_cgt_dist_order a
            where a.mc04_cgt_dist_order_code = #{mc04CgtDistOrderCode}
		) a
		left join nation_member b on a.ba_com_org_code = b.member_code
		left join nation_member c on a.icom_code = c.member_code
		left join nation_dist_region d on a.icom_code = d.member_code and a.md02_cgt_dist_region_code = d.region_code
		left join nation_dist_receive_region e on a.ba_com_org_code = e.ma02_cgt_trade_req_memb_code and a.md02_dist_receiveregion_code = e.md02_dist_receiveregion_code
	</select>

	<select id="getUpdateDist" resultType="map">
		select
			mc04_cgt_dist_order_code as ITFPK,
			aa.pk as PK,
			'UPDATE' as ACTION,
			aa.version as VERSION,
			a.icom_code as SUPMEMBER_CODE,
			c.member_name as SUPMEMBER_NAME,
			a.ba_com_org_code as REQMEMBER_CODE,
			b.member_name as REQMEMBER_NAME,
			a.md02_cgt_dist_region_code as DISTREGION_CODE,
			d.region_name as DISTREGION_NAME,
			a.md02_dist_receiveregion_code as MD02_DIST_RECEIVEREGION_CODE,
		    e.md02_dist_receiveregion_name  as MD02_DIST_RECEIVEREGION_NAME,
			coalesce(a.create_name, '') as SUP_CONFIRMER_NAME,
			coalesce(aa.sup_confirm_time, date_format(a.create_time, '%Y-%m-%d %H:%i:%S')) as SUP_CONFIRM_TIME,
			'' as REQ_CONFIRMER_NAME,
			aa.req_confirm_time as REQ_CONFIRM_TIME,
			a.za_remark as REMARK
		from  (
			select mc04_cgt_dist_order_code,icom_code,ba_com_org_code,md02_cgt_dist_region_code,md02_dist_receiveregion_code, mc04_cgt_dist_order_dist_date,max(za_remark) za_remark,max(create_name) create_name,max(create_time) create_time
			from mc04_islm_cgt_dist_order a
			where a.mc04_cgt_dist_order_code = #{mc04CgtDistOrderCode}
			group by mc04_cgt_dist_order_code,icom_code,ba_com_org_code,md02_cgt_dist_region_code,md02_dist_receiveregion_code, mc04_cgt_dist_order_dist_date
		) a
		left join nation_dist aa on a.mc04_cgt_dist_order_code = aa.itfpk and aa.itfpk = #{mc04CgtDistOrderCode}
		left join nation_member b on a.ba_com_org_code = b.member_code
		left join nation_member c on a.icom_code = c.member_code
		left join nation_dist_region d on a.icom_code = d.member_code and a.md02_cgt_dist_region_code = d.region_code
		left join nation_dist_receive_region e on a.ba_com_org_code = e.ma02_cgt_trade_req_memb_code and a.md02_dist_receiveregion_code = e.md02_dist_receiveregion_code
	</select>

	<select id="getDistItems" resultType="map">
        select
			aa.row_pk as ROW_PK,
			aa.pk as PK,
			a.mc04_cgt_dist_order_code as ITFPK,
			coalesce(aa.itfrow_pk, concat(a.mc04_cgt_dist_order_code,a.ac_cgt_carton_code)) as ITFROW_PK,
			aa.version as VERSION,
			e.pk_tradeproduct as PK_TRADEPRODUCT,
			a.ac_cgt_carton_code as PRODUCT_CODE,
			e.cigarette_name as PRODUCT_NAME,
			a.dist_model as DIST_MODEL,
			a.calc_model as CALC_MODEL,
			IF(c.mc04_cgt_dist_order_type = '40', '4', a.dist_model) as DIST_MODEL,
			IF(c.mc04_cgt_dist_order_type = '40', '5', a.calc_model) as CALC_MODEL,
			coalesce(c.md02_cgt_dist_confirm_qty, c.mc04_cgt_dist_order_sale_area_req_qty, 0) as SUPCOMFIRMNUM,
			c.za_remark REMARK,
			c.md02_cgt_dist_indu_abnormal_remark as MD02_CGT_DIST_INDU_ABNORMAL_REMARK,
			aa.md02_cgt_dist_busi_abnormal_remark as MD02_CGT_DIST_BUSI_ABNORMAL_REMARK
		from
		(
			select a.mc04_cgt_dist_order_code,a.icom_code,a.md02_cgt_dist_region_code,a.ba_com_org_code,a.md02_dist_receiveregion_code,
			    c.product_code ac_cgt_carton_code,c.dist_model,c.calc_model
			from
				(
				select distinct a.supmember_code,a.reqmember_code,a.distregion_code,a.md02_dist_receiveregion_code,
				    b.product_code,b.dist_model,b.calc_model
				from nation_parm a,nation_parm_item b
				where a.pk = b.pk
				) c,
				(
				select distinct mc04_cgt_dist_order_code,md02_cgt_dist_region_code,md02_dist_receiveregion_code,ba_com_org_code,icom_code
				from mc04_islm_cgt_dist_order a
				where a.mc04_cgt_dist_order_code = #{mc04CgtDistOrderCode}
				) a
			where
                a.ba_com_org_code = c.reqmember_code
                and a.icom_code = c.supmember_code
                and coalesce(a.md02_cgt_dist_region_code,'') = coalesce(c.distregion_code,'')
                and coalesce(a.md02_dist_receiveregion_code,'') = coalesce(c.md02_dist_receiveregion_code,'')
		) a
		left join mc04_islm_cgt_dist_order c on a.ba_com_org_code = c.ba_com_org_code and a.icom_code = c.icom_code
		and a.ac_cgt_carton_code = c.ac_cgt_carton_code and a.mc04_cgt_dist_order_code = c.mc04_cgt_dist_order_code
		and c.mc04_cgt_dist_order_code = #{mc04CgtDistOrderCode}
		left join (
            select bb.version,bb.pk,aa.row_pk,aa.itfpk,aa.itfrow_pk,aa.product_code,aa.md02_cgt_dist_busi_abnormal_remark
            from nation_dist bb left join nation_dist_item aa on aa.pk = bb.pk where  bb.itfpk = #{mc04CgtDistOrderCode}
		) aa
		on a.mc04_cgt_dist_order_code = aa.itfpk and a.ac_cgt_carton_code = aa.product_code
		left join nation_product e on a.ac_cgt_carton_code = e.cigarette_code  and a.icom_code = e.org_code
	</select>



</mapper>