/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 发货仓库规格托盘
 *
 * @Author: wmd
 * @Since: 2025-08-04
 * @Email: <EMAIL>
 * @Create: 2025-08-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_islmc_cont_deliv_whse_item_tray")
public class Mc04IslmcContDelivWhseItemTrayDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 卷烟发货仓库代码
             */

            @TableId(value = "md02_cgt_out_storehouse_code", type = IdType.NONE)
    @Field(value = "卷烟发货仓库代码", name = "卷烟发货仓库代码")
    private String md02CgtOutStorehouseCode;
                                /**
             * 卷烟代码（条）
             */


    @Field(value = "卷烟代码（条）", name = "卷烟代码（条）")
    private String acCgtCartonCode;
                                /**
             * 二级牌号编码
             */

    @Field(value = "二级牌号编码", name = "二级牌号编码")
    private String acTwoLevelCigCode;
                                /**
             * 托盘容量
             */

    @Field(value = "托盘容量", name = "托盘容量")
    private BigDecimal ma02LogtIcTrayPalletTransQty;
                                /**
             * 物流托盘联运托盘类型
             */

    @Field(value = "物流托盘联运托盘类型", name = "物流托盘联运托盘类型")
    private String md03LogtTrayCombTspTrayType;
                                                                                                    

}
